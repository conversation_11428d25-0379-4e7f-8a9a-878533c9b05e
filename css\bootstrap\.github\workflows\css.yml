name: CSS

on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

env:
  FORCE_COLOR: 2
  NODE: 20

permissions:
  contents: read

jobs:
  css:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "${{ env.NODE }}"
          cache: npm

      - name: Install npm dependencies
        run: npm ci

      - name: Build CSS
        run: npm run css

      - name: Run CSS tests
        run: npm run css-test
