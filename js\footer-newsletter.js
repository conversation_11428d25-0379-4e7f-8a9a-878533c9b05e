/**
 * Footer Newsletter Subscription
 */
jQuery(document).ready(function($) {
    // Handle newsletter form submission
    $('#footer-newsletter-form').on('submit', function(e) {
        e.preventDefault();
        
        var email = $('#footer-email-input').val();
        var submitBtn = $('#footer-subscribe-btn');
        var responseDiv = $('#newsletter-response');
        
        // Basic validation
        if (!email || !isValidEmail(email)) {
            responseDiv.html('<div class="alert alert-danger">Please enter a valid email address.</div>');
            return false;
        }
        
        // Disable button and show loading state
        submitBtn.prop('disabled', true).html('Subscribing...');
        
        // Send AJAX request
        $.ajax({
            url: tendeal_newsletter.ajax_url,
            type: 'POST',
            data: {
                action: 'tendeal_newsletter_subscribe',
                email: email,
                nonce: tendeal_newsletter.nonce
            },
            success: function(response) {
                if (response.success) {
                    responseDiv.html('<div class="alert alert-success">' + response.data.message + '</div>');
                    $('#footer-email-input').val(''); // Clear the input
                } else {
                    responseDiv.html('<div class="alert alert-danger">' + response.data.message + '</div>');
                }
            },
            error: function() {
                responseDiv.html('<div class="alert alert-danger">An error occurred. Please try again later.</div>');
            },
            complete: function() {
                // Re-enable button
                submitBtn.prop('disabled', false).html('Subscribe');
                
                // Hide the message after 5 seconds
                setTimeout(function() {
                    responseDiv.html('');
                }, 5000);
            }
        });
    });
    
    // Helper function to validate email
    function isValidEmail(email) {
        var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        return regex.test(email);
    }
});
