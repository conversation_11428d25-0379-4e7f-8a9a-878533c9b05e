<?php
/**
 * Custom Login Form
 *
 * This template overrides the default WooCommerce login form.
 *
 * @package tendeal
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Ensure CSS is loaded
wp_enqueue_style('tendeal-account-pages', get_stylesheet_directory_uri() . '/css/account-pages.css');

get_header();

do_action('woocommerce_before_customer_login_form');
?>

<div class="account-page-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo esc_url(home_url('/')); ?>">Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Login</li>
        </ol>
    </nav>

    <h1 class="account-page-title">Log in to your account</h1>

    <form class="account-form woocommerce-form woocommerce-form-login login" method="post">
        <?php do_action('woocommerce_login_form_start'); ?>

        <div class="form-group">
            <label for="username"><?php esc_html_e('Email address', 'woocommerce'); ?></label>
            <input type="text" class="form-control" name="username" id="username" autocomplete="username" value="<?php echo (!empty($_POST['username'])) ? esc_attr(wp_unslash($_POST['username'])) : ''; ?>" placeholder="Email address" />
        </div>

        <div class="form-group">
            <label for="password"><?php esc_html_e('Password', 'woocommerce'); ?></label>
            <div class="password-field-container">
                <input class="form-control" type="password" name="password" id="password" autocomplete="current-password" placeholder="Password" />
                <button type="button" class="password-toggle" aria-label="Toggle password visibility">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye" viewBox="0 0 16 16">
                        <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"/>
                        <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="form-check">
            <input class="form-check-input" name="rememberme" type="checkbox" id="rememberme" value="forever">
            <label class="form-check-label" for="rememberme">
                <?php esc_html_e('Remember me', 'woocommerce'); ?>
            </label>
        </div>

        <a href="<?php echo esc_url(wp_lostpassword_url()); ?>" class="forgot-password"><?php esc_html_e('Forgot password?', 'woocommerce'); ?></a>

        <?php wp_nonce_field('woocommerce-login', 'woocommerce-login-nonce'); ?>
        <input type="hidden" name="redirect" value="<?php echo esc_url(wc_get_page_permalink('myaccount')); ?>" />

        <button type="submit" class="btn-submit" name="login" value="<?php esc_attr_e('Log in', 'woocommerce'); ?>"><?php esc_html_e('Login', 'woocommerce'); ?></button>

        <?php do_action('woocommerce_login_form'); ?>

        <div class="divider">or</div>

        <div class="social-login">
            <a href="#" class="social-btn google-login" aria-label="Login with Google">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"/>
                </svg>
            </a>
            <a href="#" class="social-btn facebook-login" aria-label="Login with Facebook">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path d="M9.198,21.5h4v-8.01h3.604l0.396-3.98h-4V7.5c0-1.034,0.252-1.99,1.98-1.99h2.022V2.14c-0.984-0.103-1.977-0.16-2.97-0.16c-3.02,0-5.032,1.84-5.032,5.04v2.49h-3.6v3.98h3.6V21.5z"/>
                </svg>
            </a>
        </div>

        <div class="account-switch">
            <?php esc_html_e("Don't have an account?", 'woocommerce'); ?>
            <a href="<?php echo esc_url(add_query_arg('action', 'register', wc_get_page_permalink('myaccount'))); ?>">
                <?php esc_html_e('Create new account', 'woocommerce'); ?>
            </a>
        </div>

        <?php do_action('woocommerce_login_form_end'); ?>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Password visibility toggle
    $('.password-toggle').on('click', function() {
        const passwordField = $(this).siblings('input');
        const icon = $(this).find('svg');

        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.html('<path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"/><path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"/><path d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12-.708.708z"/>');
        } else {
            passwordField.attr('type', 'password');
            icon.html('<path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"/><path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>');
        }
    });
});
</script>

<?php
do_action('woocommerce_after_customer_login_form');
get_footer();
?>
