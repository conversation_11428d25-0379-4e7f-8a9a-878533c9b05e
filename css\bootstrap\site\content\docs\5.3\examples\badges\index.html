---
layout: examples
title: Badges
extra_css:
  - "badges.css"
body_class: ""
---

<svg xmlns="http://www.w3.org/2000/svg" class="d-none">
  <symbol id="x-circle-fill" viewBox="0 0 16 16">
    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
  </symbol>
</svg>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge text-bg-primary rounded-pill">Primary</span>
  <span class="badge text-bg-secondary rounded-pill">Secondary</span>
  <span class="badge text-bg-success rounded-pill">Success</span>
  <span class="badge text-bg-danger rounded-pill">Danger</span>
  <span class="badge text-bg-warning rounded-pill">Warning</span>
  <span class="badge text-bg-info rounded-pill">Info</span>
  <span class="badge text-bg-light rounded-pill">Light</span>
  <span class="badge text-bg-dark rounded-pill">Dark</span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge bg-primary-subtle text-primary-emphasis rounded-pill">Primary</span>
  <span class="badge bg-secondary-subtle text-secondary-emphasis rounded-pill">Secondary</span>
  <span class="badge bg-success-subtle text-success-emphasis rounded-pill">Success</span>
  <span class="badge bg-danger-subtle text-danger-emphasis rounded-pill">Danger</span>
  <span class="badge bg-warning-subtle text-warning-emphasis rounded-pill">Warning</span>
  <span class="badge bg-info-subtle text-info-emphasis rounded-pill">Info</span>
  <span class="badge bg-light-subtle text-light-emphasis rounded-pill">Light</span>
  <span class="badge bg-dark-subtle text-dark-emphasis rounded-pill">Dark</span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge bg-primary-subtle border border-primary-subtle text-primary-emphasis rounded-pill">Primary</span>
  <span class="badge bg-secondary-subtle border border-secondary-subtle text-secondary-emphasis rounded-pill">Secondary</span>
  <span class="badge bg-success-subtle border border-success-subtle text-success-emphasis rounded-pill">Success</span>
  <span class="badge bg-danger-subtle border border-danger-subtle text-danger-emphasis rounded-pill">Danger</span>
  <span class="badge bg-warning-subtle border border-warning-subtle text-warning-emphasis rounded-pill">Warning</span>
  <span class="badge bg-info-subtle border border-info-subtle text-info-emphasis rounded-pill">Info</span>
  <span class="badge bg-light-subtle border border-light-subtle text-light-emphasis rounded-pill">Light</span>
  <span class="badge bg-dark-subtle border border-dark-subtle text-dark-emphasis rounded-pill">Dark</span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge d-flex align-items-center p-1 pe-2 text-primary-emphasis bg-primary-subtle border border-primary-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Primary
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-secondary-emphasis bg-secondary-subtle border border-secondary-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Secondary
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-success-emphasis bg-success-subtle border border-success-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Success
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-danger-emphasis bg-danger-subtle border border-danger-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Danger
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-warning-emphasis bg-warning-subtle border border-warning-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Warning
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-info-emphasis bg-info-subtle border border-info-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Info
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-dark-emphasis bg-light-subtle border border-dark-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Light
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-dark-emphasis bg-dark-subtle border border-dark-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Dark
  </span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge d-flex p-2 align-items-center text-bg-primary rounded-pill">
    <span class="px-1">Primary</span>
    <a href="#"><svg class="bi ms-1" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex p-2 align-items-center text-primary-emphasis bg-primary-subtle rounded-pill">
    <span class="px-1">Primary</span>
    <a href="#"><svg class="bi ms-1" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex p-2 align-items-center text-primary-emphasis bg-primary-subtle border border-primary-subtle rounded-pill">
    <span class="px-1">Primary</span>
    <a href="#"><svg class="bi ms-1" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge d-flex align-items-center p-1 pe-2 text-primary-emphasis bg-primary-subtle border border-primary-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Primary
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-secondary-emphasis bg-secondary-subtle border border-secondary-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Secondary
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-success-emphasis bg-success-subtle border border-success-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Success
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-danger-emphasis bg-danger-subtle border border-danger-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Danger
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-warning-emphasis bg-warning-subtle border border-warning-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Warning
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-info-emphasis bg-info-subtle border border-info-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Info
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-light-emphasis bg-light-subtle border border-dark-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Light
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-dark-emphasis bg-dark-subtle border border-dark-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Dark
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
</div>
