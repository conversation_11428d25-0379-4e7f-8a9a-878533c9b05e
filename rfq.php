<?php
/**
 * Template Name: Request for Quotation
 *
 * This is a custom page template for the Request for Quotation (RFQ) page.
 */

get_header(); // Include the header.php file of your theme
?>


<div class="entry-content  bg-white">
  <div class="container mx-auto p-4 md:p-8">
    <h1 class="text-2xl md:text-4xl font-semibold text-gray-800 mb-6 text-center"> Request for Quotation</h1>

    <div class="flex justify-center mb-8">
      <div class="flex items-center space-x-2 md:space-x-4">
        <div
          class="step-circle w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center text-sm md:text-base font-semibold active-step"
          data-step="1">
          <i class="bi bi-currency-dollar"></i>
        </div>
        <div class="step-line h-1 md:h-2 bg-gray-300 w-16 md:w-24"></div>
        <div
          class="step-circle w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center text-sm md:text-base font-semibold"
          data-step="2">
          <i class="bi bi-box-seam "></i>
        </div>
        <div class="step-line h-1 md:h-2 bg-gray-300 w-16 md:w-24"></div>
        <div
          class="step-circle w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center text-sm md:text-base font-semibold"
          data-step="3">
          <i class="bi bi-check"></i>
        </div>
      </div>
    </div>

    <div id="step-1-container" class="step-container">
      <div class="flex  md:flex-row justify-center gap-6 mb-8">
        <div class="product-selection w-full md:w-1/3 cursor-pointer" data-step="2" data-rfq-type="product">
          <div
            class="bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-center hover:shadow-lg transition-shadow duration-200 border border-gray-200">
            <!-- <i class="fas fa-box-open text-4xl text-amber-500 mb-2"></i> -->
            <i class="bi bi-box-seam text-4xl text-amber-500 mb-2"></i>
            <p class="text-gray-700 font-medium text-center">Browse our products</p>
          </div>
        </div>
        <div class="seller-selection w-full md:w-1/3 cursor-pointer" data-step="2" data-rfq-type="seller">
          <div
            class="bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-center hover:shadow-lg transition-shadow duration-200 border border-gray-200">
            <!-- <i class="fas fa-store text-4xl text-amber-500 mb-2"></i> -->
            <i class="bi bi-shop-window text-4xl text-amber-500 mb-2"></i>
            <p class="text-gray-700 font-medium text-center">Browse our sellers</p>
          </div>
        </div>
        <div class="rfq-customization w-full md:w-1/3 cursor-pointer" data-step="3" data-rfq-type="custom">
          <div
            class="bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-center hover:shadow-lg transition-shadow duration-200 border border-gray-200">
            <!-- <i class="fas fa-file-Pen text-4xl text-amber-500 mb-2"></i> -->
            <i class="bi bi-currency-dollar text-4xl text-amber-500 mb-2"></i>
            <p class="text-gray-700 font-medium text-center">Customize your RFQ</p>
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-6">
        <button id="next-button-1"
          class="bg-amber-500 hover:bg-amber-600 text-white font-semibold py-2.5 px-5 rounded-btn transition-colors duration-200 text-lg">
          Next
        </button>
      </div>
    </div>

    <div id="step-2-container" class="step-container hidden bg-white rounded-lg shadow-md p-6 mb-8">
      <!-- <h2 class="text-xl font-semibold text-gray-800 mb-4">Select Products for RFQ</h2> -->

      <div class="row mb-4">
        <div class="col-6">
          <!-- < class="flex-grow min-w-[200px]"> -->
          <label for="product-category-filter" class="block text-gray-700 text-sm font-bold mb-2">Filter by
            Category:</label>
          <select id="product-category-filter"
            class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            <option value="">All Categories</option>
            <?php
                    $categories = get_terms(array(
                        'taxonomy' => 'product_cat',
                        'hide_empty' => true, // Set to false to show empty categories if needed
                    ));
                    if (!empty($categories) && !is_wp_error($categories)) {
                        foreach ($categories as $category) {
                            $subcategories = get_terms(array(
                                'taxonomy' => 'product_cat',
                                'parent'   => $category->term_id,
                                'hide_empty' => true,
                            ));
                            echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                            if ($subcategories) {
                                foreach ($subcategories as $subcategory) {
                                    echo '<option value="' . esc_attr($subcategory->term_id) . '">&nbsp;&nbsp;&nbsp;- ' . esc_html($subcategory->name) . '</option>';
                                }
                            }
                        }
                    }
                    ?>
          </select>
        </div>
        <div class="col-6">
          <label for="product-subcategory-filter" class="block text-gray-700 text-sm font-bold mb-2">Filter by
            Subcategory:</label>
          <select id="product-subcategory-filter"
            class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            <option value="">All Subcategories</option>
          </select>
        </div>
      </div>



      <!-- <div class="flex flex-wrap gap-4 mb-4">
        <div class="flex-grow min-w-[200px]">
          <label for="product-category-filter" class="block text-gray-700 text-sm font-bold mb-2">Filter by
            Category:</label>
          <select id="product-category-filter"
            class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            <option value="">All Categories</option>
            <?php
                    $categories = get_terms(array(
                        'taxonomy' => 'product_cat',
                        'hide_empty' => true, // Set to false to show empty categories if needed
                    ));
                    if (!empty($categories) && !is_wp_error($categories)) {
                        foreach ($categories as $category) {
                            $subcategories = get_terms(array(
                                'taxonomy' => 'product_cat',
                                'parent'   => $category->term_id,
                                'hide_empty' => true,
                            ));
                            echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                            if ($subcategories) {
                                foreach ($subcategories as $subcategory) {
                                    echo '<option value="' . esc_attr($subcategory->term_id) . '">&nbsp;&nbsp;&nbsp;- ' . esc_html($subcategory->name) . '</option>';
                                }
                            }
                        }
                    }
                    ?>
          </select>
        </div>
        <div class="flex-grow min-w-[200px]">
          <label for="product-subcategory-filter" class="block text-gray-700 text-sm font-bold mb-2">Filter by
            Subcategory:</label>
          <select id="product-subcategory-filter"
            class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            <option value="">All Subcategories</option>
          </select>
        </div>
      </div> -->


      <div id="product-list" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <?php
            $products = wc_get_products(array('limit' => -1)); // Get all products
            if (!empty($products)) {
                foreach ($products as $product) {
                    $product_id = $product->get_id();
                    $product_name = $product->get_name();
                    $product_price = $product->get_price();
                    $product_image_url = wp_get_attachment_url($product->get_image_id());

                    echo '<div class="bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-between border border-gray-200">';
                    echo '<img src="' . esc_url($product_image_url) . '" alt="' . esc_attr($product_name) . '" class="w-full h-32 object-contain mb-4">';
                    echo '<h3 class="text-md font-semibold text-gray-800 mb-2 text-center">' . esc_html($product_name) . '</h3>';
                    echo '<p class="text-gray-600 text-sm mb-2 text-center">Price: $' . esc_html($product_price) . '</p>';
                    echo '<button class="add-to-rfq bg-amber-500 hover:bg-amber-600 text-white font-semibold py-2 px-4 rounded-btn transition-colors duration-200 text-sm" data-product-id="' . esc_attr($product_id) . '">Add to RFQ</button>';
                    echo '</div>';
                }
            } else {
                echo '<p class="text-red-500">No products found in WooCommerce.</p>';
            }
            ?>
      </div>

      <div id="seller-list" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 hidden">
        <?php
            // Get all users with the 'seller' role.
            $sellers = get_users(array('role__in' => array('wcfm_vendor', 'seller', 'vendor')));
            if (!empty($sellers)) {
                foreach ($sellers as $seller) {
                    $seller_id = $seller->ID;
                    $seller_name = $seller->display_name;
                    $seller_email = $seller->user_email;
                    // You can add more seller information here, like a seller rating or store description.
                    // echo '<div class="seller-item bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-between border border-gray-200">'  . esc_html($seller) . '</div>';
                   
                    echo '<div class="seller-item bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-between border border-gray-200">';
                    echo '<h3 class="text-md font-semibold text-gray-800 mb-2 text-center">' . esc_html($seller_name) . '</h3>';
                    echo '<p class="text-gray-600 text-sm mb-2 text-center">Email: ' . esc_html($seller_email) . '</p>';
                    // echo '<p class="text-gray-600 text-sm mb-2 text-center">Rating: 4.5/5</p>'; // Example of additional info
                    echo '<button class="select-seller bg-amber-500 hover:bg-amber-600 text-white font-semibold py-2 px-4 rounded-full transition-colors duration-200 text-sm" data-seller-id="' . esc_attr($seller_id) . '">Select Seller</button>';
                    echo '</div>';
                }
            } else {
                echo '<p class="text-red-500">No sellers found.</p>';
            }
            ?>
      </div>

      <div id="seller-products" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 col-span-full text-center">Products from Selected Seller
        </h3>
        <?php
            if (!empty($products)) {
                foreach ($products as $product) {
                    $product_id = $product->get_id();
                    $product_name = $product->get_name();
                    $product_price = $product->get_price();
                    $product_image_url = wp_get_attachment_url($product->get_image_id());
                    $product_categories = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));

                    echo '<div class="product-item bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-between border border-gray-200" data-category-ids="' . implode(',', $product_categories) . '" data-product-id="' . esc_attr($product_id) . '">';
                    echo '<img src="' . esc_url($product_image_url) . '" alt="' . esc_attr($product_name) . '" class="w-full h-32 object-contain mb-4">';
                    echo '<h3 class="text-md font-semibold text-gray-800 mb-2 text-center">' . esc_html($product_name) . '</h3>';
                    echo '<p class="text-gray-600 text-sm mb-2 text-center">Price: $' . esc_html($product_price) . '</p>';
                    echo '<button class="add-to-rfq bg-amber-500 hover:bg-amber-600 text-white font-semibold py-2 px-4 rounded-full transition-colors duration-200 text-sm" data-product-id="' . esc_attr($product_id) . '">Add to RFQ</button>';
                    echo '</div>';
                }
            } else {
                echo '<p class="text-red-500">No products found from this seller.</p>';
            }
            ?>
      </div>
      <div class="flex justify-center mt-6 gap-4">
        <button id="back-to-1"
          class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-2.5 px-5 rounded-btn transition-colors duration-200 text-lg mr-4">
          Back
        </button>
        <button id="next-button-2"
          class="bg-amber-500 hover:bg-amber-600 text-white font-semibold py-2.5 px-5 rounded-btn transition-colors duration-200 text-lg">
          Next
        </button>
      </div>
    </div>

    <div id="step-3-container" class="step-container hidden bg-white  shadow-md p-6 mb-8">
      <!-- <h2 class="text-xl font-semibold text-gray-800 mb-4">Customize Your RFQ</h2> -->
      <form id="rfq-form" class="space-y-4">
        <?php
            if (!empty($selected_products)) {
                foreach ($selected_products as $product) {
                    $product_id = $product['id'];
                    $product_name = $product['name'];
                    $product_price = $product['price'];
                    echo '<div class="bg-white rounded-lg shadow-md p-4 mb-4">';
                    echo '<h3 class="text-lg font-semibold text-gray-800 mb-2">' . esc_html($product_name) . ' - Price: $' . esc_html($product_price) . '</h3>';
                    echo '<div class="form-group flex flex-col sm:flex-row items-start sm:items-center gap-4">';
                    echo '<label for="quantity-' . esc_attr($product_id) . '" class="block text-gray-700 text-sm font-bold mb-2 sm:mb-0 min-w-[100px]">Quantity:</label>';
                    echo '<input type="number" id="quantity-' . esc_attr($product_id) . '" name="quantity[' . esc_attr($product_id) . ']" value="1" min="1" required class="shadow appearance-none border rounded w-full sm:w-auto py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">';
                    echo '</div>';
                    echo '<div class="form-group flex flex-col sm:flex-row items-start sm:items-start  mt-4">';
                    echo '<label for="description-' . esc_attr($product_id) . '" class="block text-gray-700 text-sm font-bold mb-2 sm:mb-0 min-w-[100px]">Description:</label>';
                    echo '<textarea id="description-' . esc_attr($product_id) . '" name="description[' . esc_attr($product_id) . ']" rows="2" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>';
                    echo '</div>';
                    echo '<input type="hidden" name="product_id[]" value="' . esc_attr($product_id) . '">';
                    echo '</div>';
                }
            } else {
                echo '<p class="text-red-500">No products selected. Please select products to request a quotation.</p>';
            }
            ?>
        <!-- <div class="form-group mt-6">
          <label for="notes" class="block text-gray-700 text-sm font-bold mb-2">Additional
            Notes:</label>
          <textarea id="notes" name="notes" rows="4"
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
        </div> -->
        <div class="flex justify-center mt-6 gap-4">
          <button id="back-to-2"
            class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-2.5 px-5 rounded-btn transition-colors duration-200 text-lg mr-4">
            Back
          </button>
          <button type="submit"
            class="bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-btn transition-colors duration-200 text-lg">
            Submit RFQ
          </button>
        </div>
      </form>


      <form id="rfq-custom-form" class="space-y-4">

        <div class="mb-4">
          <label for="product-name" class="block text-gray-700 text-sm font-bold mb-2">Product name</label>
          <input type="text" id="product-name" name="product-name"
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>

        <div class="mb-4">
          <label for="product-details" class="block text-gray-700 text-sm font-bold mb-2">Product details</label>
          <textarea id="product-details" name="product-details" rows="4"
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
        </div>

        <div class="mb-4">
          <label for="quantity" class="block text-gray-700 text-sm font-bold mb-2">Quantity</label>
          <input type="number" id="quantity" name="quantity" min="1" value="1"
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>

        <div class="mb-4">
          <label for="supplier" class="block text-gray-700 text-sm font-bold mb-2">Choose supplier</label>
          <select id="supplier" name="supplier"
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            <option value="">Choose supplier</option>
            <option value="supplier1">Supplier 1</option>
            <option value="supplier2">Supplier 2</option>
            <option value="supplier3">Supplier 3</option>
          </select>
        </div>

        <div class="mb-4">
          <label for="file-upload" class="block text-gray-700 text-sm font-bold mb-2">Upload File</label>
          <div
            class="file-upload-container border-2 border-dashed border-gray-300 rounded-md p-6 text-center cursor-pointer">
            <p class="text-gray-600">Drag and drop to upload the file</p>
            <p class="text-gray-600">Or</p>
            <button type="button"
              class="file-upload-button bg-white text-blue-500 border border-blue-500 rounded-md py-2 px-4 focus:outline-none focus:shadow-outline">Browse
              files</button>
            <p class="text-gray-500 text-xs mt-2">Files must be in PDF format</p>
            <input type="file" id="file-upload" name="file-upload" class="hidden" accept=".pdf">
          </div>
        </div>

        <div class="flex justify-between mt-8">
          <button type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">Back</button>
          <button type="submit"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">Validate</button>
        </div>
      </form>

    </div>
  </div>
</div>




<script>
jQuery(document).ready(function($) {
  const stepCircles = $('.step-circle');
  const stepLines = $('.step-line');
  const productSelection = $('.product-selection');
  const sellerSelection = $('.seller-selection');
  const rfqCustomization = $('.rfq-customization');
  const stepContainers = $('.step-container');
  const nextButtons = $('.next-button');
  const backButtons = $('.back-button');
  const rfqForm = $('#rfq-form');

  const nextButton = $('#next-button');


  const productListContainer = $('#product-list-container');
  const productList = $('#product-list');
  const rfqFormContainer = $('#rfq-form-container');

  const backToMainButton = $('#back-to-main');
  const nextButtonProducts = $('#next-button-products');
  const backToProductsButton = $('#back-to-products');



  const categoryFilter = $('#product-category-filter');
  const subcategoryFilter = $('#product-subcategory-filter');
  const sellerList = $('#seller-list');
  const sellerProductsList = $('#seller-products');
  const productFilterContainer = $('#productFilterContainer');
  const rfqCustomForm = $('#rfq-custom-form');


  let currentStep = 1;
  let selectedProducts = [];
  let selectedRfqType = '';
  let allProducts = []; // Store all products


  function updateStep(step) {
    if (step >= 1 && step <= 3) {
      currentStep = step;
      stepCircles.each(function(index) {
        if (index < step - 1) {
          $(this).removeClass('active-step').addClass('completed-step');
        } else if (index === step - 1) {
          $(this).removeClass('completed-step').addClass('active-step');
        } else {
          $(this).removeClass('active-step completed-step');
        }
      });
      stepLines.each(function(index) {
        if (index < step - 1) {
          $(this).addClass('bg-green-500');
        } else {
          $(this).removeClass('bg-green-500');
        }
      });

      // Show/hide sections based on the current step
      stepContainers.each(function(index) {
        $(this).addClass('hidden');
      });
      $('#step-' + step + '-container').removeClass('hidden');

      if (step === 2) {


        if (selectedRfqType === 'product') {
          productFilterContainer.removeClass('hidden');
          productList.removeClass('hidden');
          sellerList.addClass('hidden');
          sellerProductsList.addClass('hidden');
          filterProductsByCategory(); // Initial filtering
        } else if (selectedRfqType === 'seller') {
          productFilterContainer.addClass('hidden');
          productList.addClass('hidden');
          sellerList.removeClass('hidden');
          sellerProductsList.removeClass('hidden'); // Show seller products
          // Clear old seller products
          sellerProductsList.empty();
          // Get the selected seller ID.
          selectedSellerId = $('.seller-item.selected').data('seller-id');
          if (selectedSellerId) {
            // Display the products for the selected seller.
            displaySellerProducts(selectedSellerId);
          }

        }



        // console.log("first====================", step, allProducts.length)
        // productListContainer.removeClass('hidden');
        // rfqFormContainer.addClass('hidden');
        // nextButton.addClass('hidden');

        // if (allProducts.length === 0) { // only load products once
        //   // loadProducts();

        //   loadProductsFromWooCommerce();
        // }
      } else if (step === 3) {

        if ((selectedRfqType != 'custom')) {
          rfqCustomForm.empty();
          generateRFQForm();
        }
        console.log("================== ")

      }
    }
  }

  productSelection.on('click', function() {
    selectedRfqType = 'product';
    updateStep(2);
  });

  sellerSelection.on('click', function() {
    selectedRfqType = 'seller';
    updateStep(2);
  });

  rfqCustomization.on('click', function() {
    selectedRfqType = 'custom';
    updateStep(3);
  });

  nextButtons.on('click', function() {
    if (currentStep < 3) {
      updateStep(currentStep + 1);
    }
  });

  backButtons.on('click', function() {
    if (currentStep > 1) {
      updateStep(currentStep - 1);
    }
  });

  function loadProductsFromWooCommerce() {
    productList.empty(); // Clear any existing content
    $.ajax({
      url: '/wp-admin/admin-ajax.php',
      type: 'POST',
      data: {
        action: 'get_woocommerce_products',
      },
      success: function(response) {
        if (response.success) {
          allProducts = response.data;
          if (allProducts.length > 0) {
            displayProducts(allProducts);
          } else {
            productList.append('<p class="text-red-500">No products found in WooCommerce.</p>');
          }

        } else {
          console.error('Error fetching products:', response.message);
          productList.append('<p class="text-red-500">Failed to load products. Please try again.</p>');
        }
      },
      error: function(error) {
        console.error('AJAX error:', error);
        productList.append(
          '<p class="text-red-500">Failed to load products. Please check your connection.</p>');
      }
    });
  }

  function loadProducts() {
    // In a real scenario, you would use AJAX to fetch products from your WooCommerce store.
    // This is just a simulation with static data.
    allProducts = [{
        id: 1,
        name: 'aminamin 1',
        price: 19.99,
        image: 'https://via.placeholder.com/150'
      },
      {
        id: 2,
        name: 'Product 2',
        price: 29.99,
        image: 'https://via.placeholder.com/150'
      },
      {
        id: 3,
        name: 'Product 3',
        price: 39.99,
        image: 'https://via.placeholder.com/150'
      },
      {
        id: 4,
        name: 'Product 4',
        price: 49.99,
        image: 'https://via.placeholder.com/150'
      },
      {
        id: 5,
        name: 'Product 5',
        price: 59.99,
        image: 'https://via.placeholder.com/150'
      },
      {
        id: 6,
        name: 'Product 6',
        price: 69.99,
        image: 'https://via.placeholder.com/150'
      },
      {
        id: 7,
        name: 'Product 7',
        price: 79.99,
        image: 'https://via.placeholder.com/150'
      },
      {
        id: 8,
        name: 'Product 8',
        price: 89.99,
        image: 'https://via.placeholder.com/150'
      },
    ];

    productList.empty(); // Clear any existing content
    allProducts.forEach(product => {
      const productItem = $(`
                    <div class="bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-between border border-gray-200">
                        <img src="${product.image}" alt="${product.name}" class="w-full h-32 object-contain mb-4">
                        <h3 class="text-md font-semibold text-gray-800 mb-2 text-center">${product.name}</h3>
                        <p class="text-gray-600 text-sm mb-2 text-center">Price: $${product.price}</p>
                        <button class="add-to-rfq bg-amber-500 hover:bg-amber-600 text-white font-semibold py-2 px-4 rounded-full transition-colors duration-200 text-sm" data-product-id="${product.id}">
                            Add to RFQ
                        </button>
                    </div>
                `);
      productList.append(productItem);
    });

    // Attach event listener to the "Add to RFQ" buttons
    $('.add-to-rfq').on('click', function() {
      console.log("============================= add-to-rfq")
      const productId = $(this).data('product-id');
      const product = allProducts.find(p => p.id === productId);
      if (product && !selectedProducts.find(p => p.id === productId)) {
        selectedProducts.push(product);
        $(this).text('Added to RFQ').prop('disabled', true).removeClass('bg-amber-500 hover:bg-amber-600')
          .addClass('bg-green-500');
      }
    });
  }


  // Handle "Select Seller" button click
  sellerList.on('click', '.select-seller', function() {
    selectedSellerId = $(this).data('seller-id');
    console.log("============================== select saller", selectedSellerId)
    $('.seller-item').removeClass('selected');
    $(this).closest('.seller-item').addClass('selected');
    //updateStep(3);  //removed
    // Clear the seller products list before adding new products
    sellerProductsList.empty();
    displaySellerProducts(selectedSellerId);

  });



  function displaySellerProducts(sellerId) {
    jQuery.ajax({ // Use jQuery here to ensure it's defined
      url: '/wp-admin/admin-ajax.php',
      type: 'POST',
      data: {
        action: 'get_store_products', // Changed action name.
        seller_id: sellerId
      },
      success: function(response) {


        let productsHtml = response;
        sellerProductsList.html(productsHtml);

        productFilterContainer.addClass('hidden');
        productList.addClass('hidden');
        sellerList.addClass('hidden');




        if (response.success) {

          const products = response.data.products;
          console.log("================== products", products)
          let productsHtml = '';
          if (products && products.length > 0) {
            products.forEach(product => {
              productsHtml += `
                              <div class="product-item bg-white rounded-lg shadow-md p-4 flex flex-col items-center justify-between border border-gray-200" data-product-id="${product.id}">
                                  <img src="${product.image}" alt="${product.title}" class="w-full h-32 object-contain mb-4">
                                  <h3 class="text-md font-semibold text-gray-800 mb-2 text-center">${product.title}</h3>
                                  <p class="text-gray-600 text-sm mb-2 text-center">Price: $${product.price}</p>
                                  <button class="add-to-rfq bg-amber-500 hover:bg-amber-600 text-white font-semibold py-2 px-4 rounded-full transition-colors duration-200 text-sm" data-product-id="${product.id}">Add to RFQ</button>
                              </div>
                          `;
            });
            sellerProductsList.html(productsHtml);
            // Re-attach event listener to the "Add to RFQ" buttons inside seller products list.
            sellerProductsList.find('.add-to-rfq').on('click', function() {

              const productId = $(this).data('product-id');
              const productElement = $(this).closest('.product-item'); //find the product-item
              const productName = productElement.find('h3').text();
              const productPrice = productElement.find('p').text().replace('Price: $', '');
              const productImage = productElement.find('img').attr('src');;
              console.log("================================== sellerProductsList.html(productsHtml);",
                productElement)
              console.log("================================== sellerProductsList.html(productsHtml);",
                productImage)
              const product = {
                id: productId,
                name: productName,
                price: productPrice,
                image: productImage,
              };

              console.log("================== ", product)

              if (!selectedProducts.find(p => p.id === productId)) {
                selectedProducts.push(product);
                $(this).text('Added to RFQ').prop('disabled', true).removeClass(
                  'bg-amber-500 hover:bg-amber-600').addClass('bg-green-500');
              }
            });
          } else {
            sellerProductsList.html('<p class="text-red-500">No products found from this seller.</p>');
          }
        } else {
          console.error('Error fetching seller products:', response);
          sellerProductsList.html('<p class="text-red-500">Failed to load products.</p>');
        }
      },
      error: function(error) {
        console.error('AJAX error:', error);
        sellerProductsList.html('<p class="text-red-500">Error: ' + error.responseText + '</p>');
      }
    });
  }


  function generateRFQForm() {

    console.log("===================generateRFQForm", selectedProducts)
    rfqForm.empty(); // Clear any existing form fields
    if (selectedProducts.length === 0) {
      rfqForm.append(
        '<p class="text-red-500">No products selected. Please select products to request a quotation.</p>');
      return;
    }


    let formHtml = '';
    selectedProducts.forEach(product => {
      const formGroup = `
                    <div class="bg-white d-flex rounded-lg shadow-md p-4 mb-4">

                    <div class="product-thumbnail">
                      <a href="">
                       <img src="${product.image}" alt="" class="w-full h-32 object-contain mb-4">
                   
                      </a>
                    </div>

                    <div class="product-details p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">${product.name} - Price: $${product.price}</h3>
                      <div class="product-reviews">⭐⭐⭐⭐⭐ 4.8 (397 Reviews)</div>
                       
                      <div class="quantity-control mt-2 mb-2 gap-3">
                        <label for="quantity-${product.id}" class="block text-gray-700 text-sm font-bold mb-2 sm:mb-0 min-w-[100px]">Quantity:</label>
                        <button type="button" class="decrease-qty"><i class="bi bi-dash"></i></button>
                        <input type="number" name="quantity[${product.id}]" value="1" min="1" class="qty-input">
                        <button type="button" class="increase-qty"><i class="bi bi-plus"></i></button>
               
                      </div>
                       
                      <div class="form-group flex flex-col sm:flex-row items-start sm:items-start  mt-4">
                        <label for="description-${product.id}" class="block text-gray-700 text-sm font-bold mb-2 sm:mb-0 min-w-[100px]">Description:</label>
                        <textarea id="description-${product.id}" name="description[${product.id}]" rows="2" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
                      </div>
                      
                        <input type="hidden" name="product_id[]" value="${product.id}">
                    </div>
                        
                    </div>
                `;
      formHtml += formGroup;
    });


    // const notesGroup = `
    //             <div class="form-group mt-6">
    //                 <label for="notes" class="block text-gray-700 text-sm font-bold mb-2">Additional Notes:</label>
    //                 <textarea id="notes" name="notes" rows="4" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
    //             </div>
    //         `;
    // formHtml += notesGroup;

    const submitButtons = `
                <div class="flex justify-center mt-6 gap-4">
                    <button id="back-to-2" class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-semibold py-2.5 px-5 rounded-full transition-colors duration-200 text-lg mr-4">
                        Back
                    </button>
                    <button type="submit" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-full transition-colors duration-200 text-lg">
                        Submit RFQ
                    </button>
                </div>
            `;
    formHtml += submitButtons;

    rfqForm.html(formHtml);

    // selectedProducts.forEach(product => {
    //   const formGroup = $(`
    //                 <div class="form-group">
    //                     <label for="quantity-${product.id}" class="block text-gray-700 text-sm font-bold mb-2">Quantity for ${product.name} ($${product.price}):</label>
    //                     <input type="number" id="quantity-${product.id}" name="quantity[${product.id}]" value="1" min="1" required class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
    //                     <input type="hidden" name="product_id[]" value="${product.id}">
    //                 </div>
    //             `);
    //   rfqForm.append(formGroup);
    // });

    // const notesGroup = $(`
    //             <div class="form-group">
    //                 <label for="notes" class="block text-gray-700 text-sm font-bold mb-2">Additional Notes:</label>
    //                 <textarea id="notes" name="notes" rows="4" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
    //             </div>
    //         `);


    // rfqForm.append(notesGroup);


    // Add a hidden input field to store the selected product IDs.  This is important
    // const productIdsInput = $('<input type="hidden" name="selected_product_ids" value="' + selectedProducts.map(p =>
    //   p.id).join(',') + '">');
    // rfqForm.append(productIdsInput);


    // Handle form submission (you'll need to send this data to the server)
    // rfqForm.on('submit', function(event) {
    //   event.preventDefault();
    //   const formData = $(this).serialize();
    //   // In a real application, you would use AJAX to send the data to your server.
    //   // console.log(formData); // For debugging
    //   // alert('RFQ submitted!  Check console for data.');

    //   $.ajax({
    //     type: 'POST',
    //     url: '/wp-admin/admin-ajax.php', // WordPress AJAX endpoint
    //     data: formData + '&action=submit_rfq', // Include action parameter
    //     success: function(response) {
    //       alert('RFQ submitted successfully!');
    //       // Optionally redirect the user or show a success message
    //       // window.location.href = '/thank-you-page';
    //     },
    //     error: function(error) {
    //       console.error('Error submitting RFQ:', error);
    //       alert('Failed to submit RFQ. Please try again.');
    //     }
    //   });

    // });
  }

  // Initial setup
  updateStep(1); // Show the first step initially

  // Event listeners for step navigation
  productSelection.on('click', function() {
    updateStep(2);
  });
  sellerSelection.on('click', function() {
    updateStep(2);
  });
  rfqCustomization.on('click', function() {
    updateStep(3);
  });

  $('#next-button-1').on('click', function() {
    updateStep(2);
  });

  $('#next-button-2').on('click', function() {
    if (selectedProducts.length > 0) {
      updateStep(3);
    } else {
      alert('Please select at least one product.');
    }
  });

  $('#back-to-1').on('click', function() {
    updateStep(1);
  });

  $('#back-to-2').on('click', function() {
    updateStep(2);
  });

  // Attach event listener to the "Add to RFQ" buttons.  This needs to be here
  $(document).on('click', '.add-to-rfq', function() {
    console.log("jdsfsdlfjlsdjflksdjflsdfjlks")
    const productId = $(this).data('product-id');
    const product = { //  Create a product object.
      id: productId,
      name: $(this).closest('.bg-white').find('h3').text(),
      price: $(this).closest('.bg-white').find('p').text().replace('Price: $', ''),
      image: $(this).closest('.bg-white').find('img').attr('src'),
    };

    if (!selectedProducts.find(p => p.id === productId)) {
      selectedProducts.push(product);
      $(this).text('Added to RFQ').prop('disabled', true).removeClass('bg-amber-500 hover:bg-amber-600')
        .addClass('bg-green-500');
    }
  });


});



add_action('wp_ajax_submit_rfq', 'submit_rfq');
add_action('wp_ajax_nopriv_submit_rfq', 'submit_rfq');

// function submit_rfq() {
//   // Security check (optional, but recommended)
//   if (!check_ajax_referer('submit_rfq_nonce', 'security', false)) {
//     wp_send_json_error('Invalid security token.');
//     wp_die();
//   }

//   // Get the submitted data
//   $selected_product_ids = isset($_POST['selected_product_ids']) ? explode(',', $_POST['selected_product_ids']) :
//     array();
//   $quantities = isset($_POST['quantity']) ? $_POST['quantity'] : array();
//   $notes = isset($_POST['notes']) ? sanitize_textarea_field($_POST['notes']) : '';
//   $descriptions = isset($_POST['description']) ? $_POST['description'] : array(); // Get descriptions


//   // Basic validation
//   if (empty($selected_product_ids) || empty($quantities)) {
//     wp_send_json_error('Please select products and specify quantities.');
//     wp_die();
//   }

//   // Prepare email content
//   $to = get_option('admin_email'); // Send to the admin email
//   $subject = 'New RFQ Submitted';
//   $message = "A new Request for Quotation has been submitted:\n\n";

//   foreach($selected_product_ids as $product_id) {
//     $quantity = isset($quantities[$product_id]) ? intval($quantities[$product_id]) : 0;
//     $description = isset($descriptions[$product_id]) ? sanitize_text_field($descriptions[$product_id]) :
//       ''; // Sanitize description

//     if ($quantity > 0) {
//       $product = wc_get_product($product_id);
//       if ($product) {
//         $message. = "Product: ".$product - > get_name().
//         "\n";
//         $message. = "Quantity: ".$quantity.
//         "\n";
//         $message. = "Price: ".$product - > get_price().
//         "\n";
//         $message. = "Total Price: ".($quantity * $product - > get_price()).
//         "\n";
//         $message. = "Description: ".$description.
//         "\n\n"; // Include description in email
//       }
//     }
//   }

//   $message. = "Additional Notes:\n".$notes.
//   "\n\n";
//   // $message .= "View in admin: " . admin_url('post.php?post=' . $product_id . '&action=edit'); // Removed to prevent errors if product is deleted


//   // Send email
//   $mail_result = wp_mail($to, $subject, $message);

//   if ($mail_result) {
//     wp_send_json_success('RFQ submitted successfully! We will contact you shortly.');
//   } else {
//     wp_send_json_error('Failed to send RFQ. Please try again.');
//   }
//   wp_die();
// } ?
// >
</script>