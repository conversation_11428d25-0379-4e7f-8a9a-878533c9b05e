/* Header Styles - Exact Match to Design */

/* App Download Banner */
.app-download-banner {
  background-color: #1a1e25;
  color: white;
  /* padding: 10px 0; */
}

.app-banner-content {
  padding-top: 65px ;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
}

.app-banner-image img {
  max-height: 80px;
}

.app-banner-text {
  text-align: left;
}

.app-banner-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.app-banner-text h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.btn-app-download {
  background-color: #ea9c00;
  color: white;
  padding: 6px 14px;
  border-radius: 5px;
  font-weight: 600;
  font-size: 14px;
  text-decoration: none;
  transition: background-color 0.2s;
}

.btn-app-download:hover {
  background-color: #d08a00;
  color: white;
}

/* Info Bar */
.info-bar {
  background-color: #ffff;
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.05); */
  padding: 6px 0;
}

.trp-ls-shortcode-current-language {
  border-radius: 25px;
}

.info-bar__list {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: flex;
  align-items: center;
  gap: 15px;
}

.info-bar__list li {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #000;
  transition: color 0.2s ease;
  gap: 3px;
}

.info-bar__list li a {
  display: flex;
  align-items: center;
  color: #000;
  text-decoration: none;
  transition: color 0.2s ease;
  font-weight: 500;

}

.info-bar__list li .trp-ls-shortcode-current-language {
  border-radius: 25px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.trp-language-switcher .trp-ls-shortcode-language {
  border-radius: 25px;
}

 .trp-language-switcher:hover .trp-ls-shortcode-language {
  z-index: 9999;
 }

.info-bar__list li a:hover {
  color: #ea9c00;
}

.info-bar__list li .bi {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin-right: 4px;
  font-size: 14px;
  color: #ea9c00;
}

.info-bar__list select {
  border-radius: 20px;
  font-size: 16px;
  padding: 3px 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  color: #555;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.info-bar__list select:focus {
  outline: none;
  border-color: #ea9c00;
  box-shadow: 0 0 0 2px rgba(234, 156, 0, 0.2);
}

/* Polylang language switcher */
.info-bar__list .language-switcher select {
  padding-right: 25px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23555' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 10px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Right side info bar */
.info-bar .text-md-end .info-bar__list {
  justify-content: flex-end;
}

.info-bar .text-md-end .info-bar__list li {
  font-weight: 500;
}

/* Phone number styling */
.info-bar .text-md-end .info-bar__list li a {
  font-weight: 600;
  color: #ea9c00;
}

.info-bar .text-md-end .info-bar__list li a:hover {
  text-decoration: underline;
}

/* Main Header */
.site-header {
  padding: 12px 0 0 0;
  background-color: white;
}

.site-logo img {
  max-height: 75px;
  width: auto;
}

.search-wrapper {
  width: 100%;
}

.search-wrapper .aws-container {
  width: 100%;
}

.search-wrapper .aws-search-field {
  border-radius: 20px !important;
  height: 35px;
  padding-left: 15px;
  font-size: 16px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.search-wrapper .aws-search-form {
  height: 35px;
}

.search-wrapper .aws-search-form .aws-form-btn {
  background: #ea9c00;
  border: none;
}

.search-wrapper .aws-search-form .aws-search-btn_icon {
  color: white;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-menu-button {
  background: none;
  border: none;
  font-size: 22px;
  color: #333;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-menu-button:focus {
  outline: none;
}

/* Header Icons */
.site-header__list {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

.site-header__list li {
  position: relative;
}

.site-header__list li a {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-decoration: none;
  color: #333;
  font-size: 12px;
  transition: color 0.2s;
  gap: 3px;
}

.site-header__list li a:hover {
  color: #ea9c00;
}

.site-header__list li a i {
  font-size: 18px;
  margin-bottom: 3px;
}

.site-header__list li .cart-count {
  position: absolute;
  top: -5px;
  right: 35px;
  background-color: #ea9c00;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Account Dropdown */
.site-header__list li.account-menu-item {
  position: relative;
}

.site-header__list li.account-menu-item:hover .account-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.site-header__list li.account-menu-item .account-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  width: 220px;
  padding: 10px 0;
  z-index: 100;
  margin-top: 10px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.site-header__list li.account-menu-item .account-dropdown:before {
  content: '';
  position: absolute;
  top: -10px;
  right: 20px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid white;
}

.site-header__list li.account-menu-item .account-dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.site-header__list li.account-menu-item .account-dropdown ul li {
  margin: 0;
  display: block;
}

.site-header__list li.account-menu-item .account-dropdown ul li a {
  padding: 10px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #333;
  font-size: 14px;
  transition: background-color 0.2s;
}

.site-header__list li.account-menu-item .account-dropdown ul li a:hover {
  background-color: rgba(234, 156, 0, 0.1);
  color: #ea9c00;
}

.site-header__list li.account-menu-item .account-dropdown ul li a i {
  margin-right: 10px;
  margin-bottom: 0;
  font-size: 16px;
}

/* Login Button */
.site-header__list li .login-button {
  background-color: #ea9c00;
  color: white;
  padding: 6px 12px;
  border-radius: 5px;
  transition: background-color 0.2s;
  flex-direction: row;
  font-size: 16px;
}

.site-header__list li .login-button i {
  margin-right: 5px;
  margin-bottom: 0;
  font-size: 14px;
}

.site-header__list li .login-button:hover {
  background-color: #d08a00;
  color: white;
}

/* Main Navigation */
.main-navigation-wrapper {
  background-color: #1a1e25;
  margin-top: 8px;
}

.main-navigation {
  padding: 15px 0;
}

.main-navigation ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.main-navigation li {
  position: relative;
}

.main-navigation a {
  display: block;
  padding: 8px 12px;
  color: white;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.2s;
}

.main-navigation a:hover {
  color: #ea9c00;
}

/* Categories Button */
.category-menu-button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 12px;
  background-color: #ea9c00;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
  text-align: left;
}

.category-menu-button:hover {
  background-color: #d08a00;
}

.category-menu-button i {
  margin-right: 6px;
  font-size: 14px;
}

/* Category Dropdown */
.category-dropdown {
  position: absolute;
  /* top: 100%; */
  /* left: 0; */
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1000;
  width: 800px;
  max-width: 90vw;
  margin-top: 5px;
}

.category-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.category-dropdown-content {
  display: flex;
  padding: 20px;
  gap: 20px;
}

.category-column {
  flex: 1;
  min-width: 180px;
}

.category-section {
  margin-bottom: 20px;
}

.category-section strong {
  display: block;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.category-section strong a {
  color: #333;
  font-size: 14px;
  font-weight: 700;
  text-decoration: none;
}

.category-section strong a:hover {
  color: #ea9c00;
}

.category-link {
  display: block;
  padding: 5px 0;
  color: #333;
  font-size: 16px;
  text-decoration: none;
  transition: color 0.2s ease;
}

.category-link:hover {
  color: #ea9c00;
}

.category-link.view-all {
  color: #ea9c00;
  font-weight: 600;
  font-style: italic;
}

/* Menu Toggle Button */
.menu-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
}

.menu-toggle:focus {
  outline: none;
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.mobile-nav-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.mobile-nav-item a {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #555;
  text-decoration: none;
  font-size: 11px;
}

.mobile-nav-item a i {
  font-size: 18px;
  margin-bottom: 3px;
}

.mobile-nav-item a:hover {
  color: #ea9c00;
}

.mobile-cart-count {
  position: absolute;
  top: -5px;
  right: 50%;
  transform: translateX(10px);
  background-color: #ea9c00;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .site-header__list {
    gap: 0px;
  }

  .site-header__list li a span {
    font-size: 11px;
    font-weight: 500;
  }

  .category-dropdown {
    width: 600px;
  }
}

@media (max-width: 768px) {

  .app-banner-image img {
    display: none;
  }

  .app-banner-content {
    flex-direction: row;
    text-align: center;
    padding: 8px 0;
  }

  .app-banner-text {
    text-align: center;
  }

  .info-bar__list {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }

  .info-bar .text-md-end .info-bar__list {
    justify-content: center;
    margin-top: 8px;
  }

  .site-header {
    padding: 8px 0;
  }

  .site-logo img {
    max-height: 30px;
  }

  .search-wrapper .aws-search-field {
    height: 32px;
    font-size: 12px;
  }

  .search-wrapper .aws-search-form {
    height: 32px;
  }

  .category-dropdown {
    position: static;
    width: 100%;
    max-width: 100%;
  }

  .category-dropdown-content {
    flex-direction: column;
  }

  body {
    padding-bottom: 50px;
    /* Space for mobile bottom nav */
  }

  /* Make the main navigation more compact on mobile */
  .main-navigation {
    padding: 0px 0;
    text-align: left;
  }

  /* Make main navigation wrapper inline on mobile */
  .main-navigation-wrapper {
    display: inline-block !important;
    width: auto !important;
    margin-top: 0;
  }

  /* Override Bootstrap grid behavior for inline navigation */
  .main-navigation-wrapper .container {
    display: inline-block !important;
    width: auto !important;
    max-width: none !important;
    padding: 0 !important;
  }

  .main-navigation-wrapper .row {
    display: inline-flex !important;
    flex-wrap: nowrap !important;
    margin: 0 !important;
  }

  .main-navigation-wrapper .col-6,
  .main-navigation-wrapper .col-md-9,
  .main-navigation-wrapper .col-lg-10 {
    flex: none !important;
    width: auto !important;
    padding: 0 !important;
  }

  .category-menu-button {
    padding: 6px 10px;
    font-size: 12px;
  }

  .menu-toggle {
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .info-bar__list {
    gap: 8px;
  }

  .info-bar__list li {
    font-size: 12px;
  }

  .info-bar__list select {
    font-size: 12px;
    padding: 2px 8px;
  }

  .site-logo img {
    max-height: 28px;
  }

  .search-wrapper .aws-search-field {
    height: 30px;
    font-size: 12px;
  }

  .search-wrapper .aws-search-form {
    height: 30px;
  }
}