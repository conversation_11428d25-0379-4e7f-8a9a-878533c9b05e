{{- /*
  Usage: `js-docs name="name" file="file/_location.js`

  Prints everything between `// js-docs-start "name"` and `// js-docs-end "name"`
  comments in the docs.
*/ -}}

{{- $name := .Get "name" -}}
{{- $file := .Get "file" -}}

{{- /* If any parameters are missing, print an error and exit */ -}}
{{- if or (not $name) (not $file) -}}
  {{- errorf "%s: %q: Missing required parameters! Got: name=%q file=%q!" .Position .Name $name $file -}}
{{- else -}}
  {{- $capture_start := printf "// js-docs-start %s\n" $name -}}
  {{- $capture_end := printf "// js-docs-end %s\n" $name -}}
  {{- $regex := printf `%s((?:.|\n)*)%s` $capture_start $capture_end -}}
  {{- $regex_nested := printf `// js-docs-.*\n` -}}

  {{- $match := findRE $regex (readFile $file) -}}
  {{- $match = index $match 0 -}}

  {{- if not $match -}}
    {{- errorf "%s: %q: Got no matches for name=%q in file=%q!" .Position .Name $name $file -}}
  {{- end -}}

  {{- $match = replace $match $capture_start "" -}}
  {{- $match = replace $match $capture_end "" -}}

  {{- $match_nested := findRE $regex_nested $match -}}
  {{- range $to_remove := $match_nested -}}
    {{- $match = replace $match $to_remove "" -}}
  {{- end -}}

  <div class="bd-example-snippet bd-code-snippet bd-file-ref">
    <div class="d-flex align-items-center highlight-toolbar ps-3 pe-2 py-1 border-bottom">
      <a class="font-monospace link-secondary link-underline-secondary link-underline-opacity-0 link-underline-opacity-100-hover small" href="{{ .Site.Params.repo }}/blob/v{{ .Site.Params.current_version }}/{{ $file | replaceRE `\\` "/" }}">
        {{- $file -}}
      </a>
      <div class="d-flex ms-auto">
        <button type="button" class="btn-clipboard mt-0 me-0" title="Copy to clipboard">
          <svg class="bi" aria-hidden="true"><use xlink:href="#clipboard"/></svg>
        </button>
      </div>
    </div>

    {{- $unindent := 0 -}}
    {{- $found := false -}}
    {{- $first_line:= index (split $match "\n") 0 -}}
    {{- range $char := split $first_line "" -}}
      {{- if and (eq $char " ") (not $found) -}}
        {{- $unindent = add $unindent 1 -}}
      {{- else -}}
        {{- $found = true -}}
      {{- end -}}
    {{- end -}}
    {{- $output := "" -}}
    {{- if (gt $unindent 0) -}}
      {{- $prefix := (strings.Repeat $unindent " ") -}}
      {{- range $line := split $match "\n" -}}
        {{- $line = strings.TrimPrefix $prefix $line -}}
        {{ $output = printf "%s%s\n" $output $line }}
      {{- end -}}
      {{- $output = chomp $output -}}
    {{- else -}}
      {{- $output = $match -}}
    {{- end -}}
    {{- highlight $output "js" "" -}}
  </div>
{{- end -}}
