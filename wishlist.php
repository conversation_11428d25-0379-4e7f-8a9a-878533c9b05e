<?php
/**
 * Wishlist Page Template
 * This template can be overridden by copying it to yourtheme/woocommerce/wishlist.php
 */

?>

<?php get_header();?>
<?php

defined( 'ABSPATH' ) || exit;

// Handle wishlist item removal via GET (simple and reliable)
if (isset($_GET['remove_item']) && isset($_GET['_wpnonce'])) {
    $product_id = intval($_GET['remove_item']);
    $nonce = sanitize_text_field($_GET['_wpnonce']);

    if (wp_verify_nonce($nonce, 'remove_wishlist_' . $product_id)) {
        // Direct database removal - most reliable method
        global $wpdb;
        $table_name = $wpdb->prefix . 'yith_wcwl';

        $user_id = get_current_user_id();
        $where_conditions = array('prod_id' => $product_id);

        if ($user_id > 0) {
            $where_conditions['user_id'] = $user_id;
        } else {
            // For guest users, use session
            $session_id = isset($_COOKIE['yith_wcwl_session_' . COOKIEHASH]) ? $_COOKIE['yith_wcwl_session_' . COOKIEHASH] : '';
            if ($session_id) {
                $where_conditions['session_id'] = $session_id;
            }
        }

        $deleted = $wpdb->delete($table_name, $where_conditions);

        // Set a flag for showing message without redirect
        $removal_result = $deleted > 0 ? 'success' : 'failed';
    }
}



do_action( 'woocommerce_before_wishlist' ); ?>

<div class="entry-content">
  <div class="custom-wishlist-container">
    <!-- <h1 class="custom-wishlist-title"><?php esc_html_e('My Wishlist', 'woocommerce'); ?></h1> -->

    <?php
    // Show removal status messages
    if (isset($removal_result)) {
        if ($removal_result === 'success') {
            echo '<div class="woocommerce-message" style="margin-bottom: 20px; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; color: #155724; border-radius: 4px;">✓ Item removed from wishlist successfully!</div>';
        } else {
            echo '<div class="woocommerce-error" style="margin-bottom: 20px; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; border-radius: 4px;">✗ Failed to remove item from wishlist. Please try again.</div>';
        }
    }
    ?>

    <form class="woocommerce-wishlist-form" action="<?php echo esc_url(wc_get_cart_url()); ?>" method="post">
      <div class="custom-wishlist-header">
        <div class="custom-wishlist-header-checkbox">
          <input type="checkbox" id="select-all" name="select-all">
          <label for="select-all"><?php esc_html_e('Select all', 'woocommerce'); ?></label>
        </div>
        <button type="button" id="delete-selected" class="custom-delete-selected">
          <i class="bi bi-trash3"></i>
          <?php esc_html_e('Delete selected items', 'woocommerce'); ?>
        </button>
      </div>

      <?php
      $wishlist_items = YITH_WCWL()->get_products([
        'wishlist_id' => 'all',
        'user_id' => get_current_user_id(),
      ]);

      if (!empty($wishlist_items)) : ?>
      <div class="custom-wishlist-grid">
        <?php foreach ($wishlist_items as $wishlist_item) :
            $product = wc_get_product($wishlist_item['prod_id']);
            if (!$product) continue;

            $product_permalink = $product->is_visible() ? $product->get_permalink() : '';
            $store_url = get_permalink(get_post_field('post_author', $product->get_id()));
            $store_name = get_the_title(get_post_field('post_author', $product->get_id()));
            if (empty($store_name)) {
              $store_name = esc_html__('Laptop Store', 'woocommerce');
            }
          ?>
        <div class="wishlist-item-row">
          <div class="item-checkbox">
            <input type="checkbox" class="wishlist-item-checkbox"
              value="<?php echo esc_attr($wishlist_item['prod_id']); ?>">
          </div>

          <div class="item-content">
            <div class="product-image">
              <a href="<?php echo esc_url($product_permalink); ?>">
                <?php echo $product->get_image('medium'); ?>
              </a>
            </div>

            <div class="product-details">
              <h3 class="product-title">
                <a href="<?php echo esc_url($product_permalink); ?>">
                  <?php echo esc_html($product->get_name()); ?>
                </a>
              </h3>

              <div class="store-info">
                <i class="bi bi-shop"></i>
                <span class="store-name"><?php echo esc_html($store_name); ?></span>
              </div>

              <div class="product-rating">
                <?php
                  if ($product->get_average_rating()) {
                    echo wc_get_rating_html($product->get_average_rating());
                    echo '<span class="rating-text">' . number_format($product->get_average_rating(), 1) . '</span>';
                    echo '<span class="review-count">' . $product->get_review_count() . ' ' . esc_html(_n('Review', 'Reviews', $product->get_review_count(), 'woocommerce')) . '</span>';
                  } else {
                    echo '<div class="rating-display no-reviews">';
                    echo '<div class="stars-empty">☆☆☆☆☆</div>';
                    echo '<span class="no-reviews-text">' . esc_html__('No reviews', 'woocommerce') . '</span>';
                    echo '</div>';
                  }
                  ?>
              </div>

              <div class="product-categories">
                <?php
                  $categories = get_the_terms($product->get_id(), 'product_cat');
                  if ($categories && !is_wp_error($categories)) {
                    foreach (array_slice($categories, 0, 2) as $category) {
                      echo '<span class="category-tag">' . esc_html($category->name) . '</span>';
                    }
                  } else {
                    echo '<span class="category-tag">Gaming Laptops</span>';
                    echo '<span class="category-tag">Laptops</span>';
                  }
                  ?>
              </div>

              <div class="product-actions">
                <button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>"
                  class="btn-add-to-cart">
                  <i class="bi bi-cart"></i> <?php esc_html_e('Add to cart', 'woocommerce'); ?>
                </button>
                <?php
                $remove_url = add_query_arg(array(
                    'remove_item' => $product->get_id(),
                    '_wpnonce' => wp_create_nonce('remove_wishlist_' . $product->get_id())
                ));
                ?>
                <a href="<?php echo esc_url($remove_url); ?>" class="btn-delete-item"
                  onclick="return confirm('Remove this item from wishlist?');">
                  <i class="bi bi-trash3"></i> <?php esc_html_e('Delete item', 'woocommerce'); ?>
                </a>
              </div>
            </div>

            <div class="product-price-section">
              <div class="price-amount">
                <?php echo $product->get_price_html(); ?>
              </div>
              <div class="tax-info">
                <?php esc_html_e('Tax:', 'woocommerce'); ?> 20.00 <?php esc_html_e('QAR', 'woocommerce'); ?>
              </div>
            </div>
          </div>
        </div>
        <?php endforeach; ?>
      </div>
      <?php else : ?>
      <div class="custom-wishlist-empty">
        <p><?php esc_html_e('Your wishlist is currently empty.', 'woocommerce'); ?></p>
        <a href="<?php echo esc_url(get_permalink(wc_get_page_id('shop'))); ?>"
          class="button"><?php esc_html_e('Browse Products', 'woocommerce'); ?></a>
      </div>
      <?php endif; ?>
    </form>
  </div>
</div>


<script>
jQuery(document).ready(function($) {
  // Select all functionality
  $('#select-all').on('change', function() {
    $('.wishlist-item-checkbox').prop('checked', $(this).prop('checked'));
  });

  // Bulk delete functionality - simple and reliable
  $('#delete-selected').on('click', function(e) {
    e.preventDefault();

    const selectedItems = $('.wishlist-item-checkbox:checked');

    if (selectedItems.length === 0) {
      alert('Please select items to delete');
      return;
    }

    if (!confirm('Remove ' + selectedItems.length + ' selected item(s) from wishlist?')) {
      return;
    }

    // Remove selected items by hiding them and then reloading page
    selectedItems.each(function() {
      const productId = $(this).val();
      const $row = $(this).closest('.wishlist-item-row');

      // Fade out the row
      $row.fadeOut(300);
    });

    // After animation, redirect to remove the first item
    // setTimeout(function() {
    //   const firstProductId = selectedItems.first().val();
    //   const removeUrl = window.location.href +
    //     (window.location.href.indexOf('?') > -1 ? '&' : '?') +
    //     'remove_item=' + firstProductId +
    //     '&_wpnonce=' + '<?php echo wp_create_nonce('remove_wishlist_'); ?>' + firstProductId;

    //   window.location.href = removeUrl;
    // }, 500);
  });
});
</script>