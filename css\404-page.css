/* 404 Error Page Styles */
.error-404-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
  padding: 60px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.error-404-content {
  text-align: center;
  max-width: 900px;
  width: 100%;
  /* background: white; */
  border-radius: 0px;
  padding: 60px 40px;
  box-shadow: none;
  border: none;
}

error-404-actions a:visited {
  color: #f8f9fa !important;
}

/* Broken Link Icon */
.error-404-icon {
  margin-bottom: 30px;
}

.error-404-icon svg {
  width: 80px;
  height: 80px;
  opacity: 0.9;
}

/* 404 Number */
.error-404-number {
  margin-bottom: 25px;
}

.error-404-number h1 {
  font-size: 6rem;
  font-weight: 800;
  color: #2c3e50;
  margin: 0;
  line-height: 1;
  letter-spacing: -2px;
}

/* Error Message */
.error-404-message {
  margin-bottom: 40px;
}

.error-404-message p {
  font-size: 1.1rem;
  color: #6c757d;
  line-height: 1.6;
  margin: 0;
  max-width: 450px;
  margin: 0 auto;
}

/* Action Button */
.error-404-actions {
  margin-bottom: 50px;
}

.error-404-btn {
  background-color: #ea9c00;
  border-color: #ea9c00;
  color: white;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 15px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  box-shadow: none;
}

.error-404-btn:hover {
  background-color: #d08a00;
  border-color: #d08a00;
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(234, 156, 0, 0.4);
}

/* Additional Links Section */
.error-404-links {
  border-top: 1px solid #e9ecef;
  padding-top: 40px;
}

.helpful-links {
  margin-bottom: 40px;
}

.helpful-links h3,
.error-404-search h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  max-width: 400px;
  margin: 0 auto;
}

.helpful-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #6c757d;
  padding: 20px 15px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  background: #fafafa;
  font-size: 0.9rem;
  font-weight: 500;
}

.helpful-link:hover {
  text-decoration: none;
  color: #ea9c00;
  border-color: rgba(234, 156, 0, 0.3);
  background: white;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.helpful-link i {
  margin-bottom: 8px;
  font-size: 20px;
}

/* Search Section */
.error-404-search {
  max-width: 400px;
  margin: 0 auto;
}

.error-404-search .search-wrapper {
  margin-top: 15px;
}

.error-404-search .search-form-wrapper {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 25px;
  overflow: hidden;
  background: white;
}

.error-404-search .search-field {
  flex: 1;
  border: none;
  padding: 12px 20px;
  font-size: 1rem;
  outline: none;
}

.error-404-search .search-submit {
  background: #ea9c00;
  border: none;
  color: white;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.error-404-search .search-submit:hover {
  background: #d08a00;
}

/* AWS Search Form Styling */
.error-404-search .aws-container {
  width: 100%;
}

.error-404-search .aws-search-field {
  border-radius: 25px !important;
  height: 45px;
  padding-left: 20px;
  font-size: 1rem;
  border: 1px solid #ddd;
}

.error-404-search .aws-search-form {
  height: 45px;
}

.error-404-search .aws-search-form .aws-form-btn {
  background: #ea9c00;
  border: none;
  border-radius: 0 25px 25px 0;
}

.error-404-search .aws-search-form .aws-search-btn_icon {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .error-404-container {
    min-height: 60vh;
    padding: 40px 15px;
  }

  .error-404-content {
    padding: 40px 25px;
    border-radius: 15px;
  }

  .error-404-number h1 {
    font-size: 4.5rem;
  }

  .error-404-message p {
    font-size: 1rem;
  }

  .error-404-btn {
    padding: 12px 25px;
    font-size: 1rem;
  }

  .link-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .helpful-link {
    padding: 15px 10px;
    font-size: 0.85rem;
  }

  .helpful-link i {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .error-404-number h1 {
    font-size: 3.5rem;
  }

  .error-404-content {
    padding: 30px 20px;
  }

  .link-grid {
    grid-template-columns: 1fr 1fr;
  }

  .helpful-links h3,
  .error-404-search h3 {
    font-size: 1.1rem;
  }
}
