/* Search Form Fixes and Improvements */

/* Fallback search form styling */
.search-form {
    width: 100%;
    position: relative;
}

.search-form-wrapper {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    overflow: hidden;
    height: 40px;
}

.search-form .search-field {
    flex: 1;
    border: none;
    outline: none;
    padding: 0 15px;
    font-size: 14px;
    background: transparent;
    height: 100%;
}

.search-form .search-field:focus {
    outline: none;
    box-shadow: none;
}

.search-form .search-submit {
    background: #ea9c00;
    border: none;
    padding: 0 15px;
    height: 100%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.search-form .search-submit:hover {
    background: #d08a00;
}

.search-form .search-submit i {
    width: 16px;
    height: 16px;
}

/* AWS Search Form Improvements */
.search-wrapper .aws-container {
    width: 100%;
}

.search-wrapper .aws-search-field {
    border-radius: 10px !important;
    height: 40px;
    padding-left: 15px;
    font-size: 14px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: #F4F7FD;
}

.search-wrapper .aws-search-form {
    height: 40px;
}

.search-wrapper .aws-search-form .aws-form-btn {
    background: #ea9c00;
    border: none;
    border-radius: 0 20px 20px 0;
}

.search-wrapper .aws-search-form .aws-form-btn:hover {
    background: #d08a00;
}

/* Search Results Page Styling */
.search-results-count {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

.search-results-products .row {
    margin: 0 -15px;
}

.search-results-products .col-lg-3,
.search-results-products .col-md-4,
.search-results-products .col-sm-6 {
    padding: 0 15px;
}

.search-results-general .entry-header {
    margin-bottom: 15px;
}

.search-results-general .entry-title {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.search-results-general .entry-summary {
    color: #666;
    line-height: 1.6;
}

/* Mobile Search Improvements */
@media (max-width: 767.98px) {

    .search-wrapper .aws-search-field,
    .search-form .search-field {
        height: 44px !important;
        font-size: 16px !important;
        /* Prevents zoom on iOS */
        padding: 12px 16px !important;
    }

    .search-wrapper .aws-search-form,
    .search-form-wrapper {
        height: 44px !important;
    }

    .search-wrapper .aws-search-form .aws-form-btn,
    .search-form .search-submit {
        min-width: 44px;
        height: 44px;
    }

    .search-results-products .col-lg-3,
    .search-results-products .col-md-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* Search Loading State */
.search-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.search-loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ea9c00;
    border-radius: 50%;
    border-top-color: transparent;
    /* No spinning animation */
    margin-right: 10px;
}

/* Search Error State */
.search-error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
    border: 1px solid #f5c6cb;
}

/* Search No Results */
.no-results.not-found {
    text-align: center;
    padding: 40px 20px;
}

.no-results .page-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #333;
}

.no-results .page-content {
    color: #666;
    line-height: 1.6;
}

/* AWS Search Dropdown Improvements */
.aws-search-result {
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.aws-search-result .aws_result_item {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
}

.aws-search-result .aws_result_item:last-child {
    border-bottom: none;
}

.aws-search-result .aws_result_item:hover {
    background: #f8f9fa;
}

/* Search Form Focus States */
.search-wrapper .aws-search-field:focus,
.search-form .search-field:focus,
.tendeal-search-form .search-field:focus {
    border-color: #ea9c00;
    box-shadow: 0 0 0 2px rgba(234, 156, 0, 0.2);
}

/* Tendeal Search Form Styling */
.tendeal-search-form {
    width: 100%;
    position: relative;
}

.tendeal-search-form .search-form-wrapper {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    overflow: hidden;
    height: 40px;
}

.tendeal-search-form .search-field {
    flex: 1;
    border: none;
    outline: none;
    padding: 0 15px;
    font-size: 14px;
    background: transparent;
    height: 100%;
}

.tendeal-search-form .search-submit {
    background: #ea9c00;
    border: none;
    padding: 0 15px;
    height: 100%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.tendeal-search-form .search-submit:hover {
    background: #d08a00;
}

/* Search Messages */
.search-message {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    padding: 10px 15px;
    border-radius: 5px;
    margin-top: 5px;
    font-size: 14px;
    z-index: 1000;
}

.search-message-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.search-message-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.search-message-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.search-message-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* Search Form Loading States */
.search-form.searching,
.aws-search-form.searching,
.tendeal-search-form.searching {
    opacity: 0.7;
    pointer-events: none;
}

.search-form.searching .search-submit,
.aws-search-form.searching .aws-form-btn,
.tendeal-search-form.searching .search-submit {
    background: #ccc;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    /* No spinning animation */
}

/* Search Results Improvements */
.search-results-products .product {
    transition: transform 0.2s, box-shadow 0.2s;
}

.search-results-products .product:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Search Page Header */
.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
}

.page-header .page-title {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #333;
}

.page-header .page-title span,
.shop-title .search-term {
    color: #ea9c00;
    font-weight: bold;
}

/* Shop Search Results Styling */
.shop-header .shop-title .search-term {
    color: #ea9c00;
    font-weight: bold;
    text-decoration: underline;
}