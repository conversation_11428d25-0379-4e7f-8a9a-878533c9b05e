{"root": true, "extends": ["plugin:import/errors", "plugin:import/warnings", "plugin:unicorn/recommended", "xo", "xo/browser"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "import/extensions": ["error", "ignorePackages", {"js": "always"}], "import/first": "error", "import/newline-after-import": "error", "import/no-absolute-path": "error", "import/no-amd": "error", "import/no-cycle": ["error", {"ignoreExternal": true}], "import/no-duplicates": "error", "import/no-extraneous-dependencies": "error", "import/no-mutable-exports": "error", "import/no-named-as-default": "error", "import/no-named-as-default-member": "error", "import/no-named-default": "error", "import/no-self-import": "error", "import/no-unassigned-import": ["error"], "import/no-useless-path-segments": "error", "import/order": "error", "indent": ["error", 2, {"MemberExpression": "off", "SwitchCase": 1}], "logical-assignment-operators": "off", "max-params": ["warn", 5], "multiline-ternary": ["error", "always-multiline"], "new-cap": ["error", {"properties": false}], "no-console": "error", "no-negated-condition": "off", "object-curly-spacing": ["error", "always"], "operator-linebreak": ["error", "after"], "prefer-object-has-own": "off", "prefer-template": "error", "semi": ["error", "never"], "strict": "error", "unicorn/explicit-length-check": "off", "unicorn/filename-case": "off", "unicorn/no-array-callback-reference": "off", "unicorn/no-array-method-this-argument": "off", "unicorn/no-null": "off", "unicorn/no-typeof-undefined": "off", "unicorn/no-unused-properties": "error", "unicorn/numeric-separators-style": "off", "unicorn/prefer-array-flat": "off", "unicorn/prefer-at": "off", "unicorn/prefer-dom-node-dataset": "off", "unicorn/prefer-module": "off", "unicorn/prefer-query-selector": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-string-replace-all": "off", "unicorn/prevent-abbreviations": "off"}, "overrides": [{"files": ["build/**"], "env": {"browser": false, "node": true}, "parserOptions": {"sourceType": "module"}, "rules": {"no-console": "off", "unicorn/prefer-top-level-await": "off"}}, {"files": ["js/**"], "parserOptions": {"sourceType": "module"}}, {"files": ["js/tests/*.js", "js/tests/integration/rollup*.js"], "env": {"node": true}, "parserOptions": {"sourceType": "script"}}, {"files": ["js/tests/unit/**"], "env": {"jasmine": true}, "rules": {"no-console": "off", "unicorn/consistent-function-scoping": "off", "unicorn/no-useless-undefined": "off", "unicorn/prefer-add-event-listener": "off"}}, {"files": ["js/tests/visual/**"], "plugins": ["html"], "settings": {"html/html-extensions": [".html"]}, "rules": {"no-console": "off", "no-new": "off", "unicorn/no-array-for-each": "off"}}, {"files": ["scss/tests/**"], "env": {"node": true}, "parserOptions": {"sourceType": "script"}}, {"files": ["site/**"], "env": {"browser": true, "node": false}, "parserOptions": {"sourceType": "script", "ecmaVersion": 2019}, "rules": {"no-new": "off", "unicorn/no-array-for-each": "off"}}, {"files": ["**/*.md"], "plugins": ["markdown"], "processor": "markdown/markdown"}, {"files": ["**/*.md/*.js"], "extends": "plugin:markdown/recommended", "parserOptions": {"sourceType": "module"}, "rules": {"unicorn/prefer-node-protocol": "off"}}]}