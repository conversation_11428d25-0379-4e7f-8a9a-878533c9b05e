/**
 * Mobile Layout Fix for Tendeal Theme
 *
 * This file fixes the common issue where mobile layout appears smaller
 * than the screen size with white space around the edges.
 */

/* ==========================================================================
   CRITICAL MOBILE LAYOUT FIXES
   ========================================================================== */

/* Ensure proper viewport behavior */
html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Fix body and html to use full width */
html,
body {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix the main site container */
#page.site {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}

/* Fix Bootstrap containers on mobile */
@media (max-width: 767.98px) {

  /* Container fixes */
  .container,
  .container-fluid,
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-xxl {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  /* Row fixes */
  .row {
    width: 100% !important;
    max-width: 100% !important;
    margin-left: -15px !important;
    margin-right: -15px !important;
  }

  /* Column fixes */
  [class*="col-"] {
    padding-left: 15px !important;
    padding-right: 15px !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Specific mobile column overrides */
  .col-4,
  .col-6,
  .col-8,
  .col-12 {
    flex: 0 0 auto !important;
  }

  .col-4 {
    width: 33.333333% !important;
  }

  .col-6 {
    width: 50% !important;
  }

  .col-8 {
    width: 66.666667% !important;
  }

  .col-12 {
    width: 100% !important;
  }
}

/* ==========================================================================
   HEADER MOBILE FIXES
   ========================================================================== */

@media (max-width: 767.98px) {

  /* App banner fixes */
  .app-download-banner {
    width: 100% !important;
    padding: 10px 0 !important;
  }

  .app-banner-content {
    flex-direction: row !important;
    margin-top: 50px !important;
    gap: 10px !important;
    padding: 0 15px !important;
  }

  .info-bar .text-md-end .info-bar__list {
    justify-content: flex-start !important;
  }

  /* .info-bar__list .left{
padding-top: 10px !important;
  } */

  .trp-ls-shortcode-current-language {
    width: auto !important;
  }

  .trp-language-switcher {
    width: auto !important;
    height: auto !important;
  }

  .logo-col {
    width: 16% !important;
  }

  .search-col {
    width: 84% !important;
  }

  .app-banner-text p {
    font-size: 10px !important;
  }

  /* Info bar fixes */
  .info-bar {
    width: 100% !important;
    padding: 8px 0 !important;
  }

  .info-bar .container {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .info-bar__list {
    flex-wrap: wrap !important;
    gap: 8px !important;
    justify-content: center !important;
  }

  .info-bar__list li {
    font-size: 12px !important;
  }

  /* Header fixes */
  .site-header {
    width: 100% !important;
    padding: 8px 0 !important;
  }

  .site-header .container {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .site-header .row {
    margin-left: -10px !important;
    margin-right: -10px !important;
  }

  .site-header [class*="col-"] {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  /* Logo fixes */
  .site-logo {
    padding: 5px 0 !important;
  }

  .site-logo img {
    max-height: 35px !important;
    width: auto !important;
  }

  /* Search wrapper fixes */
  .search-wrapper {
    width: 100% !important;
    margin: 0 !important;
  }

  .search-wrapper .aws-container {
    width: 100% !important;
  }

  .feather {
    width: 12px !important;
  }

  .search-wrapper .aws-search-field {
    width: 100% !important;
    height: 40px !important;
    font-size: 14px !important;
    padding: 8px 12px !important;
  }

  /* Navigation fixes */
  .main-navigation-wrapper {
    width: 100% !important;
    padding: 8px 0 !important;
  }

  .main-navigation .container {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .main-navigation ul {
    flex-wrap: nowrap !important;
    gap: 5px !important;
    flex-direction: row !important;
  }

  .main-navigation a {
    padding: 0px 2px !important;
    font-size: 10px !important;
  }
  
}

/* ==========================================================================
   CONTENT AREA MOBILE FIXES
   ========================================================================== */

@media (max-width: 767.98px) {

  /* Main content area */
  .site-main {
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Content containers */
  .site-main .container {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  /* Sidebar fixes */
  .side-bar {
    width: 100% !important;
    margin-bottom: 20px !important;
  }

  .side-bar__ads {
    display: flex !important;
    flex-direction: row !important;
    gap: 10px !important;
  }

  .side-bar__ads img {
    width: 30% !important;
    height: auto !important;
  }

  /* Main content fixes */
  .main-content {
    width: 100% !important;
  }

  /* Product grid fixes */
  .woocommerce ul.products {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .woocommerce ul.products li.product {
    width: 100% !important;
    margin: 0 0 20px 0 !important;
    padding: 15px !important;
    box-sizing: border-box !important;
  }

  /* Banner fixes */
  .top-ads,
  .announcement-bar {
    width: 100% !important;
    margin: 10px 0 !important;
  }

  .banner-wrapper {
    width: 100% !important;
    margin-bottom: 15px !important;
  }

  .banner-wrapper img {
    width: 100% !important;
    height: 150px !important;
  }

  .top-ads .top-left {
    position: absolute;
    text-align: start;
    top: 5px;
    left: 30px;
    width: 50%;
  }

  .top-ads .top-left span {
    font-size: 10px !important;
  }

  .top-ads .top-left h1 {
    font-size: 18px !important;
  }

  .top-ads .top-left button {
    font-size: 10px !important;
    padding: 6px 32px !important;
    margin-top: 10px !important;
    min-height: 16px !important
  }

  .right-banned .banner-wrapper img {
    height: 250px !important;
  }

  .right-banned .banner-wrapper h4 {
    font-size: 11px !important;
  }

  .right-banned .banner-wrapper .top-left {
    width: 90% !important;
  }

  .announcement-bar {
    padding: 10px;
    width: 100% !important;
    height: auto !important;
  }

  .announcement-item {
    width: 48px !important;
    height: 48px !important;
  }

  .announcement-text {
    width: auto !important;
  }
}

/* ==========================================================================
   MOBILE BOTTOM NAVIGATION FIXES
   ========================================================================== */

@media (max-width: 767.98px) {

  .mobile-bottom-nav {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    background-color: white !important;
    display: flex !important;
    justify-content: space-around !important;
    padding: 10px 0 !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
    z-index: 1000 !important;
    border-top: 1px solid #eee !important;
  }

  .mobile-nav-item {
    flex: 1 !important;
    text-align: center !important;
  }

  .mobile-nav-item a {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    color: #555 !important;
    text-decoration: none !important;
    font-size: 11px !important;
    padding: 5px !important;
  }

  .mobile-nav-item a i {
    font-size: 18px !important;
    margin-bottom: 3px !important;
  }

  /* Add bottom padding to body to account for fixed mobile nav */
  body {
    padding-bottom: 70px !important;
  }
}

/* ==========================================================================
   PREVENT HORIZONTAL SCROLLING
   ========================================================================== */

@media (max-width: 767.98px) {

  /* Prevent any element from causing horizontal scroll */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix images */
  img {
    max-width: 100% !important;
    height: auto !important;
  }

  /* Fix tables */
  table {
    width: 100% !important;
    table-layout: fixed !important;
  }

  /* Fix pre and code blocks */
  pre,
  code {
    max-width: 100% !important;
    overflow-x: auto !important;
    word-wrap: break-word !important;
  }

  /* Fix any absolute positioned elements */
  .position-absolute {
    max-width: calc(100% - 30px) !important;
  }
}

/* ==========================================================================
   FORM FIXES FOR MOBILE
   ========================================================================== */

@media (max-width: 767.98px) {

  /* Form container fixes */
  .woocommerce form,
  .woocommerce-form,
  form {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Input fixes */
  .form-control,
  .form-select,
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  input[type="number"],
  textarea,
  select {
    width: 100% !important;
    max-width: 100% !important;
    font-size: 16px !important;
    /* Prevents zoom on iOS */
    padding: 12px !important;
    border-radius: 6px !important;
  }

  /* Button fixes */
  .btn,
  .button,
  input[type="submit"],
  input[type="button"] {
    /* width: 100% !important; */
    max-width: 100% !important;
    padding: 12px 20px !important;
    font-size: 16px !important;
    margin-bottom: 10px !important;
  }
}

/* ==========================================================================
   PAGINATION FIXES - PREVENT DUPLICATES
   ========================================================================== */

/* Hide duplicate pagination elements globally - More aggressive approach */
.woocommerce-pagination:nth-of-type(n+2) {
  display: none !important;
}

.shop-pagination:nth-of-type(n+2) {
  display: none !important;
}

/* Hide any pagination that comes after another pagination */
.woocommerce-pagination+.woocommerce-pagination,
.shop-pagination+.woocommerce-pagination,
.woocommerce-pagination+.shop-pagination,
.shop-pagination+.shop-pagination {
  display: none !important;
}

/* Hide pagination with test class (from dynamic filters) if there's already one */
.woocommerce-pagination.test {
  display: none !important;
}

.woocommerce-pagination:not(.test)~.woocommerce-pagination.test {
  /* display: block !important; */
}

.woocommerce-pagination:not(.test) {
  /* display: block !important; */
}

/* Force hide duplicate pagination in specific containers */
.shop-container .woocommerce-pagination:nth-child(n+2),
.products-container .woocommerce-pagination:nth-child(n+2),
#shop-products~.woocommerce-pagination:nth-of-type(n+2) {
  /* display: none !important; */
}

/* Additional aggressive pagination hiding rules */
.woocommerce .woocommerce-pagination:not(:first-of-type),
body.woocommerce-shop .woocommerce-pagination:not(:first-of-type),
body.tax-product_cat .woocommerce-pagination:not(:first-of-type),
body.tax-product_tag .woocommerce-pagination:not(:first-of-type),
body.tax-product_brand .woocommerce-pagination:not(:first-of-type) {
  /* display: none !important; */
}

/* Force hide any pagination beyond the first in main content area */
main .woocommerce-pagination:nth-of-type(n+2),
.site-main .woocommerce-pagination:nth-of-type(n+2),
#primary .woocommerce-pagination:nth-of-type(n+2),
#content .woocommerce-pagination:nth-of-type(n+2) {
  display: none !important;
}

/* Hide pagination with specific classes that might be duplicates */
.woocommerce-pagination.test,
.woocommerce-pagination.duplicate,
.woocommerce-pagination.secondary {
  display: none !important;
}

@media (max-width: 767.98px) {

  /* Style the main pagination */
  .woocommerce-pagination,
  .shop-pagination {
    margin: 30px 0 !important;
    text-align: center !important;
  }

  .woocommerce-pagination ul,
  .shop-pagination .page-numbers {
    display: inline-flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 8px !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
  }

  .woocommerce-pagination ul li,
  .shop-pagination .page-numbers {
    margin: 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
  }

  .woocommerce-pagination ul li a,
  .woocommerce-pagination ul li span,
  .shop-pagination .page-numbers {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 44px !important;
    height: 44px !important;
    padding: 8px 12px !important;
    background: #fff !important;
    border: 1px solid #ddd !important;
    color: #333 !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
  }

  .woocommerce-pagination ul li a:hover,
  .shop-pagination .page-numbers:hover {
    background: #ea9c00 !important;
    border-color: #ea9c00 !important;
    color: white !important;
  }

  .woocommerce-pagination ul li span.current,
  .shop-pagination .page-numbers.current {
    background: #ea9c00 !important;
    border-color: #ea9c00 !important;
    color: white !important;
  }

  /* Previous/Next button styling */
  .woocommerce-pagination ul li a.prev,
  .woocommerce-pagination ul li a.next,
  .shop-pagination a.prev,
  .shop-pagination a.next {
    font-weight: 600 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
  }

  /* Ensure pagination links are clickable */
  .woocommerce-pagination ul li a,
  .shop-pagination a {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 10 !important;
  }

  /* Fix for pagination link text */
  .woocommerce-pagination ul li a .bi,
  .shop-pagination a .bi {
    pointer-events: none !important;
  }

  /* Dots styling */
  .woocommerce-pagination ul li .dots,
  .shop-pagination .dots {
    background: transparent !important;
    border: none !important;
    color: #666 !important;
    cursor: default !important;
  }
}

/* Desktop pagination improvements */
@media (min-width: 768px) {

  .woocommerce-pagination,
  .shop-pagination {
    margin: 40px 0 !important;
  }

  .woocommerce-pagination ul li a,
  .woocommerce-pagination ul li span,
  .shop-pagination .page-numbers {
    min-width: 40px !important;
    height: 40px !important;
    border-radius: 6px !important;
    pointer-events: auto !important;
    cursor: pointer !important;
  }

  /* Ensure desktop pagination links work */
  .woocommerce-pagination ul li a:hover,
  .shop-pagination a:hover {
    background: #ea9c00 !important;
    border-color: #ea9c00 !important;
    color: white !important;
  }
}

/* ==========================================================================
   DEBUGGING HELPERS (Remove in production)
   ========================================================================== */

/* Uncomment these for debugging layout issues */
/*
@media (max-width: 767.98px) {
  * {
    outline: 1px solid red !important;
  }

  .container {
    background: rgba(255, 0, 0, 0.1) !important;
  }

  .row {
    background: rgba(0, 255, 0, 0.1) !important;
  }

  [class*="col-"] {
    background: rgba(0, 0, 255, 0.1) !important;
  }
}
*/