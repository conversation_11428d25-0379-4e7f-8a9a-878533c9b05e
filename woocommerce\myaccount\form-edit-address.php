<?php
/**
 * Edit address form - Modern Design
 *
 * This template displays the address edit form with a modern card-based design.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.3.0
 */

defined( 'ABSPATH' ) || exit;

$page_title = ( 'billing' === $load_address ) ? esc_html__( 'Billing address', 'woocommerce' ) : esc_html__( 'Shipping address', 'woocommerce' );

do_action( 'woocommerce_before_edit_account_address_form' ); ?>

<?php if ( ! $load_address ) : ?>
	<?php wc_get_template( 'myaccount/my-address.php' ); ?>
<?php else : ?>

	<div class="address-form-container">
		<div class="address-form-header">
			<i data-feather="edit-2" class="address-icon"></i>
			<h3 class="address-form-title"><?php echo apply_filters( 'woocommerce_my_account_edit_address_title', $page_title, $load_address ); ?></h3>
		</div>

		<form method="post" novalidate class="address-form">
			<div class="woocommerce-address-fields">
				<?php do_action( "woocommerce_before_edit_address_form_{$load_address}" ); ?>

				<div class="woocommerce-address-fields__field-wrapper address-form-fields">
					<?php
					// Group fields for better layout
					$grouped_fields = array();
					$full_width_fields = array( 'address_1', 'address_2', 'company' );
					$row_fields = array();

					foreach ( $address as $key => $field ) {
						if ( in_array( $key, $full_width_fields ) ) {
							$grouped_fields['full_width'][] = array( 'key' => $key, 'field' => $field );
						} else {
							$row_fields[] = array( 'key' => $key, 'field' => $field );
						}
					}

					// Group remaining fields in pairs
					$grouped_fields['rows'] = array_chunk( $row_fields, 2 );

					// Render full-width fields first
					if ( !empty( $grouped_fields['full_width'] ) ) {
						foreach ( $grouped_fields['full_width'] as $field_data ) {
							echo '<div class="address-form-row full-width">';
							echo '<div class="address-form-group">';

							// Customize field for our design
							$field_data['field']['class'][] = 'address-form-input';
							$field_data['field']['label_class'][] = 'address-form-label';

							woocommerce_form_field( $field_data['key'], $field_data['field'], wc_get_post_data_by_key( $field_data['key'], $field_data['field']['value'] ) );
							echo '</div>';
							echo '</div>';
						}
					}

					// Render paired fields
					if ( !empty( $grouped_fields['rows'] ) ) {
						foreach ( $grouped_fields['rows'] as $row ) {
							echo '<div class="address-form-row">';

							foreach ( $row as $field_data ) {
								echo '<div class="address-form-group">';

								// Customize field for our design
								$field_data['field']['class'][] = 'address-form-input';
								$field_data['field']['label_class'][] = 'address-form-label';

								woocommerce_form_field( $field_data['key'], $field_data['field'], wc_get_post_data_by_key( $field_data['key'], $field_data['field']['value'] ) );
								echo '</div>';
							}

							echo '</div>';
						}
					}
					?>
				</div>

				<?php do_action( "woocommerce_after_edit_address_form_{$load_address}" ); ?>

				<div class="address-form-actions">
					<a href="<?php echo esc_url( wc_get_endpoint_url( 'edit-address', '' ) ); ?>" class="address-btn address-btn-secondary">
						<i data-feather="arrow-left"></i>
						<?php esc_html_e( 'Back to Addresses', 'woocommerce' ); ?>
					</a>
					<button type="submit" class="address-btn address-btn-primary" name="save_address" value="<?php esc_attr_e( 'Save address', 'woocommerce' ); ?>">
						<i data-feather="save"></i>
						<?php esc_html_e( 'Save address', 'woocommerce' ); ?>
					</button>
					<?php wp_nonce_field( 'woocommerce-edit_address', 'woocommerce-edit-address-nonce' ); ?>
					<input type="hidden" name="action" value="edit_address" />
				</div>
			</div>
		</form>
	</div>

<?php endif; ?>

<?php do_action( 'woocommerce_after_edit_account_address_form' ); ?>

<style>
/* Override WooCommerce default form styles */
.address-form-fields .form-row {
	margin: 0 !important;
	padding: 0 !important;
}

.address-form-fields .form-row label {
	display: block;
	font-size: 14px;
	font-weight: 500;
	color: #374151;
	margin-bottom: 6px;
}

.address-form-fields .form-row input,
.address-form-fields .form-row select,
.address-form-fields .form-row textarea {
	width: 100%;
	padding: 10px 12px;
	border: 1px solid #d1d5db;
	border-radius: 6px;
	font-size: 14px;
	transition: border-color 0.2s ease;
	background: #fff;
}

.address-form-fields .form-row input:focus,
.address-form-fields .form-row select:focus,
.address-form-fields .form-row textarea:focus {
	outline: none;
	border-color: #ea9c00;
	box-shadow: 0 0 0 3px rgba(234, 156, 0, 0.1);
}

.address-form-fields .form-row .required {
	color: #ef4444;
}

/* Hide default WooCommerce styling */
.woocommerce-address-fields__field-wrapper .form-row {
	float: none !important;
	width: 100% !important;
	clear: both !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.address-form-row {
		grid-template-columns: 1fr !important;
		gap: 12px !important;
	}

	.address-form-actions {
		flex-direction: column !important;
	}

	.address-btn {
		width: 100% !important;
		justify-content: center !important;
	}
}
</style>
