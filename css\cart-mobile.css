/* Cart Mobile Responsive Styles */
/* Enhanced mobile responsiveness for WooCommerce cart page */

/* Touch-friendly improvements */
@media screen and (max-width: 991px) {
  /* Improve touch targets for tablets */
  .quantity-control button {
    min-width: 44px;
    min-height: 44px;
  }
  
  .cart-item-checkbox {
    transform: scale(1.2);
    margin: 8px;
  }
  
  #select-all {
    transform: scale(1.2);
    margin: 8px;
  }
}

/* Mobile-first cart improvements */
@media screen and (max-width: 768px) {
  /* Cart container mobile spacing */
  .cart-container.container {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  /* Mobile cart header improvements */
  .cart-header.row {
    margin: 0;
    padding: 15px 0;
  }
  
  .cart-heder-checkbox.col-6,
  .cart-header-delbtn.col-6 {
    padding: 0;
  }
  
  /* Select all checkbox mobile styling */
  .cart-heder-checkbox {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 1rem;
    font-weight: 500;
  }
  
  .cart-heder-checkbox input[type="checkbox"] {
    margin-right: 10px;
  }
  
  /* Delete button mobile styling */
  #delete-selected-cart-items {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #f04438;
    border-radius: 8px;
    background: transparent;
    color: #f04438;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  #delete-selected-cart-items:hover {
    background: #f04438;
    color: white;
  }
  
  /* Mobile cart item card design */
  .shop_table.cart tbody tr.cart_item {
    display: block;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 15px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
    transition: box-shadow 0.3s ease;
  }
  
  .shop_table.cart tbody tr.cart_item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  /* Mobile product layout optimization */
  .product-info.d-flex {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start !important;
    gap: 12px !important;
    margin-top: 35px; /* Space for price and checkbox */
  }
  
  /* Product thumbnail mobile optimization */
  .woocommerce-cart table.cart .product-thumbnail {
    width: 80px !important;
    margin-right: 0 !important;
    flex-shrink: 0;
  }
  
  .product-thumbnail a {
    display: block;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .product-thumbnail img {
    width: 80px !important;
    height: 80px !important;
    object-fit: cover;
    border-radius: 8px;
  }
  
  /* Product details mobile layout */
  .woocommerce-cart table.cart .product-details {
    flex: 1;
    padding: 0 !important;
    min-width: 0; /* Prevent flex item from overflowing */
  }
  
  .product-details .product-name {
    font-size: 1rem !important;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 8px;
    color: #1d2939;
    text-decoration: none;
    display: block;
    word-wrap: break-word;
  }
  
  .product-details .product-name:hover {
    color: #ea9c00;
  }
  
  /* Vendor store link mobile styling */
  .product-details .vendor-store-link-btn {
    margin: 8px 0;
  }
  
  .product-details .vendor-store-link-btn a {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px !important;
    font-size: 0.85rem;
    border-radius: 6px;
    background-color: #f4f7fd;
    color: #1d2939;
    text-decoration: none;
    transition: all 0.3s ease;
  }
  
  .product-details .vendor-store-link-btn a:hover {
    background-color: #ea9c00;
    color: white;
  }
  
  /* Product reviews mobile styling */
  .product-details .product-reviews {
    margin: 8px 0;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .product-details .product-reviews .star-rating {
    display: flex;
    align-items: center;
  }
  
  /* Mobile quantity control optimization */
  .quantity-control.mt-2.mb-2 {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-top: 15px !important;
    margin-bottom: 0 !important;
    padding: 12px 0;
    border-top: 1px solid #f0f0f0;
    gap: 10px;
  }
  
  .quantity-control .decrease-qty,
  .quantity-control .increase-qty {
    width: 40px !important;
    height: 40px !important;
    border-radius: 8px !important;
    border: none;
    background: #ea9c00;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .quantity-control .decrease-qty:hover,
  .quantity-control .increase-qty:hover {
    background: #d18a00;
    transform: scale(1.05);
  }
  
  .quantity-control .qty-input {
    width: 60px !important;
    height: 40px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    background: white;
  }
  
  .quantity-control .qty-input:focus {
    outline: none;
    border-color: #ea9c00;
    box-shadow: 0 0 0 3px rgba(234, 156, 0, 0.1);
  }
  
  /* Delete item button mobile styling */
  .quantity-control .delete-btn {
    margin-left: 0 !important;
  }
  
  .quantity-control .remove {
    display: inline-flex !important;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem !important;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    text-decoration: none !important;
    color: #f04438;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .quantity-control .remove:hover {
    background: #f04438;
    color: white;
  }
  
  /* Mobile price positioning */
  .product-subtotal {
    position: absolute !important;
    top: 15px;
    left: 15px;
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    color: #ea9c00 !important;
    background: rgba(234, 156, 0, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    border: none;
  }
  
  /* Mobile checkbox positioning */
  .product-select {
    position: absolute !important;
    top: 15px;
    right: 15px;
    text-align: center;
    z-index: 2;
  }
  
  .product-select input[type="checkbox"] {
    transform: scale(1.3);
    margin: 0;
  }
}

/* Extra small mobile devices */
@media screen and (max-width: 480px) {
  .cart-container.container {
    padding-left: 10px;
    padding-right: 10px;
  }
  
  .shop_table.cart tbody tr.cart_item {
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .product-thumbnail,
  .product-thumbnail img {
    width: 70px !important;
    height: 70px !important;
  }
  
  .product-details .product-name {
    font-size: 0.95rem !important;
    line-height: 1.2;
  }
  
  .quantity-control .decrease-qty,
  .quantity-control .increase-qty {
    width: 36px !important;
    height: 36px !important;
    font-size: 14px;
  }
  
  .quantity-control .qty-input {
    width: 50px !important;
    height: 36px;
    font-size: 14px;
  }
  
  .product-subtotal {
    font-size: 1rem !important;
    padding: 3px 6px;
  }
}

/* Landscape orientation for mobile */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .cart-container {
    flex-direction: row !important;
  }
  
  .woocommerce-cart-form {
    width: 65% !important;
    order: 1;
  }
  
  .cart-sidebar {
    width: 35% !important;
    order: 2;
    padding: 15px;
  }
  
  .shop_table.cart tbody tr.cart_item {
    padding: 12px;
    margin-bottom: 10px;
  }
  
  .product-info.d-flex {
    margin-top: 25px;
  }
  
  .quantity-control.mt-2.mb-2 {
    padding: 8px 0;
  }
}
