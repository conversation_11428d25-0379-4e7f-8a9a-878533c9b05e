<?php
/**
 * My Addresses - Modern Design
 *
 * This template displays customer addresses with a modern card-based design.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.3.0
 */

defined( 'ABSPATH' ) || exit;

$customer_id = get_current_user_id();

if ( ! wc_ship_to_billing_address_only() && wc_shipping_enabled() ) {
	$get_addresses = apply_filters(
		'woocommerce_my_account_get_addresses',
		array(
			'billing'  => __( 'Billing address', 'woocommerce' ),
			'shipping' => __( 'Shipping address', 'woocommerce' ),
		),
		$customer_id
	);
} else {
	$get_addresses = apply_filters(
		'woocommerce_my_account_get_addresses',
		array(
			'billing' => __( 'Billing address', 'woocommerce' ),
		),
		$customer_id
	);
}

// Get customer data for display
$customer = new WC_Customer( $customer_id );
$user_info = get_userdata( $customer_id );
$customer_name = trim( $customer->get_first_name() . ' ' . $customer->get_last_name() );
if ( empty( $customer_name ) ) {
	$customer_name = $user_info->display_name;
}
?>

<div class="address-management">
  <h2><?php esc_html_e( 'My Addresses', 'woocommerce' ); ?></h2>

  <p class="address-description">
    <?php echo apply_filters( 'woocommerce_my_account_my_address_description', esc_html__( 'The following addresses will be used on the checkout page by default.', 'woocommerce' ) ); ?>
  </p>

  <div class="address-list">
    <?php foreach ( $get_addresses as $name => $address_title ) : ?>
    <?php
        $address = wc_get_account_formatted_address( $name );

        // Get address data using proper WooCommerce methods
        $address_data = array();
        $address_data['first_name'] = get_user_meta( $customer_id, $name . '_first_name', true );
        $address_data['last_name'] = get_user_meta( $customer_id, $name . '_last_name', true );
        $address_data['company'] = get_user_meta( $customer_id, $name . '_company', true );
        $address_data['address_1'] = get_user_meta( $customer_id, $name . '_address_1', true );
        $address_data['address_2'] = get_user_meta( $customer_id, $name . '_address_2', true );
        $address_data['city'] = get_user_meta( $customer_id, $name . '_city', true );
        $address_data['state'] = get_user_meta( $customer_id, $name . '_state', true );
        $address_data['postcode'] = get_user_meta( $customer_id, $name . '_postcode', true );
        $address_data['country'] = get_user_meta( $customer_id, $name . '_country', true );
        $address_data['phone'] = get_user_meta( $customer_id, $name . '_phone', true );
        $address_data['email'] = get_user_meta( $customer_id, $name . '_email', true );

        // Use address name if available, otherwise use customer name
        $display_name = '';
        if ( !empty( $address_data['first_name'] ) || !empty( $address_data['last_name'] ) ) {
            $display_name = trim( $address_data['first_name'] . ' ' . $address_data['last_name'] );
        }
        if ( empty( $display_name ) ) {
            $display_name = $customer_name;
        }

        // Prepare address data for the card
        $card_data = array(
            'name' => $display_name,
            'street' => '',
            'postal_code' => $address_data['postcode'] ?? '',
            'country' => $address_data['country'] ?? '',
            'phone' => $address_data['phone'] ?? '',
            'email' => $address_data['email'] ?? '',
            'company' => $address_data['company'] ?? '',
            'is_default' => true // WooCommerce only has one address per type
        );

        // Build street address
        $street_parts = array();
        if ( !empty( $address_data['address_1'] ) ) {
            $street_parts[] = $address_data['address_1'];
        }
        if ( !empty( $address_data['address_2'] ) ) {
            $street_parts[] = $address_data['address_2'];
        }
        if ( !empty( $address_data['city'] ) ) {
            $street_parts[] = $address_data['city'];
        }
        if ( !empty( $address_data['state'] ) ) {
            $street_parts[] = $address_data['state'];
        }
        $card_data['street'] = implode( ', ', array_filter( $street_parts ) );

        // Convert country code to name
        if ( !empty( $card_data['country'] ) ) {
            $countries = WC()->countries->get_countries();
            $card_data['country'] = $countries[ $card_data['country'] ] ?? $card_data['country'];
        }

        // Check if address has any data
        $has_address_data = !empty( $card_data['street'] ) || !empty( $card_data['postal_code'] ) || !empty( $card_data['country'] ) || !empty( $card_data['phone'] ) || !empty( $card_data['company'] ) || !empty( $card_data['email'] );
        ?>

    <div class="address-card address-card-with-actions" data-address-type="<?php echo esc_attr( $name ); ?>">
      <div class="address-card-actions">
        <a href="<?php echo esc_url( wc_get_endpoint_url( 'edit-address', $name ) ); ?>" class="address-action-btn"
          title="<?php esc_attr_e( 'Edit Address', 'woocommerce' ); ?>">
          <i data-feather="edit-2"></i>
        </a>
      </div>

      <div class="address-card-header">
        <i data-feather="user" class="address-icon"></i>
        <h3 class="address-name"><?php echo esc_html( $customer_name ); ?></h3>
      </div>

      <div class="address-card-type">
        <span class="address-type-badge"><?php echo esc_html( $address_title ); ?></span>
      </div>

      <?php if ( $address && $has_address_data ) : ?>
      <div class="address-details">
        <?php if ( !empty( $card_data['company'] ) ) : ?>
        <div class="address-item">
          <i data-feather="briefcase" class="address-item-icon"></i>
          <span class="address-item-text address-company"><?php echo esc_html( $card_data['company'] ); ?></span>
        </div>
        <?php endif; ?>

        <?php if ( !empty( $card_data['street'] ) ) : ?>
        <div class="address-item">
          <i data-feather="map-pin" class="address-item-icon"></i>
          <span class="address-item-text address-street"><?php echo esc_html( $card_data['street'] ); ?></span>
        </div>
        <?php endif; ?>

        <?php if ( !empty( $card_data['postal_code'] ) ) : ?>
        <div class="address-item">
          <i data-feather="mail" class="address-item-icon"></i>
          <span class="address-item-text">Zip Code <?php echo esc_html( $card_data['postal_code'] ); ?></span>
        </div>
        <?php endif; ?>

        <?php if ( !empty( $card_data['country'] ) ) : ?>
        <div class="address-item">
          <i data-feather="flag" class="address-item-icon"></i>
          <span class="address-item-text address-country"><?php echo esc_html( $card_data['country'] ); ?></span>
        </div>
        <?php endif; ?>

        <?php if ( !empty( $card_data['phone'] ) ) : ?>
        <div class="address-item">
          <i data-feather="phone" class="address-item-icon"></i>
          <span class="address-item-text address-phone"><?php echo esc_html( $card_data['phone'] ); ?></span>
        </div>
        <?php endif; ?>

        <?php if ( !empty( $card_data['email'] ) ) : ?>
        <div class="address-item">
          <i data-feather="at-sign" class="address-item-icon"></i>
          <span class="address-item-text address-email"><?php echo esc_html( $card_data['email'] ); ?></span>
        </div>
        <?php endif; ?>
      </div>
      <?php else : ?>
      <div class="address-empty">
        <div class="address-empty-icon">
          <i data-feather="map-pin"></i>
        </div>
        <p class="address-empty-text">
          <?php esc_html_e( 'You have not set up this type of address yet.', 'woocommerce' ); ?></p>
        <a href="<?php echo esc_url( wc_get_endpoint_url( 'edit-address', $name ) ); ?>"
          class="address-btn address-btn-primary">
          <i data-feather="plus"></i>
          <?php printf( esc_html__( 'Add %s', 'woocommerce' ), esc_html( $address_title ) ); ?>
        </a>
      </div>
      <?php endif; ?>

      <?php
                /**
                 * Used to output content after core address fields.
                 *
                 * @param string $name Address type.
                 * @since 8.7.0
                 */
                do_action( 'woocommerce_my_account_after_my_address', $name );
                ?>
    </div>
    <?php endforeach; ?>
  </div>
</div>

<style>
.address-management {
  max-width: 1200px;
  margin: 0 auto;
}

.address-management h2 {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 10px;
}

.address-description {
  color: #6b7280;
  margin-bottom: 30px;
  font-size: 16px;
}

.address-type-badge {
  display: inline-block;
  background: #ea9c00;
  color: white;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 20px;
  margin-bottom: 16px;
}

.address-empty {
  text-align: center;
  padding: 40px 20px;
}

.address-empty-icon {
  width: 60px;
  height: 60px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: #9ca3af;
}

.address-empty-icon i {
  width: 24px;
  height: 24px;
}

.address-empty-text {
  color: #6b7280;
  margin-bottom: 20px;
  font-size: 14px;
}

@media (max-width: 768px) {
  .address-management h2 {
    font-size: 24px;
  }

  .address-description {
    font-size: 14px;
  }
}
</style>