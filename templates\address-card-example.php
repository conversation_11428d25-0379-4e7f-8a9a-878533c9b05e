<?php
/**
 * Address Card Example Template
 * 
 * This template shows how to implement the address card design
 * that matches the provided image design.
 */

// Example address data
$address_data = array(
    'name' => 'Achref Maher',
    'street' => 'King Khalid Street, Dammam, Eastern Province',
    'postal_code' => 'Zip Code 125456',
    'country' => 'Saudi Arabia',
    'phone' => '+966 03 833 3444',
    'is_default' => true
);
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Address Card Design</title>
    <link rel="stylesheet" href="../css/address-card.css">
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
</head>
<body style="padding: 40px; background: #f9fafb; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">

    <h2>Address Card Design Examples</h2>

    <!-- Basic Address Card -->
    <div class="address-list">
        <div class="address-card">
            <?php if ($address_data['is_default']) : ?>
                <div class="address-default-badge">
                    <i data-feather="check-circle" class="icon"></i>
                    Default
                </div>
            <?php endif; ?>
            
            <div class="address-card-header">
                <i data-feather="user" class="address-icon"></i>
                <h3 class="address-name"><?php echo esc_html($address_data['name']); ?></h3>
            </div>
            
            <div class="address-details">
                <div class="address-item">
                    <i data-feather="map-pin" class="address-item-icon"></i>
                    <span class="address-item-text address-street"><?php echo esc_html($address_data['street']); ?></span>
                </div>
                
                <div class="address-item">
                    <i data-feather="mail" class="address-item-icon"></i>
                    <span class="address-item-text"><?php echo esc_html($address_data['postal_code']); ?></span>
                </div>
                
                <div class="address-item">
                    <i data-feather="flag" class="address-item-icon"></i>
                    <span class="address-item-text address-country"><?php echo esc_html($address_data['country']); ?></span>
                </div>
                
                <div class="address-item">
                    <i data-feather="phone" class="address-item-icon"></i>
                    <span class="address-item-text address-phone"><?php echo esc_html($address_data['phone']); ?></span>
                </div>
            </div>
        </div>

        <!-- Address Card with Actions -->
        <div class="address-card address-card-with-actions">
            <div class="address-card-actions">
                <button class="address-action-btn" title="Edit Address">
                    <i data-feather="edit-2"></i>
                </button>
                <button class="address-action-btn delete" title="Delete Address">
                    <i data-feather="trash-2"></i>
                </button>
            </div>
            
            <div class="address-card-header">
                <i data-feather="user" class="address-icon"></i>
                <h3 class="address-name">John Smith</h3>
            </div>
            
            <div class="address-details">
                <div class="address-item">
                    <i data-feather="map-pin" class="address-item-icon"></i>
                    <span class="address-item-text address-street">123 Main Street, New York, NY</span>
                </div>
                
                <div class="address-item">
                    <i data-feather="mail" class="address-item-icon"></i>
                    <span class="address-item-text">Zip Code 10001</span>
                </div>
                
                <div class="address-item">
                    <i data-feather="flag" class="address-item-icon"></i>
                    <span class="address-item-text address-country">United States</span>
                </div>
                
                <div class="address-item">
                    <i data-feather="phone" class="address-item-icon"></i>
                    <span class="address-item-text address-phone">****** 123 4567</span>
                </div>
            </div>
        </div>

        <!-- Compact Address Card -->
        <div class="address-card compact">
            <div class="address-card-header">
                <i data-feather="user" class="address-icon"></i>
                <h3 class="address-name">Sarah Johnson</h3>
            </div>
            
            <div class="address-details">
                <div class="address-item">
                    <i data-feather="map-pin" class="address-item-icon"></i>
                    <span class="address-item-text address-street">456 Oak Avenue, London</span>
                </div>
                
                <div class="address-item">
                    <i data-feather="mail" class="address-item-icon"></i>
                    <span class="address-item-text">SW1A 1AA</span>
                </div>
                
                <div class="address-item">
                    <i data-feather="flag" class="address-item-icon"></i>
                    <span class="address-item-text address-country">United Kingdom</span>
                </div>
                
                <div class="address-item">
                    <i data-feather="phone" class="address-item-icon"></i>
                    <span class="address-item-text address-phone">+44 20 7946 0958</span>
                </div>
            </div>
        </div>
    </div>

    <h3>Address Form Example</h3>
    
    <!-- Address Form -->
    <div class="address-form-container" style="max-width: 600px;">
        <div class="address-form-header">
            <i data-feather="plus" class="address-icon"></i>
            <h3 class="address-form-title">Add New Address</h3>
        </div>
        
        <form>
            <div class="address-form-row">
                <div class="address-form-group">
                    <label class="address-form-label">Full Name</label>
                    <input type="text" class="address-form-input" placeholder="Enter full name">
                </div>
                <div class="address-form-group">
                    <label class="address-form-label">Phone Number</label>
                    <input type="tel" class="address-form-input" placeholder="Enter phone number">
                </div>
            </div>
            
            <div class="address-form-row full-width">
                <div class="address-form-group">
                    <label class="address-form-label">Street Address</label>
                    <input type="text" class="address-form-input" placeholder="Enter street address">
                </div>
            </div>
            
            <div class="address-form-row">
                <div class="address-form-group">
                    <label class="address-form-label">City</label>
                    <input type="text" class="address-form-input" placeholder="Enter city">
                </div>
                <div class="address-form-group">
                    <label class="address-form-label">Postal Code</label>
                    <input type="text" class="address-form-input" placeholder="Enter postal code">
                </div>
            </div>
            
            <div class="address-form-row full-width">
                <div class="address-form-group">
                    <label class="address-form-label">Country</label>
                    <select class="address-form-input">
                        <option>Select Country</option>
                        <option>Saudi Arabia</option>
                        <option>United States</option>
                        <option>United Kingdom</option>
                    </select>
                </div>
            </div>
            
            <div class="address-form-actions">
                <button type="button" class="address-btn address-btn-secondary">
                    <i data-feather="x"></i>
                    Cancel
                </button>
                <button type="submit" class="address-btn address-btn-primary">
                    <i data-feather="save"></i>
                    Save Address
                </button>
            </div>
        </form>
    </div>

    <script>
        // Initialize Feather icons
        feather.replace();
        
        // Add some interactivity
        document.querySelectorAll('.address-action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.title;
                alert(`${action} clicked!`);
            });
        });
    </script>

</body>
</html>
