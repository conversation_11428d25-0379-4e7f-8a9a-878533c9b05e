/* Account Dashboard Styles */

/* Bootstrap Icons */
@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.min.css");

/* Main Dashboard Container */
.account-dashboard {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    background-color: #f4f7fd;
    border-radius: 12px;
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #1d2939;
    margin: 0;
}

/* Status Cards */
.status-cards {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.status-card {
    background-color: #fff9e6;
    border-radius: 12px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.status-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-size: 24px;
}

.status-icon.shipped {
    background-color: rgba(234, 156, 0, 0.1);
    color: #ea9c00;
}

.status-icon.confirmed {
    background-color: rgba(18, 183, 106, 0.1);
    color: #12b76a;
}

.status-icon.shipping {
    background-color: rgba(234, 156, 0, 0.1);
    color: #ea9c00;
}

.status-icon.returns {
    background-color: rgba(80, 102, 169, 0.1);
    color: #5066a9;
}

.status-icon.canceled {
    background-color: rgba(240, 68, 56, 0.1);
    color: #f04438;
}

.status-content h3 {
    font-size: 14px;
    font-weight: 500;
    color: #1d2939;
    margin: 0 0 5px;
}

.status-count {
    font-size: 24px;
    font-weight: 600;
    color: #1d2939;
}

/* Account Stats */
.account-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.wallet-card,
.points-card {
    background-color: #fff9e6;
    border-radius: 12px;
    padding: 20px;
}

.wallet-header,
.points-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.wallet-header i,
.points-header i {
    font-size: 20px;
    margin-right: 10px;
    color: #ea9c00;
}

.wallet-header span,
.points-header span {
    font-size: 16px;
    font-weight: 500;
    color: #1d2939;
}

.wallet-amount,
.points-amount {
    display: flex;
    align-items: baseline;
}

.wallet-amount .amount,
.points-amount .amount {
    font-size: 36px;
    font-weight: 700;
    color: #1d2939;
    margin-right: 5px;
}

.wallet-amount .currency,
.points-amount .label {
    font-size: 16px;
    font-weight: 500;
    color: #98a2b3;
}

.points-info {
    margin-top: 10px;
    font-size: 14px;
    color: #98a2b3;
}

/* Products Summary */
.products-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.product-summary-card {
    background-color: #fff9e6;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.product-summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
}

.product-summary-icon.cart {
    background-color: rgba(234, 156, 0, 0.1);
    color: #ea9c00;
}

.product-summary-icon.wishlist {
    background-color: rgba(240, 68, 56, 0.1);
    color: #f04438;
}

.product-summary-icon.ordered {
    background-color: rgba(18, 183, 106, 0.1);
    color: #12b76a;
}

.product-summary-count {
    font-size: 24px;
    font-weight: 700;
    color: #1d2939;
    margin-bottom: 5px;
}

.product-summary-label {
    font-size: 16px;
    font-weight: 500;
    color: #1d2939;
    margin-bottom: 5px;
}

.product-summary-sublabel {
    font-size: 14px;
    color: #98a2b3;
}

/* Account Details */
.account-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.wallet-details,
.points-details {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(152, 162, 179, 0.2);
}

.wallet-details-header,
.points-details-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.wallet-details-header i,
.points-details-header i {
    font-size: 20px;
    margin-right: 10px;
    color: #ea9c00;
}

.wallet-details-header span,
.points-details-header span {
    font-size: 16px;
    font-weight: 500;
    color: #1d2939;
}

.wallet-details-amount,
.points-details-amount {
    display: flex;
    align-items: baseline;
}

.wallet-details-amount .amount,
.points-details-amount .amount {
    font-size: 36px;
    font-weight: 700;
    color: #1d2939;
    margin-right: 5px;
}

.wallet-details-amount .currency,
.points-details-amount .label {
    font-size: 16px;
    font-weight: 500;
    color: #98a2b3;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1d2939;
    margin: 0;
}

.view-all {
    font-size: 14px;
    font-weight: 500;
    color: #ea9c00;
    text-decoration: none;
}

.view-all:hover {
    text-decoration: underline;
}

/* Wishlist Section */
.wishlist-section {
    margin-bottom: 30px;
}

.wishlist-products {
    display: grid;
    /* grid-template-columns: repeat(2, 1fr); */
    gap: 20px;
}

.wishlist-product {
    display: flex;
    background-color: #fff;
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(152, 162, 179, 0.2);
}

.product-image {
    width: 80px;
    margin-right: 15px;
}

.product-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

.product-info {
    flex: 1;
}

.product-info h4 {
    font-size: 16px;
    font-weight: 500;
    color: #1d2939;
    margin: 0 0 5px;
}

.product-price {
    font-size: 16px;
    font-weight: 600;
    color: #1d2939;
    margin-bottom: 10px;
}

.product-meta {
    font-size: 14px;
    color: #98a2b3;
}

.product-actions,
.order-actions,
.tender-actions {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.button {
    display: inline-block;
    background-color: #ea9c00;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 8px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.button:hover {
    background-color: #d08a00;
    color: #fff;
}

/* Orders Section */
.orders-section {
    margin-bottom: 30px;
}

.recent-orders {
    display: grid;
    /* grid-template-columns: repeat(2, 1fr); */
    gap: 20px;
}

.order-item {
    display: flex;
    background-color: #fff;
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(152, 162, 179, 0.2);
}

.order-info {
    flex: 1;
}

.order-info h4 {
    font-size: 16px;
    font-weight: 500;
    color: #1d2939;
    margin: 0 0 5px;
}

.order-meta {
    font-size: 14px;
    color: #98a2b3;
    margin-bottom: 10px;
}

.order-status {
    display: inline-block;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    text-transform: uppercase;
}

.order-status.completed {
    background-color: rgba(18, 183, 106, 0.1);
    color: #12b76a;
}

.order-status.processing {
    background-color: rgba(234, 156, 0, 0.1);
    color: #ea9c00;
}

.order-status.on-hold {
    background-color: rgba(80, 102, 169, 0.1);
    color: #5066a9;
}

.order-status.cancelled {
    background-color: rgba(240, 68, 56, 0.1);
    color: #f04438;
}

.order-status.refunded {
    background-color: rgba(152, 162, 179, 0.1);
    color: #98a2b3;
}

/* Tenders Section */
.tenders-section {
    margin-bottom: 30px;
}

.tenders-list {
    display: grid;
    /* grid-template-columns: repeat(2, 1fr); */
    gap: 20px;
}

.tender-item {
    display: flex;
    background-color: #fff;
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(152, 162, 179, 0.2);
}

.tender-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background-color: rgba(234, 156, 0, 0.1);
    color: #ea9c00;
    font-size: 20px;
}

.tender-info {
    flex: 1;
}

.tender-info h4 {
    font-size: 16px;
    font-weight: 500;
    color: #1d2939;
    margin: 0 0 5px;
}

.tender-meta {
    font-size: 14px;
    color: #98a2b3;
    margin-bottom: 10px;
}

.tender-dates {
    display: flex;
    gap: 10px;
    font-size: 14px;
    color: #98a2b3;
}

.tender-stats {
    margin: 0 15px;
    min-width: 150px;
}

.tender-stat {
    margin-bottom: 10px;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #98a2b3;
}

.stat-value {
    font-size: 14px;
    font-weight: 500;
    color: #1d2939;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .status-cards {
        grid-template-columns: repeat(3, 1fr);
    }

    .wishlist-products,
    .recent-orders,
    .tenders-list {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .status-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .account-stats,
    .products-summary,
    .account-details {
        grid-template-columns: 1fr;
    }

    .wishlist-product,
    .order-item,
    .tender-item {
        flex-direction: column;
    }

    .product-image {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
    }

    .product-actions,
    .order-actions,
    .tender-actions {
        margin-left: 0;
        margin-top: 15px;
    }

    .tender-stats {
        margin: 15px 0;
    }
}

@media (max-width: 576px) {
    .status-cards {
        grid-template-columns: 1fr;
    }

    .dashboard-header h2 {
        font-size: 24px;
    }

    .account-dashboard {
        padding: 15px;
    }

    .woocommerce-MyAccount-content {
        padding: 15px;
    }
}

/* Additional container responsive styles */
@media (max-width: 768px) {

    /* Adjust container layout for mobile */
    .container .row {
        flex-direction: column;
    }

    .container .col-md-3,
    .container .col-md-9 {
        width: 100%;
    }
}

/* WooCommerce Account Navigation Customization */
.woocommerce-MyAccount-navigation {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    width: 100% !important;
}

.woocommerce-MyAccount-navigation ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.woocommerce-MyAccount-navigation li {
    border-bottom: 1px solid rgba(152, 162, 179, 0.2);
}

.woocommerce-MyAccount-navigation li:last-child {
    border-bottom: none;
}

.woocommerce-MyAccount-navigation a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #1d2939;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.woocommerce-MyAccount-navigation a:hover {
    background-color: rgba(234, 156, 0, 0.05);
    color: #ea9c00;
}

.woocommerce-MyAccount-navigation li.is-active a {
    background-color: rgba(234, 156, 0, 0.1);
    color: #ea9c00;
    border-left: 3px solid #ea9c00;
}

.woocommerce-MyAccount-navigation a i {
    margin-right: 10px;
    font-size: 18px;
    width: 24px;
    text-align: center;
}

/* Remove the old icon method since we're using <i> elements now */
.woocommerce-MyAccount-navigation a::before {
    content: none;
}

.woocommerce-MyAccount-navigation-link--dashboard a::before,
.woocommerce-MyAccount-navigation-link--orders a::before,
.woocommerce-MyAccount-navigation-link--downloads a::before,
.woocommerce-MyAccount-navigation-link--edit-address a::before,
.woocommerce-MyAccount-navigation-link--edit-account a::before,
.woocommerce-MyAccount-navigation-link--wishlist a::before,
.woocommerce-MyAccount-navigation-link--customer-logout a::before {
    content: none;
}

/* WooCommerce Content Area */
.woocommerce-MyAccount-content {
    background-color: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    width: 100% !important;
}

/* Container Styles */
.container .account-dashboard {
    padding: 0;
    background-color: transparent;
}

/* Add some spacing for larger screens */
@media (min-width: 1200px) {
    .container {
        max-width: 1350px;
    }

    .woocommerce-MyAccount-navigation {
        margin-right: 15px;
    }
}