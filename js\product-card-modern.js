/**
 * JavaScript for Modern Product Card
 */
jQuery(document).ready(function($) {
    // Refresh button functionality
    $('.btn-refresh').on('click', function(e) {
        e.preventDefault();
        var $card = $(this).closest('.product-card-modern');

        // Add rotation animation
        $(this).addClass('rotating');

        // Simulate refresh with animation
        $card.addClass('refreshing');

        // Remove classes after animation completes
        setTimeout(function() {
            $('.btn-refresh').removeClass('rotating');
            $card.removeClass('refreshing');
        }, 1000);
    });

    // Add to cart functionality
    $('.btn-add-to-cart').on('click', function(e) {
        e.preventDefault();

        var $button = $(this);
        var productId = $button.data('product_id');

        // Show loading state
        $button.addClass('loading');
        $button.text('Adding...');

        // AJAX add to cart
        $.ajax({
            type: 'POST',
            url: wc_add_to_cart_params.ajax_url,
            data: {
                action: 'woocommerce_ajax_add_to_cart',
                product_id: productId,
                quantity: 1
            },
            success: function(response) {
                $button.removeClass('loading');
                $button.html('<i class="bi bi-check-circle"></i> Added to cart');

                // Reset button after 2 seconds
                setTimeout(function() {
                    $button.html('<i class="bi bi-cart-plus"></i> Add to cart');
                }, 2000);

                // Update cart fragments
                $(document.body).trigger('wc_fragment_refresh');
            },
            error: function() {
                $button.removeClass('loading');
                $button.html('<i class="bi bi-exclamation-circle"></i> Error');

                // Reset button after 2 seconds
                setTimeout(function() {
                    $button.html('<i class="bi bi-cart-plus"></i> Add to cart');
                }, 2000);
            }
        });
    });

    // Toggle wishlist functionality
    $('.btn-toggle-wishlist').on('click', function(e) {
        e.preventDefault();

        var $button = $(this);
        var productId = $button.data('product_id');
        var isInWishlist = $button.hasClass('in-wishlist');

        // Show loading state
        $button.addClass('loading');

        // If YITH Wishlist is active, use its functionality
        if (typeof yith_wcwl_l10n !== 'undefined') {
            $.ajax({
                type: 'POST',
                url: yith_wcwl_l10n.ajax_url,
                data: {
                    action: isInWishlist ? 'remove_from_wishlist' : 'add_to_wishlist',
                    product_id: productId,
                    context: 'frontend'
                },
                success: function(response) {
                    $button.removeClass('loading');

                    if (isInWishlist) {
                        $button.removeClass('in-wishlist');
                        $button.html('<i class="bi bi-heart"></i> Add to favorite');
                    } else {
                        $button.addClass('in-wishlist');
                        $button.html('<i class="bi bi-x-circle"></i> Remove from favorite');
                    }
                },
                error: function() {
                    $button.removeClass('loading');
                }
            });
        } else {
            // Simple toggle for demo purposes
            setTimeout(function() {
                $button.removeClass('loading');

                if (isInWishlist) {
                    $button.removeClass('in-wishlist');
                    $button.html('<i class="bi bi-heart"></i> Add to favorite');
                } else {
                    $button.addClass('in-wishlist');
                    $button.html('<i class="bi bi-x-circle"></i> Remove from favorite');
                }
            }, 500);
        }
    });

    // Add CSS styles (no animations)
    $('<style>')
        .text(`
            .btn-refresh.rotating i {
                /* No rotation animation */
            }

            .product-card-modern.refreshing {
                opacity: 0.7;
            }

            .btn-add-to-cart.loading,
            .btn-toggle-wishlist.loading {
                opacity: 0.7;
                pointer-events: none;
            }
        `)
        .appendTo('head');
});
