<?php
/**
 * Template Name: Custom WooCommerce Shop
 *
 * This is a custom page template for displaying the WooCommerce shop.
 *
 * @package YourTheme
 * @subpackage Templates
 */

get_header(); // Include the header of your theme

?>

<div class="container mx-auto px-4 py-6 flex flex-col md:flex-row gap-6">
  <aside class="md:w-1/4">
    <div class="filter-group">
      <h4 class="text-lg font-semibold text-gray-800 mb-4">Categories</h4>
      <?php
            $categories = get_terms( array(
                'taxonomy' => 'product_cat',
                'hide_empty' => true,
            ) );
            if ( ! empty( $categories ) && ! is_wp_error( $categories ) ) {
                echo '<ul class="space-y-2">';
                foreach ( $categories as $category ) {
                    echo '<li><a href="' . esc_url( get_term_link( $category ) ) . '" class="text-gray-700 hover:text-blue-600">' . $category->name . '</a></li>';
                }
                echo '</ul>';
            }
            ?>
    </div>

    <div class="filter-group mt-6">
      <h4 class="text-lg font-semibold text-gray-800 mb-4">Price Range</h4>
      <?php
            // Use WooCommerce price filter
             echo do_shortcode( '[woocommerce_price_filter]' );
            ?>
    </div>

    <div class="filter-group mt-6">
      <h4 class="text-lg font-semibold text-gray-800 mb-4">Filter by Attribute</h4>
      <?php
                // Get the product attributes
                $attributes = wc_get_attribute_taxonomies();

                if ($attributes) {
                    foreach ($attributes as $attribute) {
                        $attribute_name = wc_attribute_label($attribute->attribute_name);
                        $attribute_terms = get_terms(array(
                            'taxonomy' => 'pa_' . $attribute->attribute_name, // e.g., pa_color
                            'hide_empty' => true,
                        ));

                        if (!empty($attribute_terms) && !is_wp_error($attribute_terms)) {
                            echo '<div class="filter-group">';
                            echo '<h4 class="text-md font-semibold text-gray-800 mb-2">' . $attribute_name . '</h4>';
                            echo '<ul class="space-y-2">';
                            foreach ($attribute_terms as $term) {
                                echo '<li><a href="' . esc_url(get_term_link($term)) . '" class="text-gray-700 hover:text-blue-600">' . $term->name . '</a></li>';
                            }
                            echo '</ul>';
                            echo '</div>';
                        }
                    }
                }
                ?>
    </div>
  </aside>

  <div id="primary" class="content-area md:w-3/4">
    <main id="main" class="site-main">


      <header class="woocommerce-products-header">
        <h1 class="woocommerce-products-header__title"><?php woocommerce_page_title(); ?></h1>
        <?php
                    /**
                     * Hook: woocommerce_archive_description.
                     *
                     * @hooked woocommerce_taxonomy_archive_description - 10
                     * @hooked woocommerce_product_archive_description - 10
                     */
                    do_action( 'woocommerce_archive_description' );
                    ?>
      </header>


      <?php
            // WP Query to show products
            $args = array(
                'post_type' => 'product',
                'posts_per_page' => -1, // Show all products
            );
            $products = new WP_Query( $args );

            if ( $products->have_posts() ) {

                woocommerce_product_loop_start();

                while ( $products->have_posts() ) {
                    $products->the_post();

                    // Ensure we have a valid product object for each iteration
                    global $product, $post;
                    if (empty($product) || !is_a($product, 'WC_Product')) {
                        $product = wc_get_product($post->ID);
                    }

                    // Only include the template if we have a valid product
                    if ($product && is_a($product, 'WC_Product') && $product->is_visible()) {
                        // Ensure the global product is properly set
                        $GLOBALS['product'] = $product;
                        wc_get_template_part( 'content', 'product' );
                    }
                }

                woocommerce_product_loop_end();

                // Add pagination if needed
                woocommerce_pagination();

                wp_reset_postdata(); // Reset post data
            } else {
                /**
                 * Hook: woocommerce_no_products_found.
                 *
                 * @hooked wc_no_products_found - 10
                 */
                do_action( 'woocommerce_no_products_found' );
            }
            ?>

    </main>
  </div>
</div>

<?php
get_footer(); // Include the footer of your theme