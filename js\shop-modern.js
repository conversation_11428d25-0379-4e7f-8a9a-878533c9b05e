/**
 * Modern Shop Page JavaScript
 */

(function($) {
  'use strict';

  // Initialize when document is ready
  $(document).ready(function() {
    // Product actions
    initProductActions();
    
    // Featured products navigation
    initFeaturedProductsNav();
  });

  /**
   * Initialize product action buttons
   */
  function initProductActions() {
    // Add to cart button
    $('.add-to-cart').on('click', function(e) {
      e.preventDefault();
      const productId = $(this).data('product-id');
      
      // Add loading state
      $(this).addClass('loading');
      
      // AJAX add to cart
      $.ajax({
        url: wc_add_to_cart_params.ajax_url,
        type: 'POST',
        data: {
          action: 'woocommerce_ajax_add_to_cart',
          product_id: productId,
          quantity: 1
        },
        success: function(response) {
          if (response.success) {
            // Show success message
            showNotification('Product added to cart', 'success');
            
            // Update cart fragments
            $(document.body).trigger('wc_fragment_refresh');
          } else {
            showNotification('Failed to add product to cart', 'error');
          }
        },
        error: function() {
          showNotification('Error occurred. Please try again.', 'error');
        },
        complete: function() {
          // Remove loading state
          $('.add-to-cart[data-product-id="' + productId + '"]').removeClass('loading');
        }
      });
    });

    // Add to wishlist button
    $('.add-to-wishlist').on('click', function(e) {
      e.preventDefault();
      const productId = $(this).data('product-id');
      
      // Toggle active state
      $(this).toggleClass('active');
      
      if ($(this).hasClass('active')) {
        showNotification('Product added to wishlist', 'success');
      } else {
        showNotification('Product removed from wishlist', 'info');
      }
    });

    // Quick view button
    $('.quick-view').on('click', function(e) {
      e.preventDefault();
      const productId = $(this).data('product-id');
      
      // Show loading state
      $(this).addClass('loading');
      
      // AJAX quick view
      $.ajax({
        url: wc_add_to_cart_params.ajax_url,
        type: 'POST',
        data: {
          action: 'product_quick_view',
          product_id: productId
        },
        success: function(response) {
          if (response.success) {
            // Show quick view modal
            $('body').append(response.data.html);
            $('#quick-view-modal').modal('show');
          } else {
            showNotification('Failed to load product details', 'error');
          }
        },
        error: function() {
          showNotification('Error occurred. Please try again.', 'error');
        },
        complete: function() {
          // Remove loading state
          $('.quick-view[data-product-id="' + productId + '"]').removeClass('loading');
        }
      });
    });
  }

  /**
   * Initialize featured products navigation
   */
  function initFeaturedProductsNav() {
    const $container = $('.featured-products-grid');
    const $prevBtn = $('.prev-btn');
    const $nextBtn = $('.next-btn');
    
    // Previous button click
    $prevBtn.on('click', function() {
      // Implement previous slide logic
      // This would typically involve a carousel/slider library
      showNotification('Previous products', 'info');
    });
    
    // Next button click
    $nextBtn.on('click', function() {
      // Implement next slide logic
      // This would typically involve a carousel/slider library
      showNotification('Next products', 'info');
    });
  }

  /**
   * Show notification
   * 
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error, info)
   */
  function showNotification(message, type) {
    // Check if notification container exists
    let $notificationContainer = $('.shop-notifications');
    
    // Create container if it doesn't exist
    if ($notificationContainer.length === 0) {
      $('body').append('<div class="shop-notifications"></div>');
      $notificationContainer = $('.shop-notifications');
    }
    
    // Create notification element
    const $notification = $('<div class="notification notification-' + type + '">' + message + '</div>');
    
    // Add to container
    $notificationContainer.append($notification);
    
    // Show notification
    setTimeout(function() {
      $notification.addClass('show');
    }, 10);
    
    // Remove notification after delay
    setTimeout(function() {
      $notification.removeClass('show');
      setTimeout(function() {
        $notification.remove();
      }, 300);
    }, 3000);
  }

})(jQuery);
