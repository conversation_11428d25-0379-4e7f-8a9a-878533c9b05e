<section class="row g-3 g-md-5 pb-md-5 mb-5 align-items-center">
  <div class="col-lg-6">
    <div class="masthead-followup-icon d-inline-block mb-3" style="--bg-rgb: var(--bd-teal-rgb);">
      {{ partial "icons/circle-square.svg" (dict "width" "32" "height" "32") }}
    </div>
    <h2 class="display-5 mb-3 fw-semibold lh-sm">Personalize it with Bootstrap&nbsp;Icons</h2>
    <p class="lead fw-normal">
      <a href="{{ .Site.Params.icons }}">Bootstrap Icons</a> is an open source SVG icon library featuring over 1,800 glyphs, with more added every release. They're designed to work in any project, whether you use Bootstrap itself or not. Use them as SVGs or icon fonts—both options give you vector scaling and easy customization via CSS.
    </p>
    <p class="d-flex justify-content-start lead fw-normal mb-md-0">
      <a href="{{ .Site.Params.icons }}" class="icon-link icon-link-hover fw-semibold">
        Get Bootstrap Icons
        <svg class="bi"><use xlink:href="#arrow-right"></use></svg>
      </a>
    </p>
  </div>
  <div class="col-lg-6">
      <img class="img-fluid mt-3 mx-auto" srcset="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-icons.png,
                                                  /docs/{{ .Site.Params.docs_version }}/assets/img/<EMAIL> 2x"
                                          src="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-icons.png"
                                          alt="Bootstrap Icons" width="700" height="425" loading="lazy">
  </div>
</section>
