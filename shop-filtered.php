<?php
/**
 * Template Name: Shop Page with Filters
 *
 * A shop page template with sidebar filters for price, brand, category, and color.
 *
 * @package Teandeal
 */

get_header();
?>

<div class="container shop-container">
  <div class="row">
    <!-- Sidebar with filters -->
    <div class="col-lg-3 shop-sidebar">
      <div class="filter-section">
        <h4 class="filter-title">Categories</h4>
        <div class="category-filter">
          <?php
          $product_categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => true,
            'parent' => 0,
            'exclude' => array(get_option('default_product_cat')) // Exclude "Uncategorized"
          ));
          
          if (!empty($product_categories) && !is_wp_error($product_categories)) {
            echo '<ul class="category-list">';
            foreach ($product_categories as $category) {
              echo '<li class="category-item">';
              echo '<input type="checkbox" id="category-' . esc_attr($category->slug) . '" name="category[]" value="' . esc_attr($category->slug) . '" class="filter-checkbox category-checkbox">';
              echo '<label for="category-' . esc_attr($category->slug) . '">' . esc_html($category->name) . ' (' . $category->count . ')</label>';
              
              // Get subcategories
              $subcategories = get_terms(array(
                'taxonomy' => 'product_cat',
                'hide_empty' => true,
                'parent' => $category->term_id
              ));
              
              if (!empty($subcategories) && !is_wp_error($subcategories)) {
                echo '<ul class="subcategory-list">';
                foreach ($subcategories as $subcategory) {
                  echo '<li class="subcategory-item">';
                  echo '<input type="checkbox" id="category-' . esc_attr($subcategory->slug) . '" name="category[]" value="' . esc_attr($subcategory->slug) . '" class="filter-checkbox category-checkbox">';
                  echo '<label for="category-' . esc_attr($subcategory->slug) . '">' . esc_html($subcategory->name) . ' (' . $subcategory->count . ')</label>';
                  echo '</li>';
                }
                echo '</ul>';
              }
              
              echo '</li>';
            }
            echo '</ul>';
          } else {
            echo '<p>No product categories found.</p>';
          }
          ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Price Range</h4>
        <div class="price-filter">
          <?php echo do_shortcode('[woocommerce_price_filter]'); ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Brands</h4>
        <div class="brand-filter">
          <?php
          // Check if product_brand taxonomy exists (WooCommerce Brands plugin)
          if (taxonomy_exists('product_brand')) {
            $brands = get_terms(array(
              'taxonomy' => 'product_brand',
              'hide_empty' => true
            ));
            
            if (!empty($brands) && !is_wp_error($brands)) {
              echo '<ul class="brand-list">';
              foreach ($brands as $brand) {
                echo '<li class="brand-item">';
                echo '<input type="checkbox" id="brand-' . esc_attr($brand->slug) . '" name="brand[]" value="' . esc_attr($brand->slug) . '" class="filter-checkbox brand-checkbox">';
                echo '<label for="brand-' . esc_attr($brand->slug) . '">' . esc_html($brand->name) . ' (' . $brand->count . ')</label>';
                echo '</li>';
              }
              echo '</ul>';
            } else {
              echo '<p>No brands found.</p>';
            }
          } else {
            // If product_brand taxonomy doesn't exist, try to use product attributes
            $attribute_taxonomies = wc_get_attribute_taxonomies();
            $brand_attribute = false;
            
            // Look for a brand-like attribute
            foreach ($attribute_taxonomies as $tax) {
              if (strpos(strtolower($tax->attribute_name), 'brand') !== false) {
                $brand_attribute = $tax->attribute_name;
                break;
              }
            }
            
            if ($brand_attribute) {
              $taxonomy = 'pa_' . $brand_attribute;
              $brands = get_terms(array(
                'taxonomy' => $taxonomy,
                'hide_empty' => true
              ));
              
              if (!empty($brands) && !is_wp_error($brands)) {
                echo '<ul class="brand-list">';
                foreach ($brands as $brand) {
                  echo '<li class="brand-item">';
                  echo '<input type="checkbox" id="brand-' . esc_attr($brand->slug) . '" name="brand[]" value="' . esc_attr($brand->slug) . '" class="filter-checkbox brand-checkbox" data-taxonomy="' . esc_attr($taxonomy) . '">';
                  echo '<label for="brand-' . esc_attr($brand->slug) . '">' . esc_html($brand->name) . ' (' . $brand->count . ')</label>';
                  echo '</li>';
                }
                echo '</ul>';
              } else {
                echo '<p>No brands found.</p>';
              }
            } else {
              echo '<p>Brand taxonomy not available.</p>';
            }
          }
          ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Color</h4>
        <div class="color-filter">
          <?php
          // Check if there's a color attribute
          $attribute_taxonomies = wc_get_attribute_taxonomies();
          $color_attribute = false;
          
          // Look for a color attribute
          foreach ($attribute_taxonomies as $tax) {
            if (strpos(strtolower($tax->attribute_name), 'color') !== false || 
                strpos(strtolower($tax->attribute_name), 'colour') !== false) {
              $color_attribute = $tax->attribute_name;
              break;
            }
          }
          
          if ($color_attribute) {
            $taxonomy = 'pa_' . $color_attribute;
            $colors = get_terms(array(
              'taxonomy' => $taxonomy,
              'hide_empty' => true
            ));
            
            if (!empty($colors) && !is_wp_error($colors)) {
              echo '<ul class="color-list">';
              foreach ($colors as $color) {
                // Try to get color code from term meta or use a default
                $color_code = get_term_meta($color->term_id, 'color', true);
                if (!$color_code) {
                  // Try to guess color code from name
                  $color_map = array(
                    'red' => '#ff0000',
                    'blue' => '#0000ff',
                    'green' => '#00ff00',
                    'yellow' => '#ffff00',
                    'black' => '#000000',
                    'white' => '#ffffff',
                    'orange' => '#ffa500',
                    'purple' => '#800080',
                    'pink' => '#ffc0cb',
                    'brown' => '#a52a2a',
                    'gray' => '#808080',
                    'grey' => '#808080',
                  );
                  
                  $color_name = strtolower($color->name);
                  $color_code = isset($color_map[$color_name]) ? $color_map[$color_name] : '#cccccc';
                }
                
                echo '<li class="color-item">';
                echo '<input type="checkbox" id="color-' . esc_attr($color->slug) . '" name="color[]" value="' . esc_attr($color->slug) . '" class="filter-checkbox color-checkbox" data-taxonomy="' . esc_attr($taxonomy) . '">';
                echo '<label for="color-' . esc_attr($color->slug) . '">';
                echo '<span class="color-swatch" style="background-color: ' . esc_attr($color_code) . '"></span>';
                echo esc_html($color->name) . ' (' . $color->count . ')';
                echo '</label>';
                echo '</li>';
              }
              echo '</ul>';
            } else {
              echo '<p>No colors found.</p>';
            }
          } else {
            echo '<p>Color attribute not available.</p>';
          }
          ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Ratings</h4>
        <div class="rating-filter">
          <ul class="rating-list">
            <?php for ($i = 5; $i >= 1; $i--) : ?>
            <li class="rating-item">
              <input type="checkbox" id="rating-<?php echo $i; ?>" name="rating[]" value="<?php echo $i; ?>"
                class="filter-checkbox rating-checkbox">
              <label for="rating-<?php echo $i; ?>">
                <?php 
                  for ($j = 1; $j <= 5; $j++) {
                    if ($j <= $i) {
                      echo '<i class="bi bi-star-fill"></i>';
                    } else {
                      echo '<i class="bi bi-star"></i>';
                    }
                  }
                  ?>
              </label>
            </li>
            <?php endfor; ?>
          </ul>
        </div>
      </div>

      <div class="filter-actions">
        <button id="apply-filters" class="btn btn-primary">Apply Filters</button>
        <button id="reset-filters" class="btn btn-outline-secondary">Reset</button>
      </div>
    </div>

    <!-- Main content area -->
    <div class="col-lg-9 shop-main-content ">
      <div class="shop-header">
        <h1 class="shop-title">Shop</h1>
        <div class="shop-controls">
          <div class="product-count">
            <span id="product-count">Showing all products</span>
          </div>
          <div class="shop-sorting">
            <select id="shop-orderby" class="form-select">
              <option value="menu_order">Default sorting</option>
              <option value="popularity">Sort by popularity</option>
              <option value="rating">Sort by average rating</option>
              <option value="date">Sort by latest</option>
              <option value="price">Sort by price: low to high</option>
              <option value="price-desc">Sort by price: high to low</option>
            </select>
          </div>
          <div class="shop-view-mode">
            <button class="view-mode-btn grid-view active" data-view="grid"><i
                class="bi bi-grid-3x3-gap-fill"></i></button>
            <button class="view-mode-btn list-view" data-view="list"><i class="bi bi-list"></i></button>
          </div>
        </div>
      </div>

      <div id="shop-products" class="products-grid">
        <?php
        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
        $args = array(
          'post_type' => 'product',
          'posts_per_page' => 12,
          'paged' => $paged,
        );
        
        $products_query = new WP_Query($args);
        
        if ($products_query->have_posts()) {
          while ($products_query->have_posts()) {
            $products_query->the_post();
            global $product;
            
            // Make sure we have a valid product object
            if (!is_a($product, 'WC_Product')) {
              continue;
            }
            
            // Get product categories
            $categories = get_the_terms($product->get_id(), 'product_cat');
            $category_classes = '';
            $category_names = array();
            
            if (!empty($categories) && !is_wp_error($categories)) {
              foreach ($categories as $category) {
                $category_classes .= ' category-' . $category->slug;
                $category_names[] = $category->name;
              }
            }
            
            // Get product brands
            $brands = array();
            $brand_classes = '';
            $brand_names = array();
            
            if (taxonomy_exists('product_brand')) {
              $brands = get_the_terms($product->get_id(), 'product_brand');
              
              if (!empty($brands) && !is_wp_error($brands)) {
                foreach ($brands as $brand) {
                  $brand_classes .= ' brand-' . $brand->slug;
                  $brand_names[] = $brand->name;
                }
              }
            }
            
            // Get product colors
            $colors = array();
            $color_classes = '';
            $color_names = array();
            
            if ($color_attribute) {
              $colors = get_the_terms($product->get_id(), 'pa_' . $color_attribute);
              
              if (!empty($colors) && !is_wp_error($colors)) {
                foreach ($colors as $color) {
                  $color_classes .= ' color-' . $color->slug;
                  $color_names[] = $color->name;
                }
              }
            }
            
            // Get product rating
            $rating = $product->get_average_rating();
            $rating_class = 'rating-' . floor($rating);
            ?>
        <div
          class="product-card<?php echo esc_attr($category_classes . $brand_classes . $color_classes . ' ' . $rating_class); ?>"
          data-price="<?php echo esc_attr($product->get_price()); ?>"
          data-categories="<?php echo esc_attr(implode(',', $category_names)); ?>"
          data-brands="<?php echo esc_attr(implode(',', $brand_names)); ?>"
          data-colors="<?php echo esc_attr(implode(',', $color_names)); ?>"
          data-rating="<?php echo esc_attr(floor($rating)); ?>">
          <div class="product-image">
            <?php if ($product->is_on_sale()) : ?>
            <span class="sale-badge">Sale</span>
            <?php endif; ?>
            <a href="<?php the_permalink(); ?>">
              <?php 
                  if (has_post_thumbnail()) {
                    echo woocommerce_get_product_thumbnail();
                  } else {
                    echo '<img src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                  }
                  ?>
            </a>
            <div class="product-actions">
              <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-cart-plus"></i>
              </button>
              <button class="action-btn add-to-wishlist" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-heart"></i>
              </button>
              <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-eye"></i>
              </button>
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-title">
              <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
            </h3>
            <div class="product-rating">
              <?php echo wc_get_rating_html($product->get_average_rating()); ?>
              <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
            </div>
            <div class="product-price">
              <?php echo $product->get_price_html(); ?>
            </div>
            <div class="product-meta">
              <?php if ($product->get_stock_status() === 'instock') : ?>
              <span class="in-stock">In Stock</span>
              <?php else : ?>
              <span class="out-of-stock">Out of Stock</span>
              <?php endif; ?>
              <?php if (!empty($category_names)) : ?>
              <span class="product-categories">Category: <?php echo esc_html($category_names[0]); ?></span>
              <?php endif; ?>
            </div>
            <div class="product-description">
              <?php echo wp_trim_words($product->get_short_description(), 20, '...'); ?>
            </div>
            <div class="list-view-actions">
              <a href="<?php echo esc_url($product->add_to_cart_url()); ?>" class="btn btn-primary add-to-cart-btn">Add
                to Cart</a>
              <a href="<?php the_permalink(); ?>" class="btn btn-outline-secondary view-details-btn">View Details</a>
            </div>
          </div>
        </div>
        <?php
          }
          
          // Pagination
          echo '<div class="shop-pagination">';
          echo paginate_links(array(
            'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
            'format' => '?paged=%#%',
            'current' => max(1, get_query_var('paged')),
            'total' => $products_query->max_num_pages,
            'prev_text' => '<i class="bi bi-chevron-left"></i>',
            'next_text' => '<i class="bi bi-chevron-right"></i>',
          ));
          echo '</div>';
          
          wp_reset_postdata();
        } else {
          echo '<p class="no-products">No products found. Please check back later.</p>';
        }
        ?>
      </div>
    </div>
  </div>
</div>

<?php
get_footer();
?>