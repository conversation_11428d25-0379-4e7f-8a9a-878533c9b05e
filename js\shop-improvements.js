/**
 * Enhanced Shop Functionality
 */

(function ($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function () {
        initShopFunctionality();
        initViewModeToggle();
        initSortingAndPagination();
        initMobileFilters();
        initLoadingStates();
        initKeyboardNavigation();
    });

    /**
     * Initialize main shop functionality
     */
    function initShopFunctionality() {
        // Store current view mode in localStorage
        const savedViewMode = localStorage.getItem('shop_view_mode') || 'grid';
        setViewMode(savedViewMode);

        // Handle URL changes (back/forward buttons)
        window.addEventListener('popstate', function (e) {
            if (e.state && e.state.shopFilters) {
                location.reload();
            }
        });

        // Add smooth scrolling to pagination links - DISABLED
        // $(document).on('click', '.woocommerce-pagination a', function (e) {
        //     e.preventDefault();
        //     const url = $(this).attr('href');
        //     loadShopContent(url);
        // });

        // Pagination now uses normal page navigation for better reliability
        console.log('Shop improvements: AJAX pagination disabled');
    }

    /**
     * Initialize view mode toggle functionality
     */
    function initViewModeToggle() {
        // Handle both old and new view mode buttons
        $('.view-mode-btn, .view-btn').on('click', function () {
            const viewMode = $(this).data('view');
            setViewMode(viewMode);

            // Save preference
            localStorage.setItem('shop_view_mode', viewMode);

            // Update active state for both button types
            $('.view-mode-btn, .view-btn').removeClass('active');
            $(`.view-mode-btn[data-view="${viewMode}"], .view-btn[data-view="${viewMode}"]`).addClass('active');
        });
    }

    /**
     * Set view mode for products
     */
    function setViewMode(mode) {
        const $productsContainer = $('#shop-products');
        $productsContainer.attr('data-view', mode);

        // Update button states for both button types
        $('.view-mode-btn, .view-btn').removeClass('active');
        $(`.view-mode-btn[data-view="${mode}"], .view-btn[data-view="${mode}"]`).addClass('active');

        // Trigger custom event
        $(document).trigger('shop:viewModeChanged', [mode]);
    }

    /**
     * Initialize sorting and pagination with AJAX
     */
    function initSortingAndPagination() {
        // Handle sorting change (both old and new selectors)
        $('#shop-orderby, #shop-orderby-clean').on('change', function () {
            const orderby = $(this).val();
            updateShopFilters({ orderby: orderby });
        });

        // Handle products per page change
        $('#per-page-select').on('change', function () {
            const perPage = $(this).val();
            updateShopFilters({ per_page: perPage, paged: 1 }); // Reset to page 1
        });
    }

    /**
     * Update shop filters and reload content
     */
    function updateShopFilters(newParams) {
        const currentUrl = new URL(window.location);
        const searchParams = currentUrl.searchParams;

        // Update parameters
        Object.keys(newParams).forEach(key => {
            if (newParams[key]) {
                searchParams.set(key, newParams[key]);
            } else {
                searchParams.delete(key);
            }
        });

        // Build new URL
        const newUrl = currentUrl.pathname + '?' + searchParams.toString();

        // Load content with AJAX
        loadShopContent(newUrl);
    }

    /**
     * Load shop content via AJAX
     */
    function loadShopContent(url) {
        showLoadingState();

        // Add AJAX parameter to prevent full page load
        const ajaxUrl = url + (url.includes('?') ? '&' : '?') + 'ajax=1';

        $.ajax({
            url: ajaxUrl,
            type: 'GET',
            dataType: 'html',
            success: function (response) {
                // Extract products content
                const $response = $(response);
                const $newProducts = $response.find('#shop-products');
                const $newControls = $response.find('.shop-controls');
                const $newPagination = $response.find('.woocommerce-pagination');

                if ($newProducts.length) {
                    // Update products
                    $('#shop-products').html($newProducts.html());

                    // Update controls
                    if ($newControls.length) {
                        $('.shop-controls').html($newControls.html());
                        // Reinitialize controls
                        initSortingAndPagination();
                    }

                    // Update pagination - remove all existing first
                    $('.woocommerce-pagination, .shop-pagination').remove();
                    if ($newPagination.length) {
                        $('#shop-products').after($newPagination.first()); // Only add the first pagination found
                    }

                    // Remove any duplicate pagination that might slip through
                    setTimeout(function () {
                        const paginations = $('.woocommerce-pagination, .shop-pagination');
                        if (paginations.length > 1) {
                            paginations.slice(1).remove(); // Remove all but the first
                        }
                    }, 100);

                    // Update URL without page reload
                    history.pushState({ shopFilters: true }, '', url);

                    // Scroll to products (no animation)
                    $('html, body').scrollTop($('#shop-products').offset().top - 100);

                    // Trigger custom event
                    $(document).trigger('shop:contentLoaded');
                }

                hideLoadingState();
            },
            error: function () {
                // Fallback to full page reload
                window.location.href = url;
            }
        });
    }

    /**
     * Initialize mobile filters
     */
    function initMobileFilters() {
        // Copy sidebar content to mobile offcanvas
        const $sidebar = $('.shop-sidebar');
        const $mobileFilters = $('.mobile-filters');

        if ($sidebar.length && $mobileFilters.length) {
            $mobileFilters.html($sidebar.html());
        }

        // Handle mobile filter toggle
        $('.filter-toggle-btn').on('click', function () {
            // Update filter count badge
            updateFilterCount();
        });

        // Close offcanvas when filter is applied
        $(document).on('click', '.mobile-filters a', function () {
            setTimeout(function () {
                $('#shop-filters-offcanvas').offcanvas('hide');
            }, 300);
        });
    }

    /**
     * Update filter count badge
     */
    function updateFilterCount() {
        const urlParams = new URLSearchParams(window.location.search);
        let count = 0;

        // Count active filters (excluding search, post_type, paged)
        const excludeParams = ['s', 'post_type', 'paged', 'orderby', 'per_page'];
        urlParams.forEach((value, key) => {
            if (!excludeParams.includes(key) && value) {
                count++;
            }
        });

        // Update badge
        const $badge = $('.filter-count-badge');
        if (count > 0) {
            $badge.text(count).show();
        } else {
            $badge.hide();
        }
    }

    /**
     * Initialize loading states
     */
    function initLoadingStates() {
        // Show loading on form submissions
        $(document).on('submit', '#shop-filters-form', function () {
            showLoadingState();
        });

        // Show loading on filter link clicks
        $(document).on('click', '.shop-sidebar a', function () {
            showLoadingState();
        });
    }

    /**
     * Show loading state
     */
    function showLoadingState() {
        $('#shop-loading').show();
        $('#shop-products').addClass('loading');
    }

    /**
     * Hide loading state
     */
    function hideLoadingState() {
        $('#shop-loading').hide();
        $('#shop-products').removeClass('loading');
    }

    /**
     * Initialize keyboard navigation
     */
    function initKeyboardNavigation() {
        // Handle keyboard navigation for view mode buttons
        $('.view-mode-btn').on('keydown', function (e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).click();
            }
        });

        // Handle keyboard navigation for filter links
        $(document).on('keydown', '.shop-sidebar a', function (e) {
            if (e.key === 'Enter') {
                $(this)[0].click();
            }
        });
    }

    /**
     * Initialize infinite scroll (optional)
     */
    function initInfiniteScroll() {
        if (typeof window.shopSettings !== 'undefined' && window.shopSettings.infiniteScroll) {
            let loading = false;

            $(window).on('scroll', function () {
                if (loading) return;

                const $pagination = $('.woocommerce-pagination');
                const $nextLink = $pagination.find('.next');

                if ($nextLink.length && isElementInViewport($pagination[0])) {
                    loading = true;

                    $.ajax({
                        url: $nextLink.attr('href'),
                        type: 'GET',
                        success: function (response) {
                            const $response = $(response);
                            const $newProducts = $response.find('#shop-products .product');
                            const $newPagination = $response.find('.woocommerce-pagination');

                            // Append new products
                            $('#shop-products .products').append($newProducts);

                            // Update pagination - remove all existing first
                            $('.woocommerce-pagination, .shop-pagination').remove();
                            if ($newPagination.length) {
                                $('#shop-products').after($newPagination.first());
                            }

                            loading = false;
                        },
                        error: function () {
                            loading = false;
                        }
                    });
                }
            });
        }
    }

    /**
     * Check if element is in viewport
     */
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Initialize product quick actions
     */
    function initProductQuickActions() {
        // Quick view functionality
        $(document).on('click', '.quick-view-btn', function (e) {
            e.preventDefault();
            const productId = $(this).data('product-id');
            openQuickView(productId);
        });

        // Compare functionality
        $(document).on('click', '.compare-btn', function (e) {
            e.preventDefault();
            const productId = $(this).data('product-id');
            addToCompare(productId);
        });
    }

    /**
     * Open quick view modal
     */
    function openQuickView(productId) {
        // Implementation for quick view
        console.log('Opening quick view for product:', productId);
    }

    /**
     * Add product to compare
     */
    function addToCompare(productId) {
        // Implementation for compare functionality
        console.log('Adding to compare:', productId);
    }

    // Initialize filter count on page load
    updateFilterCount();

    // Expose functions globally for external use
    window.shopFunctions = {
        updateShopFilters: updateShopFilters,
        loadShopContent: loadShopContent,
        setViewMode: setViewMode,
        showLoadingState: showLoadingState,
        hideLoadingState: hideLoadingState
    };

})(jQuery);
