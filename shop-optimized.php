<?php
/**
 * Template Name: Optimized Shop for Large Catalogs
 *
 * High-performance shop page template optimized for 20000+ products
 * Uses optimized database queries and caching to prevent timeouts
 *
 * @package Tendeal
 */

get_header();

// Enqueue optimized shop styles and scripts
wp_enqueue_style('tendeal-shop-optimized', get_stylesheet_directory_uri() . '/css/shop-optimized.css', array(), _S_VERSION);
wp_enqueue_script('tendeal-shop-optimized', get_stylesheet_directory_uri() . '/js/shop-optimized.js', array('jquery'), _S_VERSION, true);

// Initialize optimized query systems
$product_query = tendeal_init_optimized_queries();
$filter_data_system = tendeal_init_optimized_filter_data();

// Performance monitoring
$page_start_time = microtime(true);

// Get current page and basic parameters
$paged = max(1, get_query_var('paged'));
$products_per_page = 12;

// Build optimized query arguments
$query_args = array(
    'limit' => $products_per_page,
    'page' => $paged,
    'status' => 'publish',
    'visibility' => 'catalog',
    'stock_status' => array('instock', 'onbackorder'),
    'paginate' => true
);

// Handle search
if (isset($_GET['s']) && !empty($_GET['s'])) {
    $search_term = sanitize_text_field($_GET['s']);
    $query_args['search'] = $search_term;
}

// Handle category filter
if (isset($_GET['product_cat']) && !empty($_GET['product_cat'])) {
    $category_slug = sanitize_text_field($_GET['product_cat']);
    $category = get_term_by('slug', $category_slug, 'product_cat');
    if ($category) {
        $query_args['category'] = array($category->term_id);
    }
}

// Handle brand filter
if (isset($_GET['product_brand']) && !empty($_GET['product_brand'])) {
    $brand_slug = sanitize_text_field($_GET['product_brand']);
    $brand = get_term_by('slug', $brand_slug, 'product_brand');
    if ($brand) {
        $query_args['tag'] = array($brand->term_id);
    }
}

// Handle price filter
if (isset($_GET['min_price']) && isset($_GET['max_price'])) {
    $min_price = floatval($_GET['min_price']);
    $max_price = floatval($_GET['max_price']);
    if ($min_price > 0 || $max_price > 0) {
        $query_args['price'] = $min_price . '...' . $max_price;
    }
}

// Handle rating filter
if (isset($_GET['rating']) && !empty($_GET['rating'])) {
    $rating = intval($_GET['rating']);
    if ($rating >= 1 && $rating <= 5) {
        $query_args['average_rating'] = $rating . '...5';
    }
}

// Handle sorting
if (isset($_GET['orderby']) && !empty($_GET['orderby'])) {
    $orderby = sanitize_text_field($_GET['orderby']);
    switch ($orderby) {
        case 'popularity':
            $query_args['orderby'] = 'popularity';
            break;
        case 'rating':
            $query_args['orderby'] = 'rating';
            break;
        case 'date':
            $query_args['orderby'] = 'date';
            $query_args['order'] = 'DESC';
            break;
        case 'price':
            $query_args['orderby'] = 'price';
            $query_args['order'] = 'ASC';
            break;
        case 'price-desc':
            $query_args['orderby'] = 'price';
            $query_args['order'] = 'DESC';
            break;
        default:
            $query_args['orderby'] = 'menu_order';
            $query_args['order'] = 'ASC';
            break;
    }
}

// Execute optimized product query
$products_result = $product_query->get_products($query_args);

// Get filter data (cached and optimized)
$context = is_product_category() ? 'category' : 'shop';
$context_id = is_product_category() ? get_queried_object()->term_id : null;
$filter_data = $filter_data_system->get_filter_data($context, $context_id);

// Calculate total page load time
$page_load_time = microtime(true) - $page_start_time;

// Add performance debug info for admins
if (current_user_can('manage_options')) {
    echo "<!-- Optimized Shop Performance: Page loaded in " . round($page_load_time, 3) . " seconds -->";
    echo "<!-- Filter data generated in " . round($filter_data['processing_time'], 3) . " seconds -->";
    echo "<!-- Total products: " . $filter_data['total_products'] . " -->";
}
?>

<div class="container shop-container optimized-shop" role="main" aria-label="<?php esc_attr_e('Product Shop', 'tendeal'); ?>">
    <div class="row">
        <!-- Optimized Sidebar with Filters -->
        <div class="col-lg-3 shop-sidebar">
            <div class="filters-container">
                <div class="filters-header">
                    <h3><?php esc_html_e('Filters', 'tendeal'); ?></h3>
                    <button type="button" class="btn btn-link btn-sm clear-filters" style="display: none;">
                        <?php esc_html_e('Clear All', 'tendeal'); ?>
                    </button>
                </div>

                <form id="shop-filters-form" method="get" action="<?php echo esc_url(wc_get_page_permalink('shop')); ?>">
                    <?php
                    // Preserve search query
                    if (isset($_GET['s']) && !empty($_GET['s'])) : ?>
                        <input type="hidden" name="s" value="<?php echo esc_attr($_GET['s']); ?>">
                        <input type="hidden" name="post_type" value="product">
                    <?php endif; ?>

                    <!-- Categories Filter -->
                    <?php if (!empty($filter_data['categories'])) : ?>
                        <div class="filter-group">
                            <h4 class="filter-title"><?php esc_html_e('Categories', 'tendeal'); ?></h4>
                            <div class="filter-options">
                                <?php foreach ($filter_data['categories'] as $category) : ?>
                                    <label class="filter-option">
                                        <input type="checkbox" name="product_cat[]" value="<?php echo esc_attr($category->slug); ?>"
                                            <?php checked(isset($_GET['product_cat']) && in_array($category->slug, (array)$_GET['product_cat'])); ?>>
                                        <span class="filter-label">
                                            <?php echo esc_html($category->name); ?>
                                            <span class="filter-count">(<?php echo esc_html($category->product_count); ?>)</span>
                                        </span>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Brands Filter -->
                    <?php if (!empty($filter_data['brands'])) : ?>
                        <div class="filter-group">
                            <h4 class="filter-title"><?php esc_html_e('Brands', 'tendeal'); ?></h4>
                            <div class="filter-options">
                                <?php foreach ($filter_data['brands'] as $brand) : ?>
                                    <label class="filter-option">
                                        <input type="checkbox" name="product_brand[]" value="<?php echo esc_attr($brand->slug); ?>"
                                            <?php checked(isset($_GET['product_brand']) && in_array($brand->slug, (array)$_GET['product_brand'])); ?>>
                                        <span class="filter-label">
                                            <?php echo esc_html($brand->name); ?>
                                            <span class="filter-count">(<?php echo esc_html($brand->product_count); ?>)</span>
                                        </span>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Price Filter -->
                    <div class="filter-group">
                        <h4 class="filter-title"><?php esc_html_e('Price Range', 'tendeal'); ?></h4>
                        <div class="price-filter">
                            <div class="price-inputs">
                                <input type="number" name="min_price" placeholder="Min" 
                                    value="<?php echo esc_attr(isset($_GET['min_price']) ? $_GET['min_price'] : ''); ?>"
                                    min="<?php echo esc_attr($filter_data['price_range']['min']); ?>"
                                    max="<?php echo esc_attr($filter_data['price_range']['max']); ?>">
                                <span>-</span>
                                <input type="number" name="max_price" placeholder="Max"
                                    value="<?php echo esc_attr(isset($_GET['max_price']) ? $_GET['max_price'] : ''); ?>"
                                    min="<?php echo esc_attr($filter_data['price_range']['min']); ?>"
                                    max="<?php echo esc_attr($filter_data['price_range']['max']); ?>">
                            </div>
                            <div class="price-range-display">
                                <?php echo wc_price($filter_data['price_range']['min']); ?> - 
                                <?php echo wc_price($filter_data['price_range']['max']); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Rating Filter -->
                    <?php if (array_sum($filter_data['rating_counts']) > 0) : ?>
                        <div class="filter-group">
                            <h4 class="filter-title"><?php esc_html_e('Customer Rating', 'tendeal'); ?></h4>
                            <div class="filter-options">
                                <?php for ($i = 5; $i >= 1; $i--) : ?>
                                    <?php if ($filter_data['rating_counts'][$i] > 0) : ?>
                                        <label class="filter-option">
                                            <input type="radio" name="rating" value="<?php echo esc_attr($i); ?>"
                                                <?php checked(isset($_GET['rating']) && $_GET['rating'] == $i); ?>>
                                            <span class="filter-label">
                                                <span class="stars">
                                                    <?php for ($j = 1; $j <= 5; $j++) : ?>
                                                        <span class="star <?php echo $j <= $i ? 'filled' : 'empty'; ?>">★</span>
                                                    <?php endfor; ?>
                                                </span>
                                                <span class="filter-count">(<?php echo esc_html($filter_data['rating_counts'][$i]); ?>)</span>
                                            </span>
                                        </label>
                                    <?php endif; ?>
                                <?php endfor; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="filter-actions">
                        <button type="submit" class="btn btn-primary btn-block">
                            <?php esc_html_e('Apply Filters', 'tendeal'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="col-lg-9 shop-main">
            <!-- Shop Header -->
            <div class="shop-header">
                <div class="shop-results-info">
                    <?php if ($products_result && $products_result->total > 0) : ?>
                        <p class="results-count">
                            <?php
                            printf(
                                esc_html__('Showing %1$d-%2$d of %3$d results', 'tendeal'),
                                ($paged - 1) * $products_per_page + 1,
                                min($paged * $products_per_page, $products_result->total),
                                $products_result->total
                            );
                            ?>
                        </p>
                    <?php endif; ?>
                </div>

                <div class="shop-controls">
                    <!-- View Mode Toggle -->
                    <div class="view-mode-toggle">
                        <button type="button" class="view-mode-btn active" data-view="grid" aria-label="Grid View">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="view-mode-btn" data-view="list" aria-label="List View">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>

                    <!-- Sort Dropdown -->
                    <select name="orderby" id="shop-orderby" class="form-select">
                        <option value="menu_order" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'menu_order'); ?>>
                            <?php esc_html_e('Default sorting', 'tendeal'); ?>
                        </option>
                        <option value="popularity" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'popularity'); ?>>
                            <?php esc_html_e('Sort by popularity', 'tendeal'); ?>
                        </option>
                        <option value="rating" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'rating'); ?>>
                            <?php esc_html_e('Sort by average rating', 'tendeal'); ?>
                        </option>
                        <option value="date" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'date'); ?>>
                            <?php esc_html_e('Sort by latest', 'tendeal'); ?>
                        </option>
                        <option value="price" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'price'); ?>>
                            <?php esc_html_e('Sort by price: low to high', 'tendeal'); ?>
                        </option>
                        <option value="price-desc" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'price-desc'); ?>>
                            <?php esc_html_e('Sort by price: high to low', 'tendeal'); ?>
                        </option>
                    </select>
                </div>
            </div>

            <!-- Loading Overlay -->
            <div id="shop-loading" class="shop-loading-overlay" style="display: none;">
                <div class="loading-spinner">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden"><?php esc_html_e('Loading...', 'tendeal'); ?></span>
                    </div>
                    <p><?php esc_html_e('Loading products...', 'tendeal'); ?></p>
                </div>
            </div>

            <!-- Products Grid -->
            <div id="shop-products" class="products-container" data-view="grid">
                <?php if ($products_result && !empty($products_result->products)) : ?>
                    <div class="products row">
                        <?php foreach ($products_result->products as $product) : ?>
                            <div class="col-lg-4 col-md-6 col-sm-6 mb-4">
                                <?php
                                // Set global product for template
                                $GLOBALS['product'] = $product;
                                
                                // Use optimized product card template
                                if (locate_template('woocommerce/content-product-card-optimized.php')) {
                                    wc_get_template('content-product-card-optimized.php');
                                } else {
                                    wc_get_template_part('content', 'product');
                                }
                                ?>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Optimized Pagination -->
                    <?php if ($products_result->max_num_pages > 1) : ?>
                        <nav class="woocommerce-pagination" aria-label="<?php esc_attr_e('Products pagination', 'tendeal'); ?>">
                            <?php
                            echo paginate_links(array(
                                'base' => esc_url_raw(str_replace(999999999, '%#%', remove_query_arg('add-to-cart', get_pagenum_link(999999999, false)))),
                                'format' => '',
                                'add_args' => false,
                                'current' => $paged,
                                'total' => $products_result->max_num_pages,
                                'prev_text' => '&larr; ' . esc_html__('Previous', 'tendeal'),
                                'next_text' => esc_html__('Next', 'tendeal') . ' &rarr;',
                                'type' => 'list',
                                'end_size' => 3,
                                'mid_size' => 3,
                            ));
                            ?>
                        </nav>
                    <?php endif; ?>

                <?php else : ?>
                    <div class="no-products-found">
                        <h3><?php esc_html_e('No products found', 'tendeal'); ?></h3>
                        <p><?php esc_html_e('Try adjusting your filters or search terms.', 'tendeal'); ?></p>
                        <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="btn btn-primary">
                            <?php esc_html_e('View All Products', 'tendeal'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Localize script for AJAX
wp_localize_script('tendeal-shop-optimized', 'shopOptimized', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('shop_optimized_nonce'),
    'shop_url' => wc_get_page_permalink('shop'),
    'loading_text' => esc_html__('Loading products...', 'tendeal'),
    'no_products_text' => esc_html__('No products found', 'tendeal')
));

get_footer();
?>
