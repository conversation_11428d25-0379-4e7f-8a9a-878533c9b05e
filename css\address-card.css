/* Address Card Design */
.address-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  max-width: 350px;
}

.address-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.address-card.selected {
  border-color: #ea9c00;
  background-color: rgba(234, 156, 0, 0.05);
  box-shadow: 0 4px 16px rgba(234, 156, 0, 0.15);
}

.address-card {
  cursor: pointer;
}

/* Address Card Header */
.address-card-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.address-icon {
  width: 20px;
  height: 20px;
  color: #ea9c00;
  flex-shrink: 0;
  margin-top: 2px;
}

.address-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
}

/* Address Details */
.address-details {
  margin-left: 32px;
  /* Align with name text */
}

.address-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.address-item:last-child {
  margin-bottom: 0;
}

.address-item-icon {
  width: 16px;
  height: 16px;
  color: #9ca3af;
  flex-shrink: 0;
  margin-top: 2px;
}

.address-item-text {
  flex: 1;
}

/* Address Types */
.address-street {
  color: #374151;
}

.address-country {
  color: #374151;
  font-weight: 500;
}

.address-phone {
  color: #374151;
}

/* Compact Address Card */
.address-card.compact {
  padding: 16px;
  max-width: 300px;
}

.address-card.compact .address-name {
  font-size: 15px;
}

.address-card.compact .address-item {
  font-size: 13px;
  margin-bottom: 8px;
}

.address-card.compact .address-details {
  margin-left: 28px;
}

/* Address Card with Actions */
.address-card-with-actions {
  position: relative;
}

.address-card-actions {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.address-card:hover .address-card-actions {
  opacity: 1;
}

.address-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f3f4f6;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.address-action-btn:hover {
  background: #ea9c00;
  color: white;
}

.address-action-btn.delete:hover {
  background: #ef4444;
  color: white;
}

/* Address Type Badge */
.address-card-type {
  margin-bottom: 16px;
}

.address-type-badge {
  display: inline-block;
  background: #ea9c00;
  color: white;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 20px;
}

/* Default Badge */
.address-default-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #ea9c00;
  color: white;
  font-size: 11px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.address-default-badge .icon {
  width: 12px;
  height: 12px;
}

/* Address List */
.address-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .address-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .address-card {
    max-width: none;
  }

  .address-card-actions {
    opacity: 1;
    /* Always show on mobile */
  }
}

/* Address Form Styles */
.address-form-container {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.address-form-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.address-form-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.address-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.address-form-row.full-width {
  grid-template-columns: 1fr;
}

.address-form-group {
  margin-bottom: 16px;
}

.address-form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.address-form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.address-form-input:focus {
  outline: none;
  border-color: #ea9c00;
  box-shadow: 0 0 0 3px rgba(234, 156, 0, 0.1);
}

.address-form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.address-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.address-btn-primary {
  background: #ea9c00;
  color: white;
}

.address-btn-primary:hover {
  background: #d48c00;
}

.address-btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.address-btn-secondary:hover {
  background: #e5e7eb;
}

@media (max-width: 768px) {
  .address-form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .address-form-actions {
    flex-direction: column;
  }

  .address-btn {
    width: 100%;
    justify-content: center;
  }
}