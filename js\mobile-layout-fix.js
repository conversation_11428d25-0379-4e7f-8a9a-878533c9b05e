/**
 * Mobile Layout Fix JavaScript for Tendeal Theme
 * 
 * This file contains JavaScript fixes for mobile layout issues
 * and provides debugging tools for responsive problems.
 */

(function($) {
    'use strict';

    // Mobile detection
    const isMobile = window.innerWidth <= 768;
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);

    /**
     * Initialize mobile layout fixes
     */
    function initMobileLayoutFixes() {
        fixViewportIssues();
        fixContainerWidths();
        fixHorizontalScrolling();
        fixIOSSpecificIssues();
        addMobileDebugInfo();
        handleOrientationChange();
        fixTouchScrolling();
    }

    /**
     * Fix viewport and scaling issues
     */
    function fixViewportIssues() {
        // Ensure viewport meta tag is properly set
        let viewportMeta = $('meta[name="viewport"]');
        if (viewportMeta.length === 0) {
            $('head').append('<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes, viewport-fit=cover">');
        }

        // Fix iOS viewport bug
        if (isIOS) {
            // Fix iOS viewport units bug
            function fixIOSViewport() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', vh + 'px');
            }
            
            fixIOSViewport();
            $(window).on('resize orientationchange', fixIOSViewport);
        }

        // Prevent zoom on input focus (iOS)
        if (isIOS) {
            $('input[type="text"], input[type="email"], input[type="password"], input[type="search"], textarea').attr('autocomplete', 'off');
        }
    }

    /**
     * Fix container width issues
     */
    function fixContainerWidths() {
        if (!isMobile) return;

        // Force containers to use full width on mobile
        $('.container, .container-fluid').each(function() {
            const $container = $(this);
            
            // Check if container is causing horizontal scroll
            if ($container.outerWidth() > $(window).width()) {
                $container.css({
                    'width': '100%',
                    'max-width': '100%',
                    'padding-left': '15px',
                    'padding-right': '15px',
                    'margin-left': '0',
                    'margin-right': '0'
                });
            }
        });

        // Fix rows that might be too wide
        $('.row').each(function() {
            const $row = $(this);
            if ($row.outerWidth() > $(window).width()) {
                $row.css({
                    'width': '100%',
                    'max-width': '100%',
                    'margin-left': '-15px',
                    'margin-right': '-15px'
                });
            }
        });
    }

    /**
     * Fix horizontal scrolling issues
     */
    function fixHorizontalScrolling() {
        if (!isMobile) return;

        // Check for elements causing horizontal scroll
        function checkHorizontalScroll() {
            const windowWidth = $(window).width();
            let problematicElements = [];

            $('*').each(function() {
                const $element = $(this);
                const elementRight = $element.offset().left + $element.outerWidth();
                
                if (elementRight > windowWidth) {
                    problematicElements.push({
                        element: this,
                        width: $element.outerWidth(),
                        right: elementRight,
                        selector: this.tagName + (this.className ? '.' + this.className.split(' ').join('.') : '') + (this.id ? '#' + this.id : '')
                    });
                }
            });

            // Fix problematic elements
            problematicElements.forEach(function(item) {
                const $element = $(item.element);
                
                // Skip if element is hidden
                if (!$element.is(':visible')) return;
                
                // Apply fixes based on element type
                if ($element.is('img')) {
                    $element.css({
                        'max-width': '100%',
                        'height': 'auto'
                    });
                } else if ($element.is('table')) {
                    $element.wrap('<div class="table-responsive"></div>');
                } else {
                    $element.css({
                        'max-width': '100%',
                        'overflow-x': 'auto',
                        'box-sizing': 'border-box'
                    });
                }
            });

            // Log problematic elements for debugging
            if (problematicElements.length > 0 && window.console) {
                console.warn('Elements causing horizontal scroll:', problematicElements);
            }
        }

        // Check on load and resize
        checkHorizontalScroll();
        $(window).on('resize orientationchange', function() {
            setTimeout(checkHorizontalScroll, 100);
        });
    }

    /**
     * Fix iOS specific issues
     */
    function fixIOSSpecificIssues() {
        if (!isIOS) return;

        // Fix iOS bounce scroll
        $('body').css({
            '-webkit-overflow-scrolling': 'touch',
            'overflow-scrolling': 'touch'
        });

        // Fix iOS input zoom
        $('input, textarea, select').on('focus', function() {
            $(this).css('font-size', '16px');
        }).on('blur', function() {
            $(this).css('font-size', '');
        });

        // Fix iOS safe area
        if (window.CSS && window.CSS.supports && window.CSS.supports('padding-top: env(safe-area-inset-top)')) {
            $('body').addClass('ios-safe-area');
        }
    }

    /**
     * Add mobile debug information
     */
    function addMobileDebugInfo() {
        // Only add debug info if URL contains debug parameter
        if (window.location.search.indexOf('mobile_debug=1') === -1) return;

        const debugInfo = {
            'Screen Width': window.screen.width,
            'Screen Height': window.screen.height,
            'Window Width': window.innerWidth,
            'Window Height': window.innerHeight,
            'Device Pixel Ratio': window.devicePixelRatio,
            'User Agent': navigator.userAgent,
            'Is Mobile': isMobile,
            'Is iOS': isIOS,
            'Is Android': isAndroid,
            'Viewport Width': $(window).width(),
            'Document Width': $(document).width(),
            'Body Width': $('body').width()
        };

        // Create debug panel
        const $debugPanel = $('<div id="mobile-debug-panel"></div>').css({
            'position': 'fixed',
            'top': '10px',
            'left': '10px',
            'background': 'rgba(0,0,0,0.8)',
            'color': 'white',
            'padding': '10px',
            'font-size': '12px',
            'z-index': '9999',
            'max-width': '300px',
            'border-radius': '5px'
        });

        let debugHtml = '<h4>Mobile Debug Info</h4>';
        for (let key in debugInfo) {
            debugHtml += '<div><strong>' + key + ':</strong> ' + debugInfo[key] + '</div>';
        }

        $debugPanel.html(debugHtml);
        $('body').append($debugPanel);

        // Add close button
        $debugPanel.append('<button onclick="$(this).parent().remove()" style="margin-top:10px;">Close</button>');
    }

    /**
     * Handle orientation change
     */
    function handleOrientationChange() {
        $(window).on('orientationchange', function() {
            // Delay to allow orientation change to complete
            setTimeout(function() {
                // Recalculate mobile state
                const newIsMobile = window.innerWidth <= 768;
                
                // Trigger resize event
                $(window).trigger('resize');
                
                // Re-run layout fixes
                fixContainerWidths();
                fixHorizontalScrolling();
                
                // Update viewport height for iOS
                if (isIOS) {
                    const vh = window.innerHeight * 0.01;
                    document.documentElement.style.setProperty('--vh', vh + 'px');
                }
            }, 500);
        });
    }

    /**
     * Fix touch scrolling issues
     */
    function fixTouchScrolling() {
        if (!isMobile) return;

        // Fix momentum scrolling
        $('body, .container, .main-content').css({
            '-webkit-overflow-scrolling': 'touch',
            'overflow-scrolling': 'touch'
        });

        // Prevent horizontal scroll on touch
        let startX = 0;
        let startY = 0;

        $(document).on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
            startY = e.originalEvent.touches[0].clientY;
        });

        $(document).on('touchmove', function(e) {
            if (!startX || !startY) return;

            const xDiff = startX - e.originalEvent.touches[0].clientX;
            const yDiff = startY - e.originalEvent.touches[0].clientY;

            // If horizontal swipe is greater than vertical, prevent default
            if (Math.abs(xDiff) > Math.abs(yDiff)) {
                // Only prevent if it would cause horizontal scroll
                if ((xDiff > 0 && $(window).scrollLeft() >= $(document).width() - $(window).width()) ||
                    (xDiff < 0 && $(window).scrollLeft() <= 0)) {
                    e.preventDefault();
                }
            }

            startX = 0;
            startY = 0;
        });
    }

    /**
     * Utility function to force layout recalculation
     */
    function forceLayoutRecalc() {
        $('body').hide().show(0);
    }

    /**
     * Add utility functions to window object
     */
    window.TendealMobileFix = {
        fixLayout: function() {
            fixContainerWidths();
            fixHorizontalScrolling();
            forceLayoutRecalc();
        },
        
        checkScrollIssues: function() {
            fixHorizontalScrolling();
        },
        
        debugInfo: function() {
            addMobileDebugInfo();
        },
        
        isMobile: function() {
            return window.innerWidth <= 768;
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        initMobileLayoutFixes();
        
        // Run fixes again after a short delay to catch dynamically loaded content
        setTimeout(function() {
            fixContainerWidths();
            fixHorizontalScrolling();
        }, 1000);
    });

    // Run fixes when window loads (after all resources)
    $(window).on('load', function() {
        setTimeout(function() {
            fixContainerWidths();
            fixHorizontalScrolling();
        }, 500);
    });

})(jQuery);
