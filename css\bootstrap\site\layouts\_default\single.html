{{ define "main" }}
  <header class="py-5 border-bottom">
  <div class="container-xxl bd-gutter pt-md-1 pb-md-4">
      <div class="row">
        <div class="col-xl-8">
          <h1 class="bd-title mt-0">{{ .Title | markdownify }}</h1>
          <p class="bd-lead">{{ .Page.Params.Description | markdownify }}</p>
          {{ if eq .Title "Examples" }}
          <div class="d-flex flex-column flex-md-row gap-3">
            <a href="{{ .Site.Params.download.dist_examples }}" class="btn btn-lg bd-btn-lg btn-bd-primary d-flex align-items-center justify-content-center fw-semibold" onclick="ga('send', 'event', 'Examples', 'Hero', 'Download Examples');">
              <svg class="bi me-2" aria-hidden="true"><use xlink:href="#box-seam"></use></svg>
              Download examples
            </a>
            <a href="{{ .Site.Params.download.source }}" class="btn btn-lg bd-btn-lg btn-outline-secondary" onclick="ga('send', 'event', 'Examples', 'Hero', 'Download');">
              Download source code
            </a>
          </div>
          {{ end }}
        </div>
        <div class="col-xl-4 d-lg-flex justify-content-xl-end">
          {{ partial "ads" . }}
        </div>
      </div>
    </div>
  </header>

  <main class="bd-content order-1 py-5" id="content">
    <div class="container-xxl bd-gutter">
      {{ .Content }}

      {{ if eq .Title "Examples" }}
        <hr class="my-5">
        <div class="container">
          <div class="text-center">
            <div class="masthead-followup-icon d-inline-block mb-2 text-bg-danger">
              {{ partial "icons/droplet-fill.svg" (dict "width" "32" "height" "32") }}
            </div>
            <h2 class="display-6 fw-normal">Go further with Bootstrap Themes</h2>
            <p class="col-md-10 col-lg-8 mx-auto lead">
              Need something more than these examples? Take Bootstrap to the next level with premium themes from the <a href="{{ .Site.Params.themes }}">official Bootstrap Themes marketplace</a>. They’re built as their own extended frameworks, rich with new components and plugins, documentation, and powerful build tools.
            </p>
            <a href="{{ .Site.Params.themes }}" class="btn btn-lg btn-outline-primary mb-3">Browse themes</a>
          </div>
          <img class="d-block img-fluid mt-3 mx-auto" srcset="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-themes-collage.png,
                                                              /docs/{{ .Site.Params.docs_version }}/assets/img/<EMAIL> 2x"
                                                        src="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-themes-collage.png"
                                                        alt="Bootstrap Themes" width="1150" height="320" loading="lazy">
        </div>
      {{ end }}
    </div>
  </main>
{{ end }}
