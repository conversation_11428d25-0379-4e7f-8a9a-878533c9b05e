/**
 * Search Functionality Fixes and Enhancements
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initSearchFunctionality();
        handleSearchFormSubmission();
        enhanceMobileSearch();
        addSearchLoadingStates();
    });

    /**
     * Initialize search functionality
     */
    function initSearchFunctionality() {
        // Check if AWS search is working
        if (typeof aws_vars !== 'undefined') {
            console.log('AWS Search plugin detected');
        } else {
            console.log('AWS Search plugin not detected, using fallback');
            enhanceFallbackSearch();
        }

        // Add search form validation
        $('.search-form, .aws-search-form, .woocommerce-product-search').on('submit', function(e) {
            const searchInput = $(this).find('input[name="s"]');
            const searchTerm = searchInput.val().trim();

            if (searchTerm.length < 2) {
                e.preventDefault();
                showSearchMessage('Please enter at least 2 characters to search', 'warning');
                searchInput.focus();
                return false;
            }
        });
    }

    /**
     * Handle search form submission
     */
    function handleSearchFormSubmission() {
        // Handle all search forms
        $('.search-form, .aws-search-form, .woocommerce-product-search').on('submit', function(e) {
            const form = $(this);
            const searchTerm = form.find('input[name="s"]').val().trim();

            if (searchTerm) {
                // Add loading state
                form.addClass('searching');
                form.find('.search-submit, .aws-form-btn').prop('disabled', true);

                // Ensure product post type is set
                if (!form.find('input[name="post_type"]').length) {
                    form.append('<input type="hidden" name="post_type" value="product">');
                }

                // For AWS forms, modify the action URL to include post_type
                if (form.hasClass('aws-search-form')) {
                    const currentAction = form.attr('action') || window.location.origin;
                    const separator = currentAction.includes('?') ? '&' : '?';
                    form.attr('action', currentAction + separator + 'post_type=product');
                }
            }
        });

        // Handle AWS search result clicks and "View all results" links
        $(document).on('click', '.aws-search-result a, .aws-search-more', function(e) {
            const href = $(this).attr('href');
            if (href && href.includes('/?s=')) {
                // Modify the URL to include post_type=product
                const newHref = href.includes('post_type=') ? href : href + '&post_type=product';
                $(this).attr('href', newHref);
            }
        });

        // Handle AWS search form "Enter" key and submit
        $(document).on('keypress', '.aws-search-field', function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                const searchTerm = $(this).val().trim();
                if (searchTerm.length >= 2) {
                    const searchUrl = window.location.origin + '/?s=' + encodeURIComponent(searchTerm) + '&post_type=product';
                    window.location.href = searchUrl;
                }
            }
        });
    }

    /**
     * Enhance mobile search experience
     */
    function enhanceMobileSearch() {
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // Prevent zoom on iOS
            $('.search-field, .aws-search-field').attr({
                'autocomplete': 'off',
                'autocorrect': 'off',
                'autocapitalize': 'off',
                'spellcheck': 'false'
            });

            // Handle search focus
            $('.search-field, .aws-search-field').on('focus', function() {
                $('html, body').animate({
                    scrollTop: $(this).offset().top - 100
                }, 300);
            });
        }
    }

    /**
     * Add loading states to search
     */
    function addSearchLoadingStates() {
        // Add loading state to AWS search if available
        if (typeof aws_vars !== 'undefined') {
            $(document).on('aws_init', function() {
                $('.aws-search-form').on('submit', function() {
                    $(this).addClass('searching');
                });
            });
        }

        // Add loading state to regular search forms
        $('.search-form, .woocommerce-product-search').on('submit', function() {
            const form = $(this);
            form.addClass('searching');

            // Add loading indicator
            const submitBtn = form.find('button[type="submit"], input[type="submit"]');
            const originalText = submitBtn.html() || submitBtn.val();

            if (submitBtn.is('button')) {
                submitBtn.html('<i class="loading-spinner"></i>');
            } else {
                submitBtn.val('Searching...');
            }

            // Store original text for restoration if needed
            submitBtn.data('original-text', originalText);
        });
    }

    /**
     * Enhance fallback search functionality
     */
    function enhanceFallbackSearch() {
        // Add autocomplete functionality for fallback search
        $('.search-field').on('input', function() {
            const searchTerm = $(this).val().trim();

            if (searchTerm.length >= 2) {
                // Debounce the search
                clearTimeout($(this).data('search-timeout'));
                $(this).data('search-timeout', setTimeout(function() {
                    // You could add AJAX autocomplete here if needed
                }, 300));
            }
        });

        // Add keyboard navigation
        $('.search-field').on('keydown', function(e) {
            if (e.key === 'Enter') {
                $(this).closest('form').submit();
            }
        });
    }

    /**
     * Show search messages
     */
    function showSearchMessage(message, type = 'info') {
        const messageClass = `search-message search-message-${type}`;
        const messageHtml = `<div class="${messageClass}">${message}</div>`;

        // Remove existing messages
        $('.search-message').remove();

        // Add new message
        $('.search-wrapper').append(messageHtml);

        // Auto-remove after 3 seconds
        setTimeout(function() {
            $('.search-message').fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    /**
     * Handle search results page enhancements
     */
    if ($('body').hasClass('search-results') || $('body.search').length) {
        // Add search result interactions
        $('.search-results-products .product').hover(
            function() {
                $(this).addClass('product-hover');
            },
            function() {
                $(this).removeClass('product-hover');
            }
        );

        // Track search analytics (if needed)
        const searchQuery = new URLSearchParams(window.location.search).get('s');
        if (searchQuery) {
            console.log('Search performed:', searchQuery);
            // You could send analytics data here
        }
    }

    /**
     * Fix AWS search issues if detected
     */
    function fixAWSSearchIssues() {
        // Check if AWS search container exists but is not working
        if ($('.aws-container').length && !$('.aws-search-field').length) {
            console.warn('AWS Search container found but search field missing');

            // Try to reinitialize AWS search
            if (typeof aws_init === 'function') {
                setTimeout(function() {
                    aws_init();
                }, 1000);
            }
        }
    }

    // Run AWS fix check after a delay
    setTimeout(fixAWSSearchIssues, 2000);

    /**
     * Handle window resize for responsive search
     */
    $(window).on('resize', function() {
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            enhanceMobileSearch();
        }
    });

    /**
     * Debug search functionality (for administrators)
     */
    if (window.location.search.includes('search_debug=1')) {
        console.log('Search Debug Mode Enabled');
        console.log('AWS Search Available:', typeof aws_vars !== 'undefined');
        console.log('Search Forms Found:', $('.search-form, .aws-search-form, .woocommerce-product-search').length);
        console.log('Current URL:', window.location.href);
        console.log('Search Query:', new URLSearchParams(window.location.search).get('s'));
    }

})(jQuery);
