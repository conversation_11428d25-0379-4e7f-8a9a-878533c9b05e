name: cspell

on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

env:
  FORCE_COLOR: 2

permissions:
  contents: read

jobs:
  cspell:
    permissions:
      # allow streetsidesoftware/cspell-action to fetch files for commits and PRs
      contents: read
      pull-requests: read
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Run cspell
        uses: streetsidesoftware/cspell-action@v5
        with:
          config: ".cspell.json"
          files: "**/*.md"
          inline: error
          incremental_files_only: false
