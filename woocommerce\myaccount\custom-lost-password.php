<?php
/**
 * Custom Lost Password Form
 *
 * This template overrides the default WooCommerce lost password form.
 *
 * @package tendeal
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Ensure CSS is loaded
wp_enqueue_style('tendeal-account-pages', get_stylesheet_directory_uri() . '/css/account-pages.css');

get_header();

do_action('woocommerce_before_lost_password_form');
?>

<div class="account-page-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo esc_url(home_url('/')); ?>">Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Did you forget your password?</li>
        </ol>
    </nav>

    <h1 class="account-page-title">did you forget<br>your password ?</h1>

    <p class="recovery-message">
        Instructions on how to reset your password<br>
        will be sent to your email.
    </p>

    <form method="post" class="account-form woocommerce-ResetPassword lost_reset_password">
        <div class="form-group">
            <label for="user_login"><?php esc_html_e('Email address', 'woocommerce'); ?></label>
            <input class="form-control" type="email" name="user_login" id="user_login" autocomplete="username" placeholder="Email address" />
        </div>

        <div class="clear"></div>

        <?php do_action('woocommerce_lostpassword_form'); ?>

        <input type="hidden" name="wc_reset_password" value="true" />
        <button type="submit" class="btn-submit" value="<?php esc_attr_e('Send', 'woocommerce'); ?>"><?php esc_html_e('Send', 'woocommerce'); ?></button>

        <?php wp_nonce_field('lost_password', 'woocommerce-lost-password-nonce'); ?>
    </form>
</div>

<?php
do_action('woocommerce_after_lost_password_form');
get_footer();
?>
