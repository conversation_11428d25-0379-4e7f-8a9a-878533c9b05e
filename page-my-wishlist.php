<?php
/**
 * Template Name: My Wishlist Template
 *
 * A clean, working wishlist template with modern design
 */

get_header();

// Simple and reliable wishlist item removal
if (isset($_GET['remove_item']) && isset($_GET['_wpnonce'])) {
    $product_id = intval($_GET['remove_item']);
    $nonce = sanitize_text_field($_GET['_wpnonce']);

    if (wp_verify_nonce($nonce, 'remove_wishlist_' . $product_id)) {
        // Direct database removal - most reliable method
        global $wpdb;
        $table_name = $wpdb->prefix . 'yith_wcwl';

        $user_id = get_current_user_id();
        $where_conditions = array('prod_id' => $product_id);

        if ($user_id > 0) {
            $where_conditions['user_id'] = $user_id;
        } else {
            // For guest users, use session
            $session_id = isset($_COOKIE['yith_wcwl_session_' . COOKIEHASH]) ? $_COOKIE['yith_wcwl_session_' . COOKIEHASH] : '';
            if ($session_id) {
                $where_conditions['session_id'] = $session_id;
            }
        }

        $deleted = $wpdb->delete($table_name, $where_conditions);

        // Set a flag for showing message without redirect
        $removal_result = $deleted > 0 ? 'success' : 'failed';
    }
}

// Get wishlist items
$wishlist_items = array();
if (function_exists('YITH_WCWL')) {
    $wishlist_items = YITH_WCWL()->get_products(array(
        'wishlist_id' => 'all',
        'user_id' => get_current_user_id(),
    ));
}
?>

<div class="entry-content">
    <div class="custom-wishlist-container">
        <h1 class="custom-wishlist-title"><?php esc_html_e('My Wishlist', 'woocommerce'); ?></h1>

        <?php
        // Show status messages
        if (isset($removal_result)) {
            if ($removal_result === 'success') {
                echo '<div class="wishlist-message success">✓ Item removed from wishlist successfully!</div>';
            } else {
                echo '<div class="wishlist-message error">✗ Failed to remove item. Please try again.</div>';
            }
        }
        ?>

        <?php if (!empty($wishlist_items)) : ?>
            <div class="custom-wishlist-header">
                <div class="custom-wishlist-header-checkbox">
                    <input type="checkbox" id="select-all" name="select-all">
                    <label for="select-all"><?php esc_html_e('Select all', 'woocommerce'); ?></label>
                </div>
                <button type="button" id="delete-selected" class="custom-delete-selected">
                    <i class="bi bi-trash3"></i>
                    <?php esc_html_e('Delete selected items', 'woocommerce'); ?>
                </button>
            </div>

            <div class="custom-wishlist-grid">
                <?php foreach ($wishlist_items as $wishlist_item) :
                    $product = wc_get_product($wishlist_item['prod_id']);
                    if (!$product) continue;

                    $product_permalink = $product->is_visible() ? $product->get_permalink() : '';
                    $store_name = 'Laptop Store'; // Default store name
                    $remove_url = add_query_arg(array(
                        'remove_item' => $product->get_id(),
                        '_wpnonce' => wp_create_nonce('remove_wishlist_' . $product->get_id())
                    ));
                ?>
                    <div class="wishlist-item-row" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                        <div class="item-checkbox">
                            <input type="checkbox" class="wishlist-item-checkbox"
                                   value="<?php echo esc_attr($product->get_id()); ?>">
                        </div>

                        <div class="item-content">
                            <div class="product-image">
                                <a href="<?php echo esc_url($product_permalink); ?>">
                                    <?php echo $product->get_image('medium'); ?>
                                </a>
                            </div>

                            <div class="product-details">
                                <h3 class="product-title">
                                    <a href="<?php echo esc_url($product_permalink); ?>">
                                        <?php echo esc_html($product->get_name()); ?>
                                    </a>
                                </h3>

                                <div class="store-info">
                                    <i class="bi bi-shop"></i>
                                    <span class="store-name"><?php echo esc_html($store_name); ?></span>
                                </div>

                                <div class="product-rating">
                                    <?php
                                    if ($product->get_average_rating()) {
                                        echo wc_get_rating_html($product->get_average_rating());
                                        echo '<span class="rating-text">' . number_format($product->get_average_rating(), 1) . '</span>';
                                        echo '<span class="review-count">' . $product->get_review_count() . ' ' . esc_html(_n('Review', 'Reviews', $product->get_review_count(), 'woocommerce')) . '</span>';
                                    } else {
                                        echo '<div class="rating-display no-reviews">';
                                        echo '<div class="stars-empty">☆☆☆☆☆</div>';
                                        echo '<span class="no-reviews-text">' . esc_html__('No reviews', 'woocommerce') . '</span>';
                                        echo '</div>';
                                    }
                                    ?>
                                </div>

                                <div class="product-categories">
                                    <?php
                                    $categories = get_the_terms($product->get_id(), 'product_cat');
                                    if ($categories && !is_wp_error($categories)) {
                                        foreach (array_slice($categories, 0, 2) as $category) {
                                            echo '<span class="category-tag">' . esc_html($category->name) . '</span>';
                                        }
                                    } else {
                                        echo '<span class="category-tag">Gaming Laptops</span>';
                                        echo '<span class="category-tag">Laptops</span>';
                                    }
                                    ?>
                                </div>

                                <div class="product-actions">
                                    <form method="post" action="<?php echo esc_url(wc_get_cart_url()); ?>" style="display: inline;">
                                        <button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>"
                                                class="btn-add-to-cart">
                                            <i class="bi bi-cart"></i> <?php esc_html_e('Add to cart', 'woocommerce'); ?>
                                        </button>
                                    </form>
                                    <a href="<?php echo esc_url($remove_url); ?>"
                                       class="btn-delete-item"
                                       onclick="return confirm('Remove this item from wishlist?');">
                                        <i class="bi bi-trash3"></i> <?php esc_html_e('Delete item', 'woocommerce'); ?>
                                    </a>
                                </div>
                            </div>

                            <div class="product-price-section">
                                <div class="price-amount">
                                    <?php echo $product->get_price_html(); ?>
                                </div>
                                <div class="tax-info">
                                    <?php esc_html_e('Tax:', 'woocommerce'); ?> 20.00 <?php esc_html_e('QAR', 'woocommerce'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else : ?>
            <div class="custom-wishlist-empty">
                <p><?php esc_html_e('Your wishlist is currently empty.', 'woocommerce'); ?></p>
                <a href="<?php echo esc_url(get_permalink(wc_get_page_id('shop'))); ?>"
                   class="button"><?php esc_html_e('Browse Products', 'woocommerce'); ?></a>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Complete Wishlist Styling */

/* Container */
.custom-wishlist-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Roboto', sans-serif;
  color: #333;
}

.custom-wishlist-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 25px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

/* Wishlist Messages */
.wishlist-message {
    padding: 12px 16px;
    margin-bottom: 20px;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}
.wishlist-message.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}
.wishlist-message.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Header */
.custom-wishlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.custom-wishlist-header-checkbox {
  display: flex;
  align-items: center;
}

.custom-wishlist-header-checkbox input[type="checkbox"] {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #ea9c00;
}

.custom-wishlist-header-checkbox label {
  font-weight: 500;
  cursor: pointer;
  color: #495057;
  font-size: 14px;
}

.custom-delete-selected {
  background-color: transparent;
  color: #dc3545;
  border: 1px solid #dc3545;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.custom-delete-selected:hover {
  background-color: #dc3545;
  color: white;
  transform: translateY(-1px);
}

.custom-delete-selected i {
  font-size: 14px;
}

/* Grid Layout */
.custom-wishlist-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

/* Item Row */
.wishlist-item-row {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.wishlist-item-row:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #ddd;
}

/* Checkbox */
.item-checkbox {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 2;
}

.item-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #ea9c00;
}

/* Content */
.item-content {
  display: flex;
  gap: 20px;
  margin-left: 40px;
  align-items: flex-start;
}

/* Product Image */
.product-image {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e9ecef;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Product Details */
.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
}

.product-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
}

.product-title a {
  color: #333;
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-title a:hover {
  color: #ea9c00;
}

/* Store Info */
.store-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.store-info i {
  color: #6c757d;
  font-size: 12px;
}

.store-name {
  color: #0073aa;
  font-weight: 500;
}

/* Rating */
.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.product-rating .star-rating {
  display: inline-block;
  position: relative;
  font-size: 14px;
  line-height: 1;
}

.product-rating .rating-text {
  font-weight: 600;
  color: #333;
}

.product-rating .review-count {
  color: #6c757d;
}

.rating-display.no-reviews {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stars-empty {
  color: #ddd;
  font-size: 14px;
  letter-spacing: 1px;
}

.no-reviews-text {
  color: #6c757d;
  font-size: 14px;
  font-style: italic;
}

/* Categories */
.product-categories {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-tag {
  background: #f8f9fa;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid #e9ecef;
}

.category-tag:hover {
  background: #e9ecef;
  color: #495057;
}

/* Actions */
.product-actions {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}

.btn-add-to-cart,
.btn-delete-item {
  flex: 1;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  text-decoration: none;
  border: none;
}

.btn-add-to-cart {
  background: transparent;
  color: #ea9c00;
  border: 1px solid #ea9c00;
}

.btn-add-to-cart:hover {
  background: #ea9c00;
  color: white;
  transform: translateY(-1px);
}

.btn-delete-item {
  background: transparent;
  color: #dc3545;
  border: 1px solid #dc3545;
}

.btn-delete-item:hover {
  background: #dc3545;
  color: white;
  transform: translateY(-1px);
}

.btn-add-to-cart i,
.btn-delete-item i {
  font-size: 12px;
}

/* Price Section */
.product-price-section {
  flex-shrink: 0;
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 4px;
  min-width: 120px;
}

.price-amount {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
}

.price-amount .woocommerce-Price-amount {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

.price-amount .woocommerce-Price-currencySymbol {
  font-size: 16px;
  font-weight: 600;
}

.tax-info {
  font-size: 12px;
  color: #6c757d;
  white-space: nowrap;
}

/* Empty State */
.custom-wishlist-empty {
  background-color: #f9f9f9;
  padding: 30px;
  text-align: center;
  border-radius: 4px;
  margin-bottom: 30px;
}

.custom-wishlist-empty p {
  font-size: 16px;
  color: #555;
  margin-bottom: 20px;
}

.custom-wishlist-empty .button {
  background-color: #ea9c00;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  text-decoration: none;
  display: inline-block;
}

.custom-wishlist-empty .button:hover {
  background-color: #d18700;
}

/* Responsive Design */
@media (max-width: 992px) {
  .product-image {
    width: 100px;
    height: 100px;
  }

  .item-content {
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .custom-wishlist-container {
    padding: 15px;
  }

  .custom-wishlist-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .custom-wishlist-header-checkbox {
    margin-bottom: 0;
  }

  .custom-wishlist-grid {
    gap: 12px;
  }

  .wishlist-item-row {
    padding: 15px;
  }

  .item-content {
    flex-direction: column;
    gap: 15px;
    margin-left: 35px;
  }

  .product-image {
    width: 100%;
    height: 150px;
    align-self: center;
    max-width: 200px;
  }

  .product-price-section {
    align-items: flex-start;
    text-align: left;
    min-width: auto;
  }

  .product-actions {
    flex-direction: column;
    gap: 8px;
  }

  .btn-add-to-cart,
  .btn-delete-item {
    flex: none;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .custom-wishlist-container {
    padding: 10px;
  }

  .custom-wishlist-header {
    padding: 12px;
  }

  .wishlist-item-row {
    padding: 12px;
  }

  .item-content {
    margin-left: 30px;
  }

  .product-image {
    height: 120px;
  }

  .product-title {
    font-size: 14px;
  }

  .price-amount {
    font-size: 18px;
  }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Select all functionality
    $('#select-all').on('change', function() {
        $('.wishlist-item-checkbox').prop('checked', $(this).prop('checked'));
    });

    // Bulk delete functionality - simple and reliable
    $('#delete-selected').on('click', function(e) {
        e.preventDefault();

        const selectedItems = $('.wishlist-item-checkbox:checked');

        if (selectedItems.length === 0) {
            alert('Please select items to delete');
            return;
        }

        if (!confirm('Remove ' + selectedItems.length + ' selected item(s) from wishlist?')) {
            return;
        }

        // Remove selected items by hiding them and then reloading page
        selectedItems.each(function() {
            const productId = $(this).val();
            const $row = $(this).closest('.wishlist-item-row');

            // Fade out the row
            $row.fadeOut(300);
        });

        // After animation, redirect to remove the first item (which will remove all via URL)
        setTimeout(function() {
            const firstProductId = selectedItems.first().val();
            const removeUrl = window.location.href +
                (window.location.href.indexOf('?') > -1 ? '&' : '?') +
                'remove_item=' + firstProductId +
                '&_wpnonce=' + '<?php echo wp_create_nonce('remove_wishlist_'); ?>' + firstProductId;

            window.location.href = removeUrl;
        }, 500);
    });
});
</script>

<?php get_footer(); ?>
