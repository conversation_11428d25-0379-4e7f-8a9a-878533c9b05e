<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feather Icons Example - TenDeal</title>
    <link rel="stylesheet" href="css/main.css">
    <!-- Include individual CSS files for demonstration -->
    <link rel="stylesheet" href="css/components/icons.css">
    <link rel="stylesheet" href="css/base/variables.css">
    <style>
        .demo-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
        }
        .demo-title {
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }
        .demo-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            border: 1px solid #eee;
            border-radius: 0.5rem;
            text-align: center;
        }
        .demo-item p {
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }
        .color-demo {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .size-demo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .app-bar-demo {
            background-color: #fff;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .button-demo {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .form-demo {
            max-width: 400px;
        }
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background-color: var(--bs-primary);
            color: white;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #d68c00;
        }
        .form-control {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Feather Icons for TenDeal</h1>
        <p>This page demonstrates how to use Feather icons in your TenDeal website.</p>

        <div class="demo-section">
            <h2 class="demo-title">Basic Icons</h2>
            <div class="demo-grid">
                <div class="demo-item">
                    <i data-feather="shopping-cart"></i>
                    <p>shopping-cart</p>
                </div>
                <div class="demo-item">
                    <i data-feather="user"></i>
                    <p>user</p>
                </div>
                <div class="demo-item">
                    <i data-feather="heart"></i>
                    <p>heart</p>
                </div>
                <div class="demo-item">
                    <i data-feather="search"></i>
                    <p>search</p>
                </div>
                <div class="demo-item">
                    <i data-feather="menu"></i>
                    <p>menu</p>
                </div>
                <div class="demo-item">
                    <i data-feather="home"></i>
                    <p>home</p>
                </div>
                <div class="demo-item">
                    <i data-feather="phone"></i>
                    <p>phone</p>
                </div>
                <div class="demo-item">
                    <i data-feather="mail"></i>
                    <p>mail</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">Icon Sizes</h2>
            <div class="size-demo">
                <i data-feather="star" class="feather-sm"></i>
                <p>feather-sm (16px)</p>
            </div>
            <div class="size-demo">
                <i data-feather="star" class="feather-md"></i>
                <p>feather-md (24px - default)</p>
            </div>
            <div class="size-demo">
                <i data-feather="star" class="feather-lg"></i>
                <p>feather-lg (32px)</p>
            </div>
            <div class="size-demo">
                <i data-feather="star" class="feather-xl"></i>
                <p>feather-xl (48px)</p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">Icon Colors</h2>
            <div class="color-demo">
                <i data-feather="award" class="feather-primary"></i>
                <i data-feather="award" class="feather-secondary"></i>
                <i data-feather="award" class="feather-success"></i>
                <i data-feather="award" class="feather-danger"></i>
                <i data-feather="award" class="feather-warning"></i>
                <i data-feather="award" class="feather-info"></i>
                <i data-feather="award" class="feather-light"></i>
                <i data-feather="award" class="feather-dark"></i>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">Icons with Background</h2>
            <div class="color-demo">
                <div class="icon-bg">
                    <i data-feather="shopping-bag" class="feather-primary"></i>
                </div>
                <div class="icon-bg icon-bg-sm">
                    <i data-feather="shopping-bag" class="feather-sm feather-primary"></i>
                </div>
                <div class="icon-bg icon-bg-lg">
                    <i data-feather="shopping-bag" class="feather-lg feather-primary"></i>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">Icon Animations</h2>
            <div class="color-demo">
                <i data-feather="loader" class="feather-spin"></i>
                <i data-feather="bell" class="feather-pulse"></i>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">App Bar Example</h2>
            <div class="app-bar-demo">
                <div class="app-bar__inner">
                    <div class="icon-with-text">
                        <i data-feather="phone" class="feather-sm"></i>
                        <span>****** 567 890</span>
                    </div>
                    <div class="icon-with-text">
                        <i data-feather="mail" class="feather-sm"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="icon-with-text">
                        <i data-feather="map-pin" class="feather-sm"></i>
                        <span>Store Locator</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">Buttons with Icons</h2>
            <div class="button-demo">
                <button class="btn">
                    <i data-feather="shopping-cart" class="feather-sm"></i>
                    Add to Cart
                </button>
                <button class="btn">
                    <i data-feather="heart" class="feather-sm"></i>
                    Wishlist
                </button>
                <button class="btn">
                    <i data-feather="share-2" class="feather-sm"></i>
                    Share
                </button>
                <button class="btn btn-icon">
                    <i data-feather="search" class="feather-sm"></i>
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">Form Controls with Icons</h2>
            <div class="form-demo">
                <div class="form-control-icon form-control-icon-left">
                    <i data-feather="search" class="feather-sm"></i>
                    <input type="text" class="form-control" placeholder="Search...">
                </div>
                <br>
                <div class="form-control-icon form-control-icon-right">
                    <input type="email" class="form-control" placeholder="Email">
                    <i data-feather="mail" class="feather-sm"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Feather Icons JavaScript -->
    <script src="https://unpkg.com/feather-icons"></script>
    <script>
        // Initialize Feather icons
        document.addEventListener('DOMContentLoaded', function() {
            feather.replace();
        });
    </script>
</body>
</html>
