/**
 * Updated Product Card JavaScript
 *
 * Handles interactions for the modern product card design
 */

(function ($) {
    'use strict';

    // Initialize product card functionality
    function initProductCard() {
        handleAddToCart();
        handleWishlist();
        handleCompare();
        handleQuickView();
        handleSecondaryActions();
        addLoadingStates();
    }

    /**
     * Handle Add to Cart functionality
     */
    function handleAddToCart() {
        $(document).on('click', '.add-to-cart-btn', function (e) {
            e.preventDefault();

            const $button = $(this);
            const productId = $button.data('product-id');

            // Skip if it's a link (variable products)
            if ($button.is('a')) {
                return true;
            }

            // Add loading state
            $button.addClass('loading');
            $button.prop('disabled', true);

            // Store original content
            const originalContent = $button.html();
            $button.html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 11-6.219-8.56"/></svg> Adding...');

            // AJAX Add to Cart
            $.ajax({
                url: wc_add_to_cart_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'woocommerce_add_to_cart',
                    product_id: productId,
                    quantity: 1
                },
                success: function (response) {
                    if (response.error) {
                        showNotification('Error: ' + response.error, 'error');
                    } else {
                        // Update cart count
                        updateCartCount();

                        // Show success message
                        showNotification('Product added to cart!', 'success');

                        // Update button temporarily
                        $button.html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20,6 9,17 4,12"></polyline></svg> Added!');
                        $button.addClass('success');

                        // Reset button after 2 seconds
                        setTimeout(function () {
                            $button.html(originalContent);
                            $button.removeClass('success');
                        }, 2000);
                    }
                },
                error: function () {
                    showNotification('Error adding product to cart', 'error');
                },
                complete: function () {
                    $button.removeClass('loading');
                    $button.prop('disabled', false);
                }
            });
        });
    }

    /**
     * Handle Wishlist functionality
     */
    function handleWishlist() {
        $(document).on('click', '.wishlist-btn', function (e) {
            e.preventDefault();

            const $button = $(this);
            const productId = $button.data('product-id');

            // Toggle active state
            $button.toggleClass('active');

            // Add loading state
            $button.addClass('loading');

            if ($button.hasClass('active')) {
                // Add to wishlist
                if (typeof yith_wcwl_l10n !== 'undefined') {
                    // Use YITH Wishlist AJAX if available
                    $.ajax({
                        url: yith_wcwl_l10n.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'add_to_wishlist',
                            product_id: productId,
                            context: 'frontend'
                        },
                        success: function (response) {
                            if (response.result === 'true') {
                                showNotification('Product added to wishlist!', 'success');
                            } else {
                                showNotification('Error adding to wishlist', 'error');
                                $button.removeClass('active');
                            }
                        },
                        error: function () {
                            showNotification('Error adding to wishlist', 'error');
                            $button.removeClass('active');
                        },
                        complete: function () {
                            $button.removeClass('loading');
                        }
                    });
                } else {
                    // Fallback for other wishlist plugins or custom implementation
                    showNotification('Product added to wishlist!', 'success');
                    $button.removeClass('loading');
                }
            } else {
                // Remove from wishlist
                showNotification('Product removed from wishlist', 'info');
                $button.removeClass('loading');
            }
        });
    }

    /**
     * Handle Compare functionality (Enhanced with multiple fallbacks)
     */
    function handleCompare() {
        $(document).on('click', '.compare-btn, .yith-compare-btn', function (e) {
            e.preventDefault();

            const $button = $(this);
            const productId = $button.data('product-id') || $button.data('id');

            if (!productId) {
                showNotification('Product ID not found', 'error');
                return;
            }

            // Add loading state
            $button.addClass('loading');

            // Try multiple compare methods in order of preference
            tryCompareMethod1($button, productId);
        });

        // Method 1: YITH Compare with proper nonce
        function tryCompareMethod1($button, productId) {
            if (typeof yith_woocompare !== 'undefined' && yith_woocompare.ajaxurl) {
                $.ajax({
                    url: yith_woocompare.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'yith_woocompare_add_product',
                        id: productId,
                        context: 'frontend',
                        _wpnonce: yith_woocompare.nonceadd || ''
                    },
                    success: function (response) {
                        handleCompareSuccess($button, response);
                    },
                    error: function () {
                        tryCompareMethod2($button, productId);
                    }
                });
            } else {
                tryCompareMethod2($button, productId);
            }
        }

        // Method 2: Alternative YITH Compare object
        function tryCompareMethod2($button, productId) {
            if (typeof yith_woocompare_obj !== 'undefined') {
                $.ajax({
                    url: yith_woocompare_obj.ajaxurl || wc_add_to_cart_params.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'yith_woocompare_add_product',
                        id: productId,
                        context: 'frontend'
                    },
                    success: function (response) {
                        handleCompareSuccess($button, response);
                    },
                    error: function () {
                        tryCompareMethod3($button, productId);
                    }
                });
            } else {
                tryCompareMethod3($button, productId);
            }
        }

        // Method 3: Custom compare implementation
        function tryCompareMethod3($button, productId) {
            $.ajax({
                url: wc_add_to_cart_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'tendeal_add_to_compare',
                    product_id: productId,
                    nonce: wc_add_to_cart_params.compare_nonce || wc_add_to_cart_params.nonce || ''
                },
                success: function (response) {
                    handleCompareSuccess($button, response);
                },
                error: function () {
                    tryCompareMethod4($button, productId);
                }
            });
        }

        // Method 4: Final fallback to YITH action
        function tryCompareMethod4($button, productId) {
            $.ajax({
                url: wc_add_to_cart_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'yith_woocompare_add_product',
                    id: productId,
                    product_id: productId,
                    context: 'frontend'
                },
                success: function (response) {
                    handleCompareSuccess($button, response);
                },
                error: function () {
                    $button.removeClass('loading');
                    showNotification('Compare functionality not available. Please install YITH Compare plugin.', 'warning');
                }
            });
        }

        // Handle successful compare response
        function handleCompareSuccess($button, response) {
            $button.removeClass('loading');

            if (response && (response.success || response.result === 'success')) {
                showNotification('Product added to compare!', 'success');
                updateCompareCount();

                // Add visual feedback
                $button.addClass('added');
                setTimeout(function () {
                    $button.removeClass('added');
                }, 2000);
            } else if (response && (response.result === 'exists' || (response.data && response.data.message && response.data.message.includes('already')))) {
                showNotification('Product already in compare list', 'info');
            } else if (response && response.data && response.data.message) {
                showNotification(response.data.message, 'info');
            } else {
                showNotification('Product added to compare!', 'success');
                updateCompareCount();
            }
        }
    }

    /**
     * Handle Quick View functionality
     */
    function handleQuickView() {
        $(document).on('click', '.quick-view-btn', function (e) {
            e.preventDefault();

            const $button = $(this);
            const productId = $button.data('product-id');

            // Add loading state
            $button.addClass('loading');

            // AJAX Quick View
            $.ajax({
                url: wc_add_to_cart_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'product_quick_view',
                    product_id: productId
                },
                success: function (response) {
                    if (response.success) {
                        // Show quick view modal
                        $('body').append(response.data.html);
                        $('#quick-view-modal').modal('show');
                    } else {
                        showNotification('Failed to load product details', 'error');
                    }
                },
                error: function () {
                    showNotification('Error occurred. Please try again.', 'error');
                },
                complete: function () {
                    $button.removeClass('loading');
                }
            });
        });
    }

    /**
     * Handle Secondary Actions functionality
     */
    function handleSecondaryActions() {
        // Secondary Add to Cart
        $(document).on('click', '.secondary-add-cart-btn', function (e) {
            e.preventDefault();

            const $button = $(this);
            const productId = $button.data('product-id');

            // Add loading state
            $button.addClass('loading');
            $button.prop('disabled', true);

            // Store original content
            const originalContent = $button.html();
            $button.html('<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 11-6.219-8.56"/></svg> Adding...');

            // AJAX Add to Cart
            $.ajax({
                url: wc_add_to_cart_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'woocommerce_add_to_cart',
                    product_id: productId,
                    quantity: 1
                },
                success: function (response) {
                    if (response.error) {
                        showNotification('Error: ' + response.error, 'error');
                    } else {
                        // Update cart count
                        updateCartCount();

                        // Show success message
                        showNotification('Product added to cart!', 'success');

                        // Update button temporarily
                        $button.html('<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20,6 9,17 4,12"></polyline></svg> Added!');
                        $button.addClass('success');

                        // Reset button after 2 seconds
                        setTimeout(function () {
                            $button.html(originalContent);
                            $button.removeClass('success');
                        }, 2000);
                    }
                },
                error: function () {
                    showNotification('Error adding product to cart', 'error');
                },
                complete: function () {
                    $button.removeClass('loading');
                    $button.prop('disabled', false);
                }
            });
        });

        // Secondary Remove from Favorite
        $(document).on('click', '.secondary-favorite-btn', function (e) {
            e.preventDefault();

            const $button = $(this);
            const productId = $button.data('product-id');

            // Add loading state
            $button.addClass('loading');

            // Toggle button state
            if ($button.hasClass('removed')) {
                // Add back to favorites
                $button.removeClass('removed');
                $button.html('<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg> Add to fave');
                showNotification('Product added to favorites!', 'success');
            } else {
                // Remove from favorites
                $button.addClass('removed');
                $button.html('<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg> Remove from fave');
                showNotification('Product removed from favorites', 'info');
            }

            $button.removeClass('loading');
        });
    }

    /**
     * Add loading states to buttons
     */
    function addLoadingStates() {
        // Add CSS for loading states
        if (!$('#product-card-loading-styles').length) {
            $('head').append(`
                <style id="product-card-loading-styles">
                    .modern-product-card .loading {
                        opacity: 0.7;
                        pointer-events: none;
                    }

                    .modern-product-card .loading svg {
                        /* No spinning animation */
                    }

                    .modern-product-card .success {
                        background: transparent !important;
                    }

                    /* Half star gradient */
                    .modern-product-card svg defs {
                        display: none;
                    }
                </style>
            `);
        }

        // Add half-star gradient definition
        if (!$('#half-star-gradient').length) {
            $('body').append(`
                <svg style="display: none;">
                    <defs>
                        <linearGradient id="half-star">
                            <stop offset="50%" stop-color="#ea9c00"/>
                            <stop offset="50%" stop-color="#ddd"/>
                        </linearGradient>
                    </defs>
                </svg>
            `);
        }
    }

    /**
     * Update cart count in header
     */
    function updateCartCount() {
        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'get_cart_count'
            },
            success: function (response) {
                if (response.success) {
                    $('.cart-count, .cart-counter').text(response.data.count);
                }
            }
        });
    }

    /**
     * Update compare count
     */
    function updateCompareCount() {
        // Update compare count if element exists
        const $compareCount = $('.compare-count');
        if ($compareCount.length) {
            const currentCount = parseInt($compareCount.text()) || 0;
            $compareCount.text(currentCount + 1);
        }
    }

    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        // Remove existing notifications
        $('.product-notification').remove();

        // Create notification
        const $notification = $(`
            <div class="product-notification notification-${type}">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `);

        // Add notification styles if not exists
        if (!$('#notification-styles').length) {
            $('head').append(`
                <style id="notification-styles">
                    .product-notification {
                        position: fixed;
                        top: 120px;
                        right: 120px;
                        padding: 12px 16px;
                        border-radius: 8px;
                        color: white;
                        font-weight: 500;
                        z-index: 9999;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        animation: slideIn 0.3s ease;
                    }

                    .notification-success { background: #28a745; }
                    .notification-error { background: #dc3545; }
                    .notification-info { background: #17a2b8; }

                    .notification-close {
                        background: none;
                        border: none;
                        color: white;
                        font-size: 18px;
                        cursor: pointer;
                        padding: 0;
                        margin-left: 10px;
                    }

                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                </style>
            `);
        }

        // Add to page
        $('body').append($notification);

        // Auto remove after 3 seconds
        setTimeout(function () {
            $notification.fadeOut(300, function () {
                $(this).remove();
            });
        }, 3000);

        // Handle close button
        $notification.find('.notification-close').on('click', function () {
            $notification.fadeOut(300, function () {
                $(this).remove();
            });
        });
    }

    // Initialize when document is ready
    $(document).ready(function () {
        initProductCard();
    });

})(jQuery);
