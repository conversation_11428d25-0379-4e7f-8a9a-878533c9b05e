name: JS Tests

on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

env:
  FORCE_COLOR: 2
  NODE: 20

permissions:
  contents: read

jobs:
  run:
    permissions:
      # allow coverallsapp/github-action to create new checks issues and fetch code
      checks: write
      contents: read
    name: JS Tests
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE }}
          cache: npm

      - name: Install npm dependencies
        run: npm ci

      - name: Run dist
        run: npm run js

      - name: Run JS tests
        run: npm run js-test

      - name: Run Coveralls
        uses: coverallsapp/github-action@v2
        if: ${{ !github.event.repository.fork }}
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
          path-to-lcov: "./js/coverage/lcov.info"
