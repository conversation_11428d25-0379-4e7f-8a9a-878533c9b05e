/**
 * Responsive Enhancements for Tendeal Theme
 * 
 * This file contains JavaScript enhancements for better mobile
 * and responsive experience.
 */

(function($) {
    'use strict';

    // Mobile detection
    const isMobile = window.innerWidth <= 768;
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    /**
     * Initialize responsive enhancements
     */
    function initResponsiveEnhancements() {
        handleMobileSearch();
        improveTouchTargets();
        handleMobileNavigation();
        optimizeImages();
        handleFormInputs();
        addMobileGestures();
        handleOrientationChange();
        optimizePerformance();
    }

    /**
     * Enhance mobile search functionality
     */
    function handleMobileSearch() {
        const searchField = $('.aws-search-field');
        
        if (searchField.length && isMobile) {
            // Prevent zoom on iOS
            searchField.attr('autocomplete', 'off');
            searchField.attr('autocorrect', 'off');
            searchField.attr('autocapitalize', 'off');
            searchField.attr('spellcheck', 'false');
            
            // Add mobile-friendly placeholder
            if (searchField.attr('placeholder')) {
                searchField.attr('placeholder', 'Search products...');
            }
            
            // Handle search focus on mobile
            searchField.on('focus', function() {
                if (isMobile) {
                    $('html, body').animate({
                        scrollTop: $(this).offset().top - 100
                    }, 300);
                }
            });
        }
    }

    /**
     * Improve touch targets for mobile
     */
    function improveTouchTargets() {
        if (!isTouch) return;

        // Add touch-friendly class to interactive elements
        const touchElements = [
            '.btn',
            '.nav-link',
            '.site-header__list a',
            '.mobile-nav-item a',
            '.woocommerce .button',
            '.category-menu-button'
        ];

        touchElements.forEach(selector => {
            $(selector).addClass('touch-target');
        });

        // Add visual feedback for touch
        $('.touch-target').on('touchstart', function() {
            $(this).addClass('touch-active');
        }).on('touchend touchcancel', function() {
            $(this).removeClass('touch-active');
        });
    }

    /**
     * Handle mobile navigation improvements
     */
    function handleMobileNavigation() {
        // Mobile menu toggle enhancement
        $('.menu-toggle').on('click', function(e) {
            e.preventDefault();
            const nav = $('.main-navigation');
            nav.toggleClass('mobile-open');
            $(this).toggleClass('active');
            
            // Prevent body scroll when menu is open
            if (nav.hasClass('mobile-open')) {
                $('body').addClass('menu-open');
            } else {
                $('body').removeClass('menu-open');
            }
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.main-navigation, .menu-toggle').length) {
                $('.main-navigation').removeClass('mobile-open');
                $('.menu-toggle').removeClass('active');
                $('body').removeClass('menu-open');
            }
        });

        // Handle mobile bottom navigation active states
        $('.mobile-nav-item a').on('click', function() {
            $('.mobile-nav-item a').removeClass('active');
            $(this).addClass('active');
        });
    }

    /**
     * Optimize images for responsive display
     */
    function optimizeImages() {
        // Add loading="lazy" to images below the fold
        $('img').each(function(index) {
            if (index > 3) { // Skip first few images
                $(this).attr('loading', 'lazy');
            }
        });

        // Handle responsive images
        $('.wp-post-image, .woocommerce-product-gallery__image img').each(function() {
            if (!$(this).hasClass('img-fluid')) {
                $(this).addClass('img-fluid');
            }
        });
    }

    /**
     * Enhance form inputs for mobile
     */
    function handleFormInputs() {
        // Add mobile-friendly attributes to form inputs
        $('input[type="email"]').attr('inputmode', 'email');
        $('input[type="tel"]').attr('inputmode', 'tel');
        $('input[type="number"]').attr('inputmode', 'numeric');
        $('input[type="url"]').attr('inputmode', 'url');

        // Handle quantity inputs
        $('.quantity input[type="number"]').each(function() {
            const $input = $(this);
            const $wrapper = $input.closest('.quantity');
            
            // Add mobile-friendly quantity controls
            if (isMobile && !$wrapper.find('.qty-btn').length) {
                $wrapper.addClass('mobile-quantity');
                $input.before('<button type="button" class="qty-btn qty-minus">-</button>');
                $input.after('<button type="button" class="qty-btn qty-plus">+</button>');
            }
        });

        // Handle quantity button clicks
        $(document).on('click', '.qty-btn', function(e) {
            e.preventDefault();
            const $btn = $(this);
            const $input = $btn.siblings('input[type="number"]');
            const currentVal = parseInt($input.val()) || 0;
            const min = parseInt($input.attr('min')) || 1;
            const max = parseInt($input.attr('max')) || 999;

            if ($btn.hasClass('qty-plus') && currentVal < max) {
                $input.val(currentVal + 1).trigger('change');
            } else if ($btn.hasClass('qty-minus') && currentVal > min) {
                $input.val(currentVal - 1).trigger('change');
            }
        });
    }

    /**
     * Add mobile gesture support
     */
    function addMobileGestures() {
        if (!isTouch) return;

        // Add swipe support for product galleries
        $('.woocommerce-product-gallery').each(function() {
            let startX = 0;
            let startY = 0;

            $(this).on('touchstart', function(e) {
                startX = e.originalEvent.touches[0].clientX;
                startY = e.originalEvent.touches[0].clientY;
            });

            $(this).on('touchmove', function(e) {
                if (!startX || !startY) return;

                const xDiff = startX - e.originalEvent.touches[0].clientX;
                const yDiff = startY - e.originalEvent.touches[0].clientY;

                if (Math.abs(xDiff) > Math.abs(yDiff)) {
                    // Horizontal swipe
                    if (xDiff > 50) {
                        // Swipe left - next image
                        $(this).find('.flex-next').trigger('click');
                    } else if (xDiff < -50) {
                        // Swipe right - previous image
                        $(this).find('.flex-prev').trigger('click');
                    }
                }

                startX = 0;
                startY = 0;
            });
        });
    }

    /**
     * Handle orientation change
     */
    function handleOrientationChange() {
        $(window).on('orientationchange', function() {
            // Delay to allow for orientation change to complete
            setTimeout(function() {
                // Recalculate mobile state
                const newIsMobile = window.innerWidth <= 768;
                
                // Trigger resize event
                $(window).trigger('resize');
                
                // Update mobile-specific elements
                if (newIsMobile !== isMobile) {
                    location.reload(); // Simple solution for major layout changes
                }
            }, 500);
        });
    }

    /**
     * Performance optimizations for mobile
     */
    function optimizePerformance() {
        // Debounce scroll events
        let scrollTimeout;
        $(window).on('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(function() {
                // Handle scroll-based functionality here
                handleScrollEffects();
            }, 16); // ~60fps
        });

        // Debounce resize events
        let resizeTimeout;
        $(window).on('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                handleResize();
            }, 250);
        });
    }

    /**
     * Handle scroll effects
     */
    function handleScrollEffects() {
        const scrollTop = $(window).scrollTop();
        
        // Hide/show mobile bottom navigation on scroll
        if (isMobile) {
            const $mobileNav = $('.mobile-bottom-nav');
            if (scrollTop > 100) {
                $mobileNav.addClass('scrolled');
            } else {
                $mobileNav.removeClass('scrolled');
            }
        }
    }

    /**
     * Handle window resize
     */
    function handleResize() {
        // Update mobile state
        const newIsMobile = window.innerWidth <= 768;
        
        // Close mobile menu if switching to desktop
        if (!newIsMobile && $('.main-navigation').hasClass('mobile-open')) {
            $('.main-navigation').removeClass('mobile-open');
            $('.menu-toggle').removeClass('active');
            $('body').removeClass('menu-open');
        }
    }

    /**
     * Add responsive utility functions to window
     */
    window.TendealResponsive = {
        isMobile: function() {
            return window.innerWidth <= 768;
        },
        isTouch: function() {
            return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        },
        getCurrentBreakpoint: function() {
            const width = window.innerWidth;
            if (width < 576) return 'xs';
            if (width < 768) return 'sm';
            if (width < 992) return 'md';
            if (width < 1200) return 'lg';
            if (width < 1400) return 'xl';
            return 'xxl';
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        initResponsiveEnhancements();
    });

})(jQuery);
