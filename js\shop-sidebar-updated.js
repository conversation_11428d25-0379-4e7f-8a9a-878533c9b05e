/**
 * Updated Shop Sidebar JavaScript
 */

(function($) {
  'use strict';

  // Initialize when document is ready
  $(document).ready(function() {
    // Initialize brand logo selection
    initBrandLogos();
    
    // Initialize price range slider
    initPriceRangeSlider();
    
    // Initialize filter actions
    initFilterActions();
  });

  /**
   * Initialize brand logo selection
   */
  function initBrandLogos() {
    $('.brand-logo-item').on('click', function() {
      $(this).toggleClass('active');
      
      // Update hidden input or data attribute for filtering
      const selectedBrands = [];
      $('.brand-logo-item.active').each(function() {
        selectedBrands.push($(this).data('brand'));
      });
      
      // Store selected brands for filtering
      $('#shop-products').attr('data-selected-brands', selectedBrands.join(','));
    });
  }

  /**
   * Initialize price range slider
   */
  function initPriceRangeSlider() {
    // Get min and max prices from products
    let minPrice = 0;
    let maxPrice = 2000; // Default max price
    
    // Try to get actual min and max prices from products
    const prices = [];
    $('.product-card').each(function() {
      const price = parseFloat($(this).data('price'));
      if (!isNaN(price) && price > 0) {
        prices.push(price);
      }
    });
    
    if (prices.length > 0) {
      minPrice = Math.floor(Math.min(...prices));
      maxPrice = Math.ceil(Math.max(...prices));
    }
    
    // Set initial values in inputs
    $('#min-price').val(minPrice);
    $('#max-price').val(maxPrice);
    
    // Initialize jQuery UI slider if available
    if ($.fn.slider) {
      $('#price-range-slider').slider({
        range: true,
        min: minPrice,
        max: maxPrice,
        values: [minPrice, maxPrice],
        slide: function(event, ui) {
          $('#min-price').val(ui.values[0]);
          $('#max-price').val(ui.values[1]);
        }
      });
      
      // Update slider when inputs change
      $('#min-price, #max-price').on('change', function() {
        const minVal = parseInt($('#min-price').val()) || minPrice;
        const maxVal = parseInt($('#max-price').val()) || maxPrice;
        
        $('#price-range-slider').slider('values', [minVal, maxVal]);
      });
    } else {
      // Fallback for when jQuery UI is not available
      $('.price-slider').hide();
      $('.price-range-inputs').css('margin-bottom', '0');
    }
  }

  /**
   * Initialize filter actions
   */
  function initFilterActions() {
    // Apply filters button
    $('.filter-apply-btn').on('click', function() {
      applyFilters();
    });
    
    // Star rating filter
    $('.star-rating-item input').on('change', function() {
      // Allow time for checkbox state to update
      setTimeout(function() {
        applyFilters();
      }, 100);
    });
    
    // Demand filter
    $('.demand-option input').on('change', function() {
      // Allow time for radio button state to update
      setTimeout(function() {
        applyFilters();
      }, 100);
    });
  }

  /**
   * Apply all filters to products
   */
  function applyFilters() {
    // Get filter values
    const selectedBrands = ($('#shop-products').attr('data-selected-brands') || '').split(',').filter(Boolean);
    const minPrice = parseFloat($('#min-price').val()) || 0;
    const maxPrice = parseFloat($('#max-price').val()) || 9999999;
    const selectedRatings = [];
    
    $('.star-rating-item input:checked').each(function() {
      selectedRatings.push(parseInt($(this).val()));
    });
    
    const demandFilter = $('input[name="demand"]:checked').val();
    
    // Filter products
    $('.product-card').each(function() {
      const $product = $(this);
      let showProduct = true;
      
      // Filter by brand
      if (selectedBrands.length > 0) {
        const productBrands = ($product.data('brands') || '').split(',');
        let brandMatch = false;
        
        for (const brand of productBrands) {
          const brandSlug = brand.toLowerCase().replace(/[^a-z0-9]/g, '-');
          if (selectedBrands.includes(brandSlug)) {
            brandMatch = true;
            break;
          }
        }
        
        if (!brandMatch) {
          showProduct = false;
        }
      }
      
      // Filter by price
      const productPrice = parseFloat($product.data('price')) || 0;
      if (productPrice < minPrice || productPrice > maxPrice) {
        showProduct = false;
      }
      
      // Filter by rating
      if (selectedRatings.length > 0) {
        const productRating = parseInt($product.data('rating')) || 0;
        if (!selectedRatings.includes(productRating)) {
          showProduct = false;
        }
      }
      
      // Apply demand filter (this would need actual data, using random for demo)
      if (demandFilter) {
        // This is just a placeholder - in a real implementation, you'd use actual demand data
        const randomDemand = Math.random();
        if (demandFilter === 'highest' && randomDemand < 0.5) {
          showProduct = false;
        } else if (demandFilter === 'lowest' && randomDemand > 0.5) {
          showProduct = false;
        }
      }
      
      // Show or hide product
      if (showProduct) {
        $product.show();
      } else {
        $product.hide();
      }
    });
    
    // Update product count
    updateProductCount();
  }

  /**
   * Update the product count display
   */
  function updateProductCount() {
    const visibleProducts = $('.product-card:visible').length;
    const totalProducts = $('.product-card').length;
    
    $('#product-count').text(`Showing ${visibleProducts} of ${totalProducts} products`);
  }

})(jQuery);
