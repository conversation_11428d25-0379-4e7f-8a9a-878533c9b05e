<?php
/**
 * The header for our theme - Exact match to design
 *
 * @package tendeal
 */
?>
<!doctype html>
<html <?php language_attributes(); ?>>

<head>
  <meta charset="<?php bloginfo('charset'); ?>">
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes, viewport-fit=cover">
  <meta name="format-detection" content="telephone=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <link rel="profile" href="https://gmpg.org/xfn/11">
  <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
  <?php wp_body_open(); ?>
  <div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e('Skip to content', 'tendeal'); ?></a>

    <!-- App Download Banner -->
    <div class="app-download-banner">
      <div class="container">
        <div class="app-banner-content">
          <div class="app-banner-image">
            <img src="<?php echo get_template_directory_uri(); ?>/img/app-banner.png" alt="Download our app">
          </div>
          <div class="app-banner-text">
            <p>Don't miss the opportunity</p>
            <h3>Download the App now!</h3>
          </div>
          <div class="app-banner-button">
            <a href="#" class="btn btn-app-download">Download the App</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Info Bar -->
    <div class="info-bar">
      <div class="container">
        <div class="row info-bar__column">
          <div class="col-md-8 align-content-center">
            <ul class="info-bar__list left">
              <!-- <li>
                <select class="form-select" aria-label="Language selector">
                  <option selected>English</option>
                  <option value="1">Arabic</option>
                </select>
              </li> -->
              <li>
                <?php echo do_shortcode('[language-switcher]'); ?>
              </li>
              <li>
                <a href="/faq" class="icon-with-text">
                  <!-- <i data-feather="help-circle" class="feather-sm"></i> -->
                  <span>FAQs</span>
                </a>
              </li>
              <li>
                <a href="/contact-us" class="icon-with-text">
                  <!-- <i data-feather="mail" class="feather-sm"></i> -->
                  <span>Contact us</span>
                </a>
              </li>
              <li>
                <a href="/be-seller" class="icon-with-text">
                  <!-- <i data-feather="shopping-bag" class="feather-sm"></i> -->
                  <span>Be Seller</span>
                </a>
              </li>
              <?php if (function_exists('pll_the_languages')): ?>
              <li class="language-switcher">
                <?php pll_the_languages(array('dropdown' => 1, 'hide_current' => 0)); ?>
              </li>
              <?php endif; ?>
            </ul>
          </div>
          <div class="col-md-4 text-md-end">
            <ul class="info-bar__list">
              <li class="icon-with-text">
                <i data-feather="phone" class="feather-sm"></i>
                <span>Help Line:</span>
              </li>
              <li>
                <a href="tel:0097450853357">
                  <span>0097450853357</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Header -->
    <header id="masthead" class="site-header">
      <div class="container">
        <div class="row align-items-center" style="height: 100px;">
          <!-- Logo -->
          <div class="col-4 col-md-2 logo-col" >
            <div class="site-logo pt-2 pb-2">
              <?php the_custom_logo(); ?>
            </div>
          </div>

          <!-- Search -->
          <div class="col-8 col-md-4 search-col">
            <div class="search-wrapper">
              <?php
              // Try AWS search form first, fallback to WooCommerce search form
              if (function_exists('aws_get_search_form')) {
                  // Use AWS search form but ensure it searches products
                  aws_get_search_form(true);
                  ?>
                  <script>
                  jQuery(document).ready(function($) {
                      // Ensure AWS search form includes product post type
                      $('.aws-search-form').on('submit', function() {
                          if (!$(this).find('input[name="post_type"]').length) {
                              $(this).append('<input type="hidden" name="post_type" value="product">');
                          }
                      });
                  });
                  </script>
                  <?php
              } elseif (function_exists('get_product_search_form')) {
                  get_product_search_form();
              } else {
                  // Fallback to custom search form
                  ?>
                  <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                      <div class="search-form-wrapper">
                          <input type="search" class="search-field" placeholder="<?php echo esc_attr_x('Search products...', 'placeholder', 'tendeal'); ?>" value="<?php echo get_search_query(); ?>" name="s" />
                          <input type="hidden" name="post_type" value="product" />
                          <button type="submit" class="search-submit">
                              <i data-feather="search"></i>
                          </button>
                      </div>
                  </form>
                  <?php
              }
              ?>
            </div>
          </div>

          <!-- Icons -->
          <div class="col-12 col-md-6">
            <div class="header-icons-wrapper d-flex justify-content-end">
              <!-- Mobile Menu Toggle -->
              <!-- <div class="d-md-none mobile-menu-toggle">
                <button id="mobile-menu-btn" class="mobile-menu-button">
                  <i data-feather="menu" class="feather-sm"></i>
                </button>
              </div> -->

              <!-- Desktop Icons -->
              <ul class="site-header__list d-inline-flex d-md-flex justify-content-md-end">
                <li>
                  <a href="#" class="notification-link icon-with-text">
                    <i data-feather="bell" class="feather-sm"></i>
                    <span>Notifications</span>
                  </a>
                </li>
                <li>
                  <a href="/compare-page" class="compare-link icon-with-text">
                    <i data-feather="repeat" class="feather-sm"></i>
                    <span>Compare</span>
                  </a>
                </li>
                <li>
                  <a href="<?php echo wc_get_cart_url(); ?>" class="cart-link icon-with-text">
                    <i data-feather="shopping-cart" class="feather-sm"></i>
                    <span>Cart</span>
                    <?php if (WC()->cart && WC()->cart->get_cart_contents_count() > 0): ?>
                    <span class="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                    <?php endif; ?>
                  </a>
                </li>
                <li>
                  <a href="<?php echo function_exists('YITH_WCWL') ? esc_url(YITH_WCWL()->get_wishlist_url()) : '/wishlist'; ?>"
                    class="wishlist-link icon-with-text">
                    <i data-feather="heart" class="feather-sm"></i>
                    <span>Favorites</span>
                  </a>
                </li>
                <?php if (is_user_logged_in()): ?>
                <li class="account-menu-item">
                  <a href="<?php echo esc_url(wc_get_account_endpoint_url('dashboard')); ?>" class="icon-with-text">
                    <i data-feather="user" class="feather-sm"></i>
                    <span>My Account</span>
                  </a>
                  <div class="account-dropdown">
                    <ul>
                      <li><a href="<?php echo esc_url(wc_get_account_endpoint_url('dashboard')); ?>"
                          class="icon-with-text">
                          <i data-feather="grid" class="feather-sm"></i> Dashboard</a></li>
                      <li><a href="<?php echo esc_url(wc_get_account_endpoint_url('orders')); ?>"
                          class="icon-with-text">
                          <i data-feather="package" class="feather-sm"></i> Orders</a></li>
                      <li><a href="<?php echo esc_url(wc_get_account_endpoint_url('edit-address')); ?>"
                          class="icon-with-text">
                          <i data-feather="map-pin" class="feather-sm"></i> Addresses</a></li>
                      <li><a href="<?php echo esc_url(wc_get_account_endpoint_url('edit-account')); ?>"
                          class="icon-with-text">
                          <i data-feather="settings" class="feather-sm"></i> Account details</a></li>
                      <li><a href="<?php echo esc_url(wc_logout_url()); ?>" class="icon-with-text">
                          <i data-feather="log-out" class="feather-sm"></i> Logout</a></li>
                    </ul>
                  </div>
                </li>
                <?php else: ?>
                <li>
                  <a href="<?php echo esc_url(wc_get_page_permalink('myaccount')); ?>"
                    class="login-button icon-with-text">
                    <i data-feather="log-in" class="feather-sm"></i>
                    <span>Login</span>
                  </a>
                </li>
                <?php endif; ?>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="main-navigation-wrapper">
        <nav id="site-navigation" class="main-navigation">
          <div class="container">
            <div class="row align-items-center">
              <!-- Categories Button -->
              <!-- <div class="d-none d-md-flex col-md-3 col-lg-2 category-btn-col">
                <div class="category-menu-wrapper">
                  <button id="category-menu-toggle" class="category-menu-button">
                    <i data-feather="menu" class="feather-sm"></i>
                    <span>Categories</span>
                  </button>
                  <div id="category-menu-dropdown" class="category-dropdown">
                    <?php
                    // Get product categories
                    $categories = get_terms(array(
                      'taxonomy' => 'product_cat',
                      'hide_empty' => false,
                      'parent' => 0,
                      'number' => 10,
                    ));

                    if (!empty($categories) && !is_wp_error($categories)) {
                      echo '<div class="category-dropdown-content">';

                      // Split categories into columns
                      $columns = array_chunk($categories, ceil(count($categories) / 3));

                      foreach ($columns as $column) {
                        echo '<div class="category-column">';

                        foreach ($column as $category) {
                          echo '<div class="category-section">';
                          echo '<strong><a href="' . get_term_link($category) . '">' . $category->name . '</a></strong>';

                          // Get subcategories
                          $subcategories = get_terms(array(
                            'taxonomy' => 'product_cat',
                            'hide_empty' => false,
                            'parent' => $category->term_id,
                            'number' => 5,
                          ));

                          if (!empty($subcategories) && !is_wp_error($subcategories)) {
                            foreach ($subcategories as $subcategory) {
                              echo '<a href="' . get_term_link($subcategory) . '" class="category-link">' . $subcategory->name . '</a>';
                            }

                            echo '<a href="' . get_term_link($category) . '" class="category-link view-all">View All</a>';
                          }

                          echo '</div>';
                        }

                        echo '</div>';
                      }

                      echo '</div>';
                    }
                    ?>
                  </div>
                </div>
              </div> -->

              <div class="d-none d-md-flex col-md-3 col-lg-2 category-btn-col">
                <?php echo woocommerce_category_button_html(); ?>
              </div>

              <!-- Primary menu -->
              <div class="col-6 col-md-9 col-lg-10">
                <?php
                wp_nav_menu(
                  array(
                    'theme_location' => 'menu-1',
                    'menu_id'        => 'primary-menu',
                    'container_class' => ' d-md-block',
                  )
                );
                ?>

                <!-- Mobile menu toggle button -->
                <!-- <div class="d-md-none text-end">
                  <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                    <i data-feather="more-horizontal" class="feather-sm"></i>
                  </button>
                </div> -->
              </div>
            </div>
          </div>
        </nav>
      </div>
    </header><!-- #masthead -->

    <!-- Mobile Bottom Navigation -->
    <div class="mobile-bottom-nav d-md-none">
      <div class="mobile-nav-item">
        <a href="<?php echo esc_url(home_url('/')); ?>" class="icon-with-text">
          <i data-feather="home" class="feather-sm"></i>
          <span>Home</span>
        </a>
      </div>
      <div class="mobile-nav-item">
        <a href="<?php echo function_exists('YITH_WCWL') ? esc_url(YITH_WCWL()->get_wishlist_url()) : '/wishlist'; ?>"
          class="icon-with-text">
          <i data-feather="heart" class="feather-sm"></i>
          <span>Favorites</span>
        </a>
      </div>
      <div class="mobile-nav-item">
        <a href="<?php echo wc_get_cart_url(); ?>" class="icon-with-text">
          <i data-feather="shopping-cart" class="feather-sm"></i>
          <span>Cart</span>
          <?php if (WC()->cart && WC()->cart->get_cart_contents_count() > 0): ?>
          <span class="mobile-cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
          <?php endif; ?>
        </a>
      </div>
      <div class="mobile-nav-item">
        <a href="<?php echo esc_url(wc_get_page_permalink('myaccount')); ?>" class="icon-with-text">
          <i data-feather="user" class="feather-sm"></i>
          <span>Account</span>
        </a>
      </div>
    </div>