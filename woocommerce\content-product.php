<?php
/**
 * The template for displaying product content within loops
 * Updated to use modern product card template
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product.php.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.4.0
 */

defined( 'ABSPATH' ) || exit;

global $product, $post, $woocommerce_loop;

// Ensure we have a valid post object first
if ( empty( $post ) ) {
	return;
}

// Ensure we have a product object - comprehensive fix for first item being empty
if ( empty( $product ) || ! is_a( $product, 'WC_Product' ) ) {
	$product = wc_get_product( $post->ID );
}

// Final validation - ensure we have a valid product and it's visible
if ( ! $product || ! is_a( $product, 'WC_Product' ) || ! $product->is_visible() ) {
	return;
}

// Ensure the global product is properly set for the template
$GLOBALS['product'] = $product;
?>
<li <?php wc_product_class( '', $product ); ?>>
  <?php
	// Ensure product is available in the template
	$GLOBALS['product'] = $product;

	// Include the updated product card template
	include( get_template_directory() . '/woocommerce/content-product-card-updated.php' );
	?>
</li>