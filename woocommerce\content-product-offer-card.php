<?php
/**
 * The template for displaying product content within offer cards
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product-offer-card.php.
 *
 * @package tendeal
 */

defined('ABSPATH') || exit;

global $product;

// Ensure visibility
if (empty($product) || !$product->is_visible()) {
    return;
}

// Get product data
$product_id = $product->get_id();
$product_name = $product->get_name();
$regular_price = $product->get_regular_price();
$sale_price = $product->get_sale_price();
$average_rating = $product->get_average_rating();
$review_count = $product->get_review_count();
$product_link = get_permalink($product_id);
$image_id = $product->get_image_id();
$image_url = wp_get_attachment_image_url($image_id, 'medium');
$short_description = $product->get_short_description();

// Limit short description to a specific length
$short_desc_excerpt = wp_trim_words($short_description, 10, '...');

// Get sale end date if product is on sale
$sale_end_date = '';
if ($product->is_on_sale()) {
    $sale_end_date = get_post_meta($product_id, '_sale_price_dates_to', true);
}

// Calculate days, hours, minutes, seconds until sale ends
$countdown_html = '';
if ($sale_end_date) {
    $now = current_time('timestamp');
    $end_time = $sale_end_date;
    $time_remaining = $end_time - $now;

    if ($time_remaining > 0) {
        $days = floor($time_remaining / (60 * 60 * 24));
        $hours = floor(($time_remaining % (60 * 60 * 24)) / (60 * 60));
        $minutes = floor(($time_remaining % (60 * 60)) / 60);
        $seconds = $time_remaining % 60;

        $countdown_html = '<div class="offer-countdown" data-end-time="' . esc_attr($end_time) . '">
            <span class="countdown-item">' . $days . '</span> :
            <span class="countdown-item">' . $hours . '</span> :
            <span class="countdown-item">' . $minutes . '</span> :
            <span class="countdown-item">' . $seconds . '</span>
        </div>';
    }
}
?>

<div class="offer-card">
  <?php if ($countdown_html) : ?>
  <?php echo $countdown_html; ?>
  <?php else : ?>
  <?php
    // Set a default end time 24 hours from now for demo purposes
    $default_end_time = current_time('timestamp') + (24 * 60 * 60);
  ?>
  <div class="offer-countdown" data-end-time="<?php echo esc_attr($default_end_time); ?>">
    <span class="countdown-item">13</span> :
    <span class="countdown-item">17</span> :
    <span class="countdown-item">19</span> :
    <span class="countdown-item">27</span>
  </div>
  <?php endif; ?>

  <div class="offer-card-content">
    <a href="<?php echo esc_url($product_link); ?>" class="offer-image">
      <?php if ($image_url) : ?>
      <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product_name); ?>">
      <?php else : ?>
      <img src="<?php echo esc_url(wc_placeholder_img_src()); ?>" alt="<?php echo esc_attr($product_name); ?>">
      <?php endif; ?>
    </a>

    <div class="offer-details">
      <h3 class="offer-title">
        <a href="<?php echo esc_url($product_link); ?>"><?php echo esc_html($product_name); ?></a>
      </h3>

      <?php if ($short_description) : ?>
      <p class="offer-description"><?php echo esc_html($short_desc_excerpt); ?></p>
      <?php endif; ?>

      <div class="offer-rating">
        <?php if ($average_rating > 0) : ?>
        <div class="star-rating">
          <?php for ($i = 1; $i <= 5; $i++) : ?>
          <?php if ($i <= $average_rating) : ?>
          <span class="star filled"><i class="bi bi-star-fill"></i></span>
          <?php elseif ($i - 0.5 <= $average_rating) : ?>
          <span class="star half"><i class="bi bi-star-half"></i></span>
          <?php else : ?>
          <span class="star"><i class="bi bi-star"></i></span>
          <?php endif; ?>
          <?php endfor; ?>
        </div>
        <span class="review-count"><?php echo esc_html($review_count); ?> Reviews</span>
        <?php endif; ?>
      </div>

      <div class="offer-price">
        <?php if ($sale_price) : ?>
        <span class="current-price"><?php echo wc_price($sale_price); ?></span>
        <span class="regular-price"><?php echo wc_price($regular_price); ?></span>
        <?php else : ?>
        <span class="current-price"><?php echo wc_price($regular_price); ?></span>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>