name: "CodeQL"

on:
  push:
    branches:
      - main
      - v4-dev
      - "!dependabot/**"
  pull_request:
    branches:
      - main
      - v4-dev
      - "!dependabot/**"
  schedule:
    - cron: "0 2 * * 4"
  workflow_dispatch:

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      security-events: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          config-file: ./.github/codeql/codeql-config.yml
          languages: "javascript"
          queries: +security-and-quality

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:javascript"
