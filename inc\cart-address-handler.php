<?php
/**
 * Cart Address Update Handler
 * 
 * Handles AJAX requests to update customer address from cart page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Handle AJAX request to update cart address
 */
function handle_update_cart_address() {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'You must be logged in to update address.'));
        return;
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['cart_address_nonce'], 'update_cart_address')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $user_id = get_current_user_id();
    
    // Sanitize and validate input data
    $address_data = array(
        'shipping_first_name' => sanitize_text_field($_POST['shipping_first_name'] ?? ''),
        'shipping_last_name'  => sanitize_text_field($_POST['shipping_last_name'] ?? ''),
        'shipping_address_1'  => sanitize_text_field($_POST['shipping_address_1'] ?? ''),
        'shipping_address_2'  => sanitize_text_field($_POST['shipping_address_2'] ?? ''),
        'shipping_city'       => sanitize_text_field($_POST['shipping_city'] ?? ''),
        'shipping_state'      => sanitize_text_field($_POST['shipping_state'] ?? ''),
        'shipping_postcode'   => sanitize_text_field($_POST['shipping_postcode'] ?? ''),
        'shipping_country'    => sanitize_text_field($_POST['shipping_country'] ?? ''),
    );

    // Validate required fields
    $required_fields = array('shipping_first_name', 'shipping_last_name', 'shipping_address_1', 'shipping_city', 'shipping_country');
    foreach ($required_fields as $field) {
        if (empty($address_data[$field])) {
            wp_send_json_error(array('message' => 'Please fill in all required fields.'));
            return;
        }
    }

    // Validate country code
    $countries = WC()->countries->get_countries();
    if (!array_key_exists($address_data['shipping_country'], $countries)) {
        wp_send_json_error(array('message' => 'Invalid country selected.'));
        return;
    }

    try {
        // Update user meta with new address data
        foreach ($address_data as $key => $value) {
            update_user_meta($user_id, $key, $value);
        }

        // Also update billing address if shipping address is empty
        $customer = new WC_Customer($user_id);
        if (empty($customer->get_billing_address_1())) {
            $billing_data = array(
                'billing_first_name' => $address_data['shipping_first_name'],
                'billing_last_name'  => $address_data['shipping_last_name'],
                'billing_address_1'  => $address_data['shipping_address_1'],
                'billing_address_2'  => $address_data['shipping_address_2'],
                'billing_city'       => $address_data['shipping_city'],
                'billing_state'      => $address_data['shipping_state'],
                'billing_postcode'   => $address_data['shipping_postcode'],
                'billing_country'    => $address_data['shipping_country'],
            );

            foreach ($billing_data as $key => $value) {
                update_user_meta($user_id, $key, $value);
            }
        }

        // Clear any cached customer data
        wp_cache_delete($user_id, 'users');
        
        // Trigger WooCommerce customer save action
        do_action('woocommerce_customer_save_address', $user_id, 'shipping');

        wp_send_json_success(array('message' => 'Address updated successfully!'));

    } catch (Exception $e) {
        wp_send_json_error(array('message' => 'Failed to update address: ' . $e->getMessage()));
    }
}

// Hook the AJAX handlers
add_action('wp_ajax_update_cart_address', 'handle_update_cart_address');
add_action('wp_ajax_nopriv_update_cart_address', function() {
    wp_send_json_error(array('message' => 'You must be logged in to update address.'));
});
