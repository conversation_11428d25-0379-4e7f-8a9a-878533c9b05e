{{- /*
  Usage: `example args`

  `args` are all optional and can be one of the following:
    * id: the `div`'s id - default: ""
    * class: any extra class(es) to be added to the `div` - default: ""
    * lang: language used to display the code - default: "html"
    * show_markup: if the markup should be output in the HTML - default: `true`
    * show_preview: if the preview should be output in the HTML - default: `true`
    * stackblitz_add_js: if extra JS snippet should be added to StackBlitz - default: `false`
*/ -}}

{{- $id := .Get "id" -}}
{{- $class := .Get "class" -}}
{{- $lang := .Get "lang" | default "html" -}}
{{- $show_markup := .Get "show_markup" | default true -}}
{{- $show_preview := .Get "show_preview" | default true -}}
{{- $stackblitz_add_js := .Get "stackblitz_add_js" | default false -}}

{{- $content := .Inner -}}

<div class="bd-example-snippet bd-code-snippet">
  {{- if eq $show_preview true }}
  <div{{ with $id }} id="{{ . }}"{{ end }} class="bd-example m-0 border-0{{ with $class }} {{ . }}{{ end }}">
    {{ $content }}
  </div>
  {{- end }}

  {{- if eq $show_markup true -}}
    {{- if eq $show_preview true -}}
      <div class="d-flex align-items-center highlight-toolbar ps-3 pe-2 py-1 border-0 border-top border-bottom">
        <small class="font-monospace text-body-secondary text-uppercase">{{ $lang }}</small>
        <div class="d-flex ms-auto">
          <button type="button" class="btn-edit text-nowrap"{{ with $stackblitz_add_js }} data-sb-js-snippet="{{ $stackblitz_add_js }}"{{ end }} title="Try it on StackBlitz">
            <svg class="bi" aria-hidden="true"><use xlink:href="#lightning-charge-fill"/></svg>
          </button>
          <button type="button" class="btn-clipboard mt-0 me-0" title="Copy to clipboard">
            <svg class="bi" aria-hidden="true"><use xlink:href="#clipboard"/></svg>
          </button>
        </div>
      </div>
    {{- end -}}

    {{- $content = replaceRE `<svg class="bd-placeholder-img(?:-lg)?(?: *?bd-placeholder-img-lg)? ?(.*?)".*?<\/svg>` `<img src="..." class="$1" alt="...">` $content -}}
    {{- $content = replaceRE `<img class="bd-placeholder-img(?:-lg)?(?: *?bd-placeholder-img-lg)? ?(.*?)".*?>` `<img src="..." class="$1" alt="...">` $content -}}
    {{- $content = replaceRE ` (class=" *?")` "" $content -}}
    {{- highlight (trim $content "\n") $lang "" -}}
  {{- end }}
</div>
