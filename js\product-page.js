/**
 * JavaScript for Product Page
 */
jQuery(document).ready(function($) {
    // Quantity buttons functionality
    $('.quantity-btn.minus').on('click', function() {
        var input = $(this).closest('.quantity-controls').find('.quantity-input');
        var currentValue = parseInt(input.val());
        if (currentValue > 1) {
            var newValue = currentValue - 1;
            input.val(newValue);
            // Update hidden quantity inputs for form submission
            $('.product-quantity-input').val(newValue);
            $('.direct-quantity-input').val(newValue);
        }
    });

    $('.quantity-btn.plus').on('click', function() {
        var input = $(this).closest('.quantity-controls').find('.quantity-input');
        var currentValue = parseInt(input.val());
        var max = parseInt(input.attr('max'));
        if (!max || currentValue < max) {
            var newValue = currentValue + 1;
            input.val(newValue);
            // Update hidden quantity inputs for form submission
            $('.product-quantity-input').val(newValue);
            $('.direct-quantity-input').val(newValue);
        }
    });

    // Initialize quantity value
    $('.product-quantity-input').val($('#product-quantity').val());
    $('.direct-quantity-input').val($('#product-quantity').val());

    // Handle manual quantity input changes
    $('#product-quantity').on('change', function() {
        var newValue = parseInt($(this).val());
        if (isNaN(newValue) || newValue < 1) {
            newValue = 1;
            $(this).val(newValue);
        }
        // Update hidden quantity inputs
        $('.product-quantity-input').val(newValue);
        $('.direct-quantity-input').val(newValue);
    });

    // Add to cart button functionality
    $('.add-to-cart-btn').on('click', function() {
        var productId = $('#product-id').val();
        var quantity = $('#product-quantity').val();

        if (!productId) {
            console.error('Product ID not found');
            alert('Error: Product ID not found');
            return;
        }

        // Update quantity in all forms
        $('.product-quantity-input').val(quantity);
        $('.direct-quantity-input').val(quantity);

        // Add loading state
        $(this).addClass('loading');

        console.log('Adding to cart:', productId, quantity);

        // Get variation ID if this is a variable product
        var variationId = $('.variation_id').val() || 0;

        // Check if this is a variable product that requires variation selection
        if ($('.variations_form').length && variationId == 0) {
            alert('Please select product options before adding to cart');
            $('.add-to-cart-btn').removeClass('loading');
            return;
        }

        // AJAX add to cart
        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'woocommerce_ajax_add_to_cart',
                product_id: productId,
                quantity: quantity,
                variation_id: variationId,
                security: wc_add_to_cart_params.nonce
            },
            success: function(response) {
                console.log('Success response:', response);
                if (response.success) {
                    // Show success message
                    alert('Product added to cart');

                    // Update cart fragments
                    $(document.body).trigger('wc_fragment_refresh');
                } else {
                    console.error('Error in response:', response);
                    // Try direct form submission
                    $('#direct-add-to-cart-form').submit();
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', xhr, status, error);
                // Try direct form submission
                $('#direct-add-to-cart-form').submit();
            },
            complete: function() {
                // Remove loading state
                $('.add-to-cart-btn').removeClass('loading');
            }
        });
    });

    // Buy now button functionality
    $('.buy-now-btn').on('click', function() {
        var productId = $('#product-id').val();
        var quantity = $('#product-quantity').val();

        if (!productId) {
            console.error('Product ID not found');
            alert('Error: Product ID not found');
            return;
        }

        // Update quantity in all forms
        $('.product-quantity-input').val(quantity);
        $('.direct-quantity-input').val(quantity);

        // Add loading state
        $(this).addClass('loading');

        console.log('Buy now:', productId, quantity);

        // Get variation ID if this is a variable product
        var variationId = $('.variation_id').val() || 0;

        // Check if this is a variable product that requires variation selection
        if ($('.variations_form').length && variationId == 0) {
            alert('Please select product options before buying');
            $('.buy-now-btn').removeClass('loading');
            return;
        }

        // AJAX add to cart and redirect to checkout
        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'woocommerce_ajax_add_to_cart',
                product_id: productId,
                quantity: quantity,
                variation_id: variationId,
                security: wc_add_to_cart_params.nonce
            },
            success: function(response) {
                console.log('Success response:', response);
                if (response.success) {
                    // Redirect to checkout
                    window.location.href = wc_add_to_cart_params.checkout_url;
                } else {
                    console.error('Error in response:', response);
                    // Try direct form submission and then redirect
                    $('#direct-add-to-cart-form').submit();
                    setTimeout(function() {
                        window.location.href = wc_add_to_cart_params.checkout_url;
                    }, 1000);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', xhr, status, error);
                // Try direct form submission and then redirect
                $('#direct-add-to-cart-form').submit();
                setTimeout(function() {
                    window.location.href = wc_add_to_cart_params.checkout_url;
                }, 1000);
            },
            complete: function() {
                // Remove loading state
                $('.buy-now-btn').removeClass('loading');
            }
        });
    });

    // Add to RFQ button functionality
    $('.add-to-rfq-btn').on('click', function() {
        var productId = $('#product-id').val();
        var quantity = $('#product-quantity').val();

        if (!productId) {
            console.error('Product ID not found');
            alert('Error: Product ID not found');
            return;
        }

        // Update quantity in all forms
        $('.product-quantity-input').val(quantity);
        $('.direct-quantity-input').val(quantity);

        // Add loading state
        $(this).addClass('loading');

        console.log('Add to RFQ:', productId, quantity);

        // Get variation ID if this is a variable product
        var variationId = $('.variation_id').val() || 0;

        // Check if this is a variable product that requires variation selection
        if ($('.variations_form').length && variationId == 0) {
            alert('Please select product options before adding to RFQ');
            $('.add-to-rfq-btn').removeClass('loading');
            return;
        }

        // AJAX add to RFQ
        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'add_to_rfq',
                product_id: productId,
                quantity: quantity,
                variation_id: variationId
            },
            success: function(response) {
                console.log('Success response:', response);
                if (response.success) {
                    // Show success message
                    alert('Product added to RFQ');
                } else {
                    console.error('Error in response:', response);
                    alert('Error adding product to RFQ. Please try again.');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', xhr, status, error);
                alert('Error occurred. Please try again.');
            },
            complete: function() {
                // Remove loading state
                $('.add-to-rfq-btn').removeClass('loading');
            }
        });
    });

    // Share button functionality
    $('.share-button').on('click', function() {
        var productUrl = $(this).data('product-url');
        var productTitle = $(this).data('product-title');
        var productImage = $(this).data('product-image');

        // Check if Web Share API is supported (mainly mobile devices)
        if (navigator.share) {
            navigator.share({
                title: productTitle,
                text: 'Check out this product: ' + productTitle,
                url: productUrl
            }).then(() => {
                console.log('Product shared successfully');
            }).catch((error) => {
                console.log('Error sharing:', error);
                // Fallback to modal if native sharing fails
                showShareModal();
            });
        } else {
            // Show custom share modal for desktop
            showShareModal();
        }
    });

    // Function to show share modal
    function showShareModal() {
        $('#share-modal').css('display', 'flex').hide().fadeIn(300);
        $('body').addClass('modal-open');
    }

    // Close share modal
    $('.share-modal-close, .share-modal').on('click', function(e) {
        if (e.target === this) {
            $('#share-modal').fadeOut(300);
            $('body').removeClass('modal-open');
        }
    });

    // Prevent modal from closing when clicking inside modal content
    $('.share-modal-content').on('click', function(e) {
        e.stopPropagation();
    });

    // Handle share option clicks
    $('.share-option').on('click', function(e) {
        e.preventDefault();

        var platform = $(this).data('platform');
        var shareButton = $('.share-button');
        var productUrl = shareButton.data('product-url');
        var productTitle = shareButton.data('product-title');
        var productImage = shareButton.data('product-image');

        var shareUrl = '';
        var shareText = 'Check out this product: ' + productTitle;

        switch(platform) {
            case 'facebook':
                shareUrl = 'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(productUrl);
                break;
            case 'twitter':
                shareUrl = 'https://twitter.com/intent/tweet?text=' + encodeURIComponent(shareText) + '&url=' + encodeURIComponent(productUrl);
                break;
            case 'whatsapp':
                shareUrl = 'https://wa.me/?text=' + encodeURIComponent(shareText + ' ' + productUrl);
                break;
            case 'email':
                shareUrl = 'mailto:?subject=' + encodeURIComponent(productTitle) + '&body=' + encodeURIComponent(shareText + '\n\n' + productUrl);
                break;
            case 'copy':
                // Copy link to clipboard
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(productUrl).then(function() {
                        // Show success message
                        var originalText = $('.share-option.copy-link span').text();
                        $('.share-option.copy-link span').text('Copied!');
                        setTimeout(function() {
                            $('.share-option.copy-link span').text(originalText);
                        }, 2000);
                    }).catch(function(err) {
                        console.error('Failed to copy: ', err);
                        // Fallback for older browsers
                        fallbackCopyTextToClipboard(productUrl);
                    });
                } else {
                    // Fallback for older browsers
                    fallbackCopyTextToClipboard(productUrl);
                }
                return; // Don't open a new window for copy
        }

        if (shareUrl) {
            // Open share URL in new window
            window.open(shareUrl, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
        }

        // Close modal after sharing
        $('#share-modal').fadeOut(300);
        $('body').removeClass('modal-open');
    });

    // Fallback function for copying to clipboard in older browsers
    function fallbackCopyTextToClipboard(text) {
        var textArea = document.createElement("textarea");
        textArea.value = text;

        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            var successful = document.execCommand('copy');
            if (successful) {
                var originalText = $('.share-option.copy-link span').text();
                $('.share-option.copy-link span').text('Copied!');
                setTimeout(function() {
                    $('.share-option.copy-link span').text(originalText);
                }, 2000);
            }
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
        }

        document.body.removeChild(textArea);
    }
});
