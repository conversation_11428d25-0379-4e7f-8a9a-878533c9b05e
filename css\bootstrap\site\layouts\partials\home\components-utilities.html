<section class="pb-md-5 mb-5">
  <div class="col-lg-8 mb-5">
    <div class="masthead-followup-icon d-inline-block mb-3 me-2" style="--bg-rgb: var(--bs-danger-rgb);">
      <svg class="bi fs-1"><use xlink:href="#menu-button-wide-fill"></use></svg>
    </div>
    <svg class="bi me-2 fs-2 text-body-secondary"><use xlink:href="#plus"></use></svg>
    <div class="masthead-followup-icon d-inline-block mb-3" style="--bg-rgb: var(--bs-info-rgb);">
      <svg class="bi fs-1"><use xlink:href="#braces-asterisk"></use></svg>
    </div>
    <h2 class="display-5 mb-3 fw-semibold lh-sm">Components, meet the Utility&nbsp;API</h2>
    <p class="lead fw-normal">
      New in Bootstrap 5, our utilities are now generated by our <a href="/docs/{{ .Site.Params.docs_version }}/utilities/api/">Utility API</a>. We built it as a feature-packed Sass map that can be quickly and easily customized. It's never been easier to add, remove, or modify any utility classes. Make utilities responsive, add pseudo-class variants, and give them custom names.
    </p>
  </div>
  <div class="row gx-md-5">
    <div class="col-lg-6">
      <h3 class="fw-semibold">Quickly customize components</h3>
      <p>Apply any of our included utility classes to our components to customize their appearance, like the navigation example below. There are hundreds of classes available—from <a href="/docs/{{ .Site.Params.docs_version }}/utilities/position/">positioning</a> and <a href="/docs/{{ .Site.Params.docs_version }}/utilities/sizing/">sizing</a> to <a href="/docs/{{ .Site.Params.docs_version }}/utilities/colors/">colors</a> and <a href="/docs/{{ .Site.Params.docs_version }}/utilities/shadows/">effects</a>. Mix them with CSS variable overrides for even more control.</p>
      <div class="p-4 border rounded-3 mb-4">
        <ul class="nav nav-pills mb-4" id="pillNav" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="home-tab" data-bs-toggle="tab" type="button" role="tab" aria-selected="true">Home</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="profile-tab" data-bs-toggle="tab" type="button" role="tab" aria-selected="false">Profile</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" type="button" role="tab" aria-selected="false">Contact</button>
          </li>
        </ul>
        <ul class="nav nav-pills nav-fill gap-2 p-1 small bg-primary rounded-5 shadow-sm" id="pillNav2" role="tablist" style="--bs-nav-link-color: rgba(255, 255, 255, .75); --bs-nav-link-hover-color: #fff; --bs-nav-pills-link-active-color: var(--bs-primary); --bs-nav-pills-link-active-bg: var(--bs-white);">
          <li class="nav-item" role="presentation">
            <button class="nav-link active rounded-5" id="home-tab2" data-bs-toggle="tab" type="button" role="tab" aria-selected="true">Home</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link rounded-5" id="profile-tab2" data-bs-toggle="tab" type="button" role="tab" aria-selected="false">Profile</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link rounded-5" id="contact-tab2" data-bs-toggle="tab" type="button" role="tab" aria-selected="false">Contact</button>
          </li>
        </ul>
      </div>
{{ highlight (printf `<ul class="nav nav-pills nav-fill gap-2 p-1 small bg-primary rounded-5 shadow-sm" id="pillNav2" role="tablist" style="--bs-nav-link-color: var(--bs-white); --bs-nav-pills-link-active-color: var(--bs-primary); --bs-nav-pills-link-active-bg: var(--bs-white);">
  <li class="nav-item" role="presentation">
    <button class="nav-link active rounded-5" id="home-tab2" data-bs-toggle="tab" type="button" role="tab" aria-selected="true">Home</button>
  </li>
  <li class="nav-item" role="presentation">
    <button class="nav-link rounded-5" id="profile-tab2" data-bs-toggle="tab" type="button" role="tab" aria-selected="false">Profile</button>
  </li>
  <li class="nav-item" role="presentation">
    <button class="nav-link rounded-5" id="contact-tab2" data-bs-toggle="tab" type="button" role="tab" aria-selected="false">Contact</button>
  </li>
</ul>
`) "html" "" }}
      <p class="d-flex justify-content-start mb-md-0">
        <a href="/docs/{{ .Site.Params.docs_version }}/examples#snippets" class="icon-link icon-link-hover fw-semibold">
          Explore customized components
          <svg class="bi"><use xlink:href="#arrow-right"></use></svg>
        </a>
      </p>
    </div>
    <div class="col-lg-6">
      <h3 class="fw-semibold">Create and extend utilities</h3>
      <p>Use Bootstrap's utility API to modify any of our included utilities or create your own custom utilities for any project. Import Bootstrap first, then use Sass map functions to modify, add, or remove utilities.</p>
{{ highlight (printf `@import "bootstrap/scss/bootstrap";

$utilities: map-merge(
  $utilities,
  (
    "cursor": (
      property: cursor,
      class: cursor,
      responsive: true,
      values: auto pointer grab,
    )
  )
);
`) "scss" "" }}

      <p class="d-flex justify-content-start mb-md-0">
        <a href="/docs/{{ .Site.Params.docs_version }}/utilities/api/" class="icon-link icon-link-hover fw-semibold mb-3">
          Explore the utility API
          <svg class="bi"><use xlink:href="#arrow-right"></use></svg>
        </a>
      </p>
    </div>
  </div>
</section>
