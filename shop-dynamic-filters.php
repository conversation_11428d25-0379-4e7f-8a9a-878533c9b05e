<?php
/**
 * Template Name: Shop with Dynamic Filters
 *
 * A shop page template with dynamic sidebar filters based on product attributes.
 *
 * @package Teandeal
 */

// This file can be used both as a standalone page template and included in archive-product.php
$is_direct_template = !defined('ABSPATH') || (defined('ABSPATH') && !did_action('wp'));

if ($is_direct_template) {
    get_header();
}

// Remove WooCommerce default pagination to prevent duplicates
remove_action('woocommerce_after_shop_loop', 'woocommerce_pagination', 10);

// Always enqueue the required styles and scripts
wp_enqueue_style('tendeal-shop-sidebar-updated', get_stylesheet_directory_uri() . '/css/shop-sidebar-updated.css', array(), _S_VERSION);
wp_enqueue_style('woocommerce-compatibility', get_stylesheet_directory_uri() . '/css/woocommerce-compatibility.css', array(), _S_VERSION);

// Add this CSS file with a higher priority to override any other styles
wp_enqueue_style('product-grid-override', get_stylesheet_directory_uri() . '/css/product-grid-override.css', array('woocommerce-general'), _S_VERSION, 'all');

// Add inline CSS to ensure our styles take precedence
add_action('wp_head', function() {
  echo '<style>
    /* Force important styles for product grid */
    .woocommerce ul.products {
      display: grid !important;
      grid-template-columns: repeat(3, 1fr) !important;
    }
    .woocommerce ul.products li.product img {
      max-height: 200px !important;
      width: auto !important;
      margin: 0 auto !important;
    }
  </style>';
}, 100);

// Add body classes for category and brand pages
add_filter('body_class', function($classes) {
  if (is_product_category()) {
    $classes[] = 'product-category';
  }
  if (is_tax('product_brand')) {
    $classes[] = 'product-brand';
  }
  return $classes;
});
wp_enqueue_script('jquery-ui-slider');
wp_enqueue_script('tendeal-shop-dynamic-filters', get_template_directory_uri() . '/js/shop-dynamic-filters.js', array('jquery', 'jquery-ui-slider'), _S_VERSION, true);
wp_enqueue_script('tendeal-dynamic-shop-filters', get_template_directory_uri() . '/js/dynamic-shop-filters.js', array('jquery'), _S_VERSION, true);

// Add WooCommerce shop URL to JavaScript
wp_add_inline_script('tendeal-shop-dynamic-filters', 'var woocommerceShopUrl = "' . esc_url(wc_get_page_permalink('shop')) . '";', 'before');

/**
 * Performance-optimized filter data generation with caching
 */
function tendeal_get_shop_filter_data($context = 'shop', $context_id = null) {
  // Create cache key based on context
  $cache_key = 'tendeal_shop_filters_' . $context;
  if ($context_id) {
    $cache_key .= '_' . $context_id;
  }

  // Try to get cached data first
  $cached_data = get_transient($cache_key);
  if ($cached_data !== false && !isset($_GET['refresh_cache'])) {
    return $cached_data;
  }

  // Start performance timing
  $start_time = microtime(true);

  // Initialize arrays to store filter data
  $brands = array();
  $categories = array();
  $attributes = array();
  $price_range = array('min' => 0, 'max' => 1000);
  $rating_counts = array(5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0);

  // Initialize arrays to store relationships
  $category_brands = array();
  $category_attributes = array();
  $brand_categories = array();
  $brand_attributes = array();

  // Build optimized query based on context
  $query_args = array(
    'post_type' => 'product',
    'post_status' => 'publish',
    'fields' => 'ids',
    'posts_per_page' => -1, // We need all products for filter data, but we'll optimize this
    'no_found_rows' => true, // Skip pagination calculations
    'update_post_meta_cache' => false, // Skip meta cache for IDs-only query
    'update_post_term_cache' => false, // Skip term cache for IDs-only query
    'meta_query' => array(
      array(
        'key' => '_stock_status',
        'value' => array('instock', 'onbackorder'),
        'compare' => 'IN'
      )
    ),
    'tax_query' => array(
      array(
        'taxonomy' => 'product_visibility',
        'field' => 'name',
        'terms' => array('exclude-from-catalog', 'exclude-from-search'),
        'operator' => 'NOT IN'
      )
    )
  );

  // Add context-specific filters
  if ($context === 'category' && $context_id) {
    $query_args['tax_query'][] = array(
      'taxonomy' => 'product_cat',
      'field' => 'term_id',
      'terms' => $context_id
    );
  } elseif ($context === 'brand' && $context_id) {
    $query_args['tax_query'][] = array(
      'taxonomy' => 'product_brand',
      'field' => 'term_id',
      'terms' => $context_id
    );
  }

  // Get product IDs
  $products_query = new WP_Query($query_args);
  $product_ids = $products_query->posts;

  // Process products in batches to avoid memory issues
  $batch_size = 100;
  $total_products = count($product_ids);
  $batches = array_chunk($product_ids, $batch_size);

  foreach ($batches as $batch_index => $batch_ids) {
    // Get products with minimal data
    foreach ($batch_ids as $product_id) {
      // Use WooCommerce's optimized product loading
      $product = wc_get_product($product_id);
      if (!$product || !$product->is_visible()) continue;

      // Get price for price range (optimized)
      $price = (float) $product->get_price();
      if ($price > 0) {
        if (!isset($price_range['min']) || $price < $price_range['min']) {
          $price_range['min'] = floor($price);
        }
        if (!isset($price_range['max']) || $price > $price_range['max']) {
          $price_range['max'] = ceil($price);
        }
      }

      // Get product categories (cached by WordPress)
      $product_categories = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'all'));
      $product_category_ids = array();

      if (!empty($product_categories) && !is_wp_error($product_categories)) {
        foreach ($product_categories as $category) {
          $product_category_ids[] = $category->term_id;

          if (!isset($categories[$category->term_id])) {
            $categories[$category->term_id] = array(
              'name' => $category->name,
              'slug' => $category->slug,
              'count' => 1,
              'parent' => $category->parent
            );
          } else {
            $categories[$category->term_id]['count']++;
          }
        }
      }

      // Get product brands (optimized)
      $product_brand_ids = array();
      if (taxonomy_exists('product_brand')) {
        $product_brands = wp_get_post_terms($product_id, 'product_brand', array('fields' => 'all'));
        if (!empty($product_brands) && !is_wp_error($product_brands)) {
          foreach ($product_brands as $brand) {
            $product_brand_ids[] = $brand->term_id;

            if (!isset($brands[$brand->term_id])) {
              // Get brand image efficiently
              $thumbnail_id = get_term_meta($brand->term_id, 'thumbnail_id', true);
              $image = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : '';

              $brands[$brand->term_id] = array(
                'name' => $brand->name,
                'slug' => $brand->slug,
                'count' => 1,
                'image' => $image
              );
            } else {
              $brands[$brand->term_id]['count']++;
            }

            // Store brand-category relationships
            if (!isset($brand_categories[$brand->term_id])) {
              $brand_categories[$brand->term_id] = array();
            }
            foreach ($product_category_ids as $cat_id) {
              if (!in_array($cat_id, $brand_categories[$brand->term_id])) {
                $brand_categories[$brand->term_id][] = $cat_id;
              }
            }
          }
        }
      }

      // Get product attributes (optimized)
      $product_attributes = $product->get_attributes();
      $product_attribute_names = array();
      $product_attribute_terms = array();

      if (!empty($product_attributes)) {
        foreach ($product_attributes as $attribute_name => $attribute) {
          if ($attribute->is_taxonomy()) {
            $taxonomy = $attribute->get_taxonomy_object();
            $attribute_label = $taxonomy ? $taxonomy->attribute_label : $attribute_name;
            $attribute_terms = $attribute->get_terms();

            $product_attribute_names[] = $attribute_name;

            if (!isset($attributes[$attribute_name])) {
              $attributes[$attribute_name] = array(
                'name' => $attribute_label,
                'terms' => array()
              );
            }

            if (!empty($attribute_terms)) {
              foreach ($attribute_terms as $term) {
                $product_attribute_terms[$attribute_name][] = $term->term_id;

                if (!isset($attributes[$attribute_name]['terms'][$term->term_id])) {
                  $attributes[$attribute_name]['terms'][$term->term_id] = array(
                    'name' => $term->name,
                    'slug' => $term->slug,
                    'count' => 1
                  );
                } else {
                  $attributes[$attribute_name]['terms'][$term->term_id]['count']++;
                }
              }
            }
          }
        }
      }

      // Store relationships efficiently
      foreach ($product_category_ids as $cat_id) {
        if (!isset($category_brands[$cat_id])) {
          $category_brands[$cat_id] = array();
        }
        foreach ($product_brand_ids as $brand_id) {
          if (!in_array($brand_id, $category_brands[$cat_id])) {
            $category_brands[$cat_id][] = $brand_id;
          }
        }

        if (!isset($category_attributes[$cat_id])) {
          $category_attributes[$cat_id] = array();
        }
        foreach ($product_attribute_names as $attr_name) {
          if (!isset($category_attributes[$cat_id][$attr_name])) {
            $category_attributes[$cat_id][$attr_name] = array();
          }
          if (isset($product_attribute_terms[$attr_name])) {
            foreach ($product_attribute_terms[$attr_name] as $term_id) {
              if (!in_array($term_id, $category_attributes[$cat_id][$attr_name])) {
                $category_attributes[$cat_id][$attr_name][] = $term_id;
              }
            }
          }
        }
      }

      foreach ($product_brand_ids as $brand_id) {
        if (!isset($brand_attributes[$brand_id])) {
          $brand_attributes[$brand_id] = array();
        }
        foreach ($product_attribute_names as $attr_name) {
          if (!isset($brand_attributes[$brand_id][$attr_name])) {
            $brand_attributes[$brand_id][$attr_name] = array();
          }
          if (isset($product_attribute_terms[$attr_name])) {
            foreach ($product_attribute_terms[$attr_name] as $term_id) {
              if (!in_array($term_id, $brand_attributes[$brand_id][$attr_name])) {
                $brand_attributes[$brand_id][$attr_name][] = $term_id;
              }
            }
          }
        }
      }

      // Get product rating
      $rating = (int) $product->get_average_rating();
      if ($rating > 0) {
        $rating = min(5, max(1, floor($rating)));
        $rating_counts[$rating]++;
      }
    }

    // Clear memory after each batch
    if ($batch_index % 5 === 0) {
      wp_cache_flush_group('wc_product_meta');
    }
  }

  // Calculate processing time
  $processing_time = microtime(true) - $start_time;

  // Prepare final data structure
  $filter_data = array(
    'brands' => $brands,
    'categories' => $categories,
    'attributes' => $attributes,
    'price_range' => $price_range,
    'rating_counts' => $rating_counts,
    'category_brands' => $category_brands,
    'category_attributes' => $category_attributes,
    'brand_categories' => $brand_categories,
    'brand_attributes' => $brand_attributes,
    'processing_time' => $processing_time,
    'total_products' => $total_products,
    'generated_at' => current_time('mysql')
  );

  // Cache the data for 1 hour
  set_transient($cache_key, $filter_data, HOUR_IN_SECONDS);

  // Add performance comment for debugging
  if (current_user_can('manage_options')) {
    echo "<!-- Filter data generated in " . round($processing_time, 3) . " seconds for $total_products products -->";
  }

  return $filter_data;
}

// Get filter data using the optimized function
$filter_data = tendeal_get_shop_filter_data();
$brands = $filter_data['brands'];
$categories = $filter_data['categories'];
$attributes = $filter_data['attributes'];
$price_range = $filter_data['price_range'];
$rating_counts = $filter_data['rating_counts'];
$category_brands = $filter_data['category_brands'];
$category_attributes = $filter_data['category_attributes'];
$brand_categories = $filter_data['brand_categories'];
$brand_attributes = $filter_data['brand_attributes'];

// Sort brands by count
uasort($brands, function($a, $b) {
  return $b['count'] - $a['count'];
});

// Sort categories by count
uasort($categories, function($a, $b) {
  return $b['count'] - $a['count'];
});

// Organize categories into parent-child relationships
$top_categories = array();
$child_categories = array();

foreach ($categories as $id => $category) {
  if ($category['parent'] == 0) {
    $top_categories[$id] = $category;
  } else {
    $child_categories[$category['parent']][$id] = $category;
  }
}

// Get current filter values from URL
$current_filters = array(
  'category' => isset($_GET['category']) ? sanitize_text_field($_GET['category']) : '',
  'brand' => isset($_GET['brand']) ? sanitize_text_field($_GET['brand']) : '',
  'min_price' => isset($_GET['min_price']) ? (float) $_GET['min_price'] : $price_range['min'],
  'max_price' => isset($_GET['max_price']) ? (float) $_GET['max_price'] : $price_range['max'],
  'rating' => isset($_GET['rating']) ? (int) $_GET['rating'] : 0,
  'attributes' => array()
);

// Get attribute filters from URL
foreach ($attributes as $attribute_name => $attribute) {
  $param_name = 'filter_' . str_replace('pa_', '', $attribute_name);
  if (isset($_GET[$param_name])) {
    $current_filters['attributes'][$attribute_name] = sanitize_text_field($_GET[$param_name]);
  }
}

// Set default products per page to 12
$default_posts_per_page = 12;

// Build query args based on current filters
$query_args = array(
  'post_type' => 'product',
  'posts_per_page' => $default_posts_per_page,
  'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
  'post_status' => 'publish',
  'tax_query' => array(
    // Use modern WooCommerce product visibility taxonomy
    array(
      'taxonomy' => 'product_visibility',
      'field' => 'name',
      'terms' => array('exclude-from-catalog', 'exclude-from-search'),
      'operator' => 'NOT IN'
    )
  ),
  'meta_query' => array(
    // Ensure only in-stock products or those that allow backorders
    array(
      'key' => '_stock_status',
      'value' => array('instock', 'onbackorder'),
      'compare' => 'IN'
    )
  )
);

// Handle search query
if (isset($_GET['s']) && !empty($_GET['s'])) {
  $query_args['s'] = sanitize_text_field($_GET['s']);
}

// Handle sorting
if (isset($_GET['orderby']) && !empty($_GET['orderby'])) {
  $orderby = sanitize_text_field($_GET['orderby']);
  switch ($orderby) {
    case 'popularity':
      $query_args['meta_key'] = 'total_sales';
      $query_args['orderby'] = 'meta_value_num';
      $query_args['order'] = 'DESC';
      break;
    case 'rating':
      $query_args['meta_key'] = '_wc_average_rating';
      $query_args['orderby'] = 'meta_value_num';
      $query_args['order'] = 'DESC';
      break;
    case 'date':
      $query_args['orderby'] = 'date';
      $query_args['order'] = 'DESC';
      break;
    case 'price':
      $query_args['meta_key'] = '_price';
      $query_args['orderby'] = 'meta_value_num';
      $query_args['order'] = 'ASC';
      break;
    case 'price-desc':
      $query_args['meta_key'] = '_price';
      $query_args['orderby'] = 'meta_value_num';
      $query_args['order'] = 'DESC';
      break;
    default:
      $query_args['orderby'] = 'menu_order';
      $query_args['order'] = 'ASC';
      break;
  }
}

// Handle products per page - default to 12
$default_per_page = 12;
$products_per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : $default_per_page;
$products_per_page = max(6, min(48, $products_per_page)); // Limit between 6-48
$query_args['posts_per_page'] = $products_per_page;

// Check if we're on a category page
if (is_product_category()) {
  $current_category = get_queried_object();
  $query_args['tax_query'][] = array(
    'taxonomy' => 'product_cat',
    'field' => 'term_id',
    'terms' => $current_category->term_id
  );
  // Set the current filter for display purposes
  $current_filters['category'] = $current_category->slug;

  // Get optimized filter data for this category
  $current_category_id = $current_category->term_id;
  $category_filter_data = tendeal_get_shop_filter_data('category', $current_category_id);

  // Use the category-specific filter data
  $brands = $category_filter_data['brands'];
  $attributes = $category_filter_data['attributes'];
  $price_range = $category_filter_data['price_range'];
  $category_brands = $category_filter_data['category_brands'];
  $category_attributes = $category_filter_data['category_attributes'];
  $brand_categories = $category_filter_data['brand_categories'];
  $brand_attributes = $category_filter_data['brand_attributes'];

  // Debug: Add HTML comment for debugging
  if (current_user_can('manage_options')) {
    echo "<!-- Category Page: " . $current_category->name . " (ID: $current_category_id) -->";
    echo "<!-- Brands: " . count($brands) . ", Attributes: " . count($attributes) . " -->";
    echo "<!-- Price Range: " . $price_range['min'] . " - " . $price_range['max'] . " -->";
  }
}
// Check if we're on a brand page
elseif (is_tax('product_brand')) {
  $current_brand = get_queried_object();
  $query_args['tax_query'][] = array(
    'taxonomy' => 'product_brand',
    'field' => 'term_id',
    'terms' => $current_brand->term_id
  );
  // Set the current filter for display purposes
  $current_filters['brand'] = $current_brand->slug;

  // Get optimized filter data for this brand
  $current_brand_id = $current_brand->term_id;
  $brand_filter_data = tendeal_get_shop_filter_data('brand', $current_brand_id);

  // Use the brand-specific filter data
  $categories = $brand_filter_data['categories'];
  $attributes = $brand_filter_data['attributes'];
  $price_range = $brand_filter_data['price_range'];
  $category_brands = $brand_filter_data['category_brands'];
  $category_attributes = $brand_filter_data['category_attributes'];
  $brand_categories = $brand_filter_data['brand_categories'];
  $brand_attributes = $brand_filter_data['brand_attributes'];

  // Organize categories into parent-child relationships for brand page
  $top_categories = array();
  $child_categories = array();
  foreach ($categories as $id => $category) {
    if ($category['parent'] == 0) {
      $top_categories[$id] = $category;
    } else {
      $child_categories[$category['parent']][$id] = $category;
    }
  }

  // Debug: Add HTML comment for debugging
  if (current_user_can('manage_options')) {
    echo "<!-- Brand Page: " . $current_brand->name . " (ID: $current_brand_id) -->";
    echo "<!-- Categories: " . count($categories) . ", Attributes: " . count($attributes) . " -->";
    echo "<!-- Price Range: " . $price_range['min'] . " - " . $price_range['max'] . " -->";
  }
}
// Otherwise use the filter parameters
else {
  // Add category filter
  if (!empty($current_filters['category'])) {
    $query_args['tax_query'][] = array(
      'taxonomy' => 'product_cat',
      'field' => 'slug',
      'terms' => $current_filters['category']
    );
  }

  // Add brand filter
  if (!empty($current_filters['brand']) && taxonomy_exists('product_brand')) {
    $query_args['tax_query'][] = array(
      'taxonomy' => 'product_brand',
      'field' => 'slug',
      'terms' => $current_filters['brand']
    );
  }
}

// Add price filter
if ($current_filters['min_price'] > $price_range['min'] || $current_filters['max_price'] < $price_range['max']) {
  $query_args['meta_query'][] = array(
    'key' => '_price',
    'value' => array($current_filters['min_price'], $current_filters['max_price']),
    'type' => 'NUMERIC',
    'compare' => 'BETWEEN'
  );
}

// Add rating filter
if ($current_filters['rating'] > 0) {
  $query_args['meta_query'][] = array(
    'key' => '_wc_average_rating',
    'value' => $current_filters['rating'],
    'type' => 'NUMERIC',
    'compare' => '>='
  );
}

// Add attribute filters
foreach ($current_filters['attributes'] as $attribute_name => $attribute_value) {
  $query_args['tax_query'][] = array(
    'taxonomy' => $attribute_name,
    'field' => 'slug',
    'terms' => explode(',', $attribute_value)
  );
}

// Apply WooCommerce product visibility fixes
if (function_exists('tendeal_fix_woocommerce_product_visibility')) {
  $query_args = tendeal_fix_woocommerce_product_visibility($query_args);
}

// Get products based on filters
$products_query = new WP_Query($query_args);

// Add structured data for better SEO
$shop_schema = array(
  '@context' => 'https://schema.org',
  '@type' => 'CollectionPage',
  'name' => get_the_title(wc_get_page_id('shop')),
  'description' => get_post_field('post_excerpt', wc_get_page_id('shop')),
  'url' => get_permalink(wc_get_page_id('shop')),
  'numberOfItems' => $products_query->found_posts
);

if (isset($_GET['s']) && !empty($_GET['s'])) {
  $shop_schema['@type'] = 'SearchResultsPage';
  $shop_schema['name'] = sprintf(__('Search Results for: %s', 'tendeal'), $_GET['s']);
}
?>

<!-- Structured Data for SEO -->
<script type="application/ld+json">
<?php echo wp_json_encode($shop_schema); ?>
</script>

<div class="container shop-container" role="main" aria-label="<?php esc_attr_e('Product Shop', 'tendeal'); ?>">
  <div class="row">
    <!-- Sidebar with dynamic filters -->
    <div class="col-lg-3 shop-sidebar">
      <?php
      // Count active filters
      $filter_count = 0;
      if (!empty($current_filters['category'])) $filter_count++;
      if (!empty($current_filters['brand'])) $filter_count++;
      if ($current_filters['min_price'] > $price_range['min'] || $current_filters['max_price'] < $price_range['max']) $filter_count++;
      if (!empty($current_filters['rating'])) $filter_count++;
      if (!empty($current_filters['attributes'])) $filter_count += count($current_filters['attributes']);
      ?>
      <form id="shop-filters-form" method="get" action="<?php echo esc_url(wc_get_page_permalink('shop')); ?>"
        data-filter-count="<?php echo esc_attr($filter_count); ?>">
        <?php
        // Preserve search query
        if (isset($_GET['s']) && !empty($_GET['s'])) : ?>
        <input type="hidden" name="s" value="<?php echo esc_attr($_GET['s']); ?>">
        <input type="hidden" name="post_type" value="product">
        <?php endif; ?>

        <?php if (is_product_category()) :
          $current_category = get_queried_object();
        ?>
        <input type="hidden" name="product_cat" value="<?php echo esc_attr($current_category->slug); ?>">
        <?php endif; ?>
        <!-- Brands Section -->
        <?php if (!empty($brands)) : ?>
        <div class="brands-section" id="brands-section">
          <h4 class="section-title">Brands</h4>
          <div class="brands-scroll-container">
            <div class="brand-logo-grid">
              <?php
              $brand_count = 0;
              $total_brands = count($brands);

              foreach ($brands as $brand_id => $brand) {
                $active_class = ($current_filters['brand'] == $brand['slug']) ? ' active' : '';
                $data_categories = '';

                if (isset($brand_categories[$brand_id])) {
                  $data_categories = implode(',', $brand_categories[$brand_id]);
                }

                // Build URL with search parameters preserved
                $brand_url_args = array('brand' => $brand['slug']);
                if (isset($_GET['s']) && !empty($_GET['s'])) {
                  $brand_url_args['s'] = $_GET['s'];
                  $brand_url_args['post_type'] = 'product';
                }
                $brand_url = add_query_arg($brand_url_args, wc_get_page_permalink('shop'));

                echo '<a href="' . esc_url($brand_url) . '"
                  class="brand-logo-item' . $active_class . '"
                  data-brand-id="' . esc_attr($brand_id) . '"
                  data-brand="' . esc_attr($brand['slug']) . '"
                  data-categories="' . esc_attr($data_categories) . '">';

                if (!empty($brand['image'])) {
                  echo '<img src="' . esc_url($brand['image']) . '" alt="' . esc_attr($brand['name']) . '">';
                } else {
                  echo '<span>' . esc_html($brand['name']) . '</span>';
                }
                echo '<span class="brand-count">(' . $brand['count'] . ')</span>';
                echo '</a>';

                $brand_count++;
              }
              ?>
            </div>
          </div>
          <?php if ($total_brands > 8) : ?>
          <!-- <div class="brands-scroll-info">
            <small class="text-muted">
              <i class="bi bi-mouse"></i> Scroll to see all <?php echo esc_html($total_brands); ?> brands
            </small>
          </div> -->
          <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Reviews Filter -->
        <div class="reviews-filter">
          <h4 class="section-title">Reviews</h4>
          <ul class="star-rating-filter">
            <?php for ($i = 5; $i >= 1; $i--) :
              $checked = ($current_filters['rating'] == $i) ? ' checked' : '';
            ?>
            <li class="star-rating-item<?php echo $checked ? ' active' : ''; ?>">
              <?php
              // Build URL with search parameters preserved
              $rating_url_args = array('rating' => $i);
              if (isset($_GET['s']) && !empty($_GET['s'])) {
                $rating_url_args['s'] = $_GET['s'];
                $rating_url_args['post_type'] = 'product';
              }
              $rating_url = add_query_arg($rating_url_args, wc_get_page_permalink('shop'));
              ?>
              <a href="<?php echo esc_url($rating_url); ?>" class="rating-link">
                <div class="stars-container">
                  <?php
                    // Display stars
                    for ($j = 1; $j <= 5; $j++) {
                      if ($j <= $i) {
                        echo '<i class="bi bi-star-fill"></i>';
                      } else {
                        echo '<i class="bi bi-star"></i>';
                      }
                    }
                    ?>
                  <span
                    class="star-rating-count">(<?php echo isset($rating_counts[$i]) ? $rating_counts[$i] : 0; ?>)</span>
                </div>
              </a>
            </li>
            <?php endfor; ?>
          </ul>
        </div>

        <!-- Price Range Filter -->
        <div class="price-range-filter">
          <h4 class="section-title">Price</h4>
          <div class="price-slider-container">
            <div class="price-range-inputs">
              <div class="price-input-group">
                <label class="price-input-label">Min</label>
                <input type="number" class="price-input" id="min-price" name="min_price"
                  value="<?php echo esc_attr($current_filters['min_price']); ?>"
                  min="<?php echo esc_attr($price_range['min']); ?>" max="<?php echo esc_attr($price_range['max']); ?>">
              </div>
              <div class="price-input-group">
                <label class="price-input-label">Max</label>
                <input type="number" class="price-input" id="max-price" name="max_price"
                  value="<?php echo esc_attr($current_filters['max_price']); ?>"
                  min="<?php echo esc_attr($price_range['min']); ?>" max="<?php echo esc_attr($price_range['max']); ?>">
              </div>
            </div>
            <div class="price-slider" id="price-range-slider" data-min="<?php echo esc_attr($price_range['min']); ?>"
              data-max="<?php echo esc_attr($price_range['max']); ?>"
              data-current-min="<?php echo esc_attr($current_filters['min_price']); ?>"
              data-current-max="<?php echo esc_attr($current_filters['max_price']); ?>"></div>
            <button type="submit" class="price-filter-apply-btn">Apply Price Filter</button>
          </div>
        </div>

        <!-- Categories Filter -->
        <?php
        // Hide categories section when on a category page, show when on brand page or shop page
        if (!empty($top_categories) && !is_product_category()) : ?>
        <div class="categories-filter" id="categories-section">
          <h4 class="section-title">Categories</h4>
          <div class="category-container">
            <ul class="category-list">
              <?php foreach ($top_categories as $id => $category) :
              $active_class = ($current_filters['category'] == $category['slug']) ? ' active' : '';
              $data_brands = '';
              $data_attributes = '';

              if (isset($category_brands[$id])) {
                $data_brands = implode(',', $category_brands[$id]);
              }

              if (isset($category_attributes[$id])) {
                $attr_data = array();
                foreach ($category_attributes[$id] as $attr_name => $term_ids) {
                  $attr_data[] = $attr_name . ':' . implode(',', $term_ids);
                }
                $data_attributes = implode(';', $attr_data);
              }
            ?>
              <li class="category-item<?php echo $active_class; ?>">
                <a href="<?php echo esc_url(get_term_link($id, 'product_cat')); ?>" class="category-link"
                  data-category-id="<?php echo esc_attr($id); ?>"
                  data-category-slug="<?php echo esc_attr($category['slug']); ?>"
                  data-brands="<?php echo esc_attr($data_brands); ?>"
                  data-attributes="<?php echo esc_attr($data_attributes); ?>">
                  <?php echo esc_html($category['name']); ?>
                  <span class="category-count">(<?php echo $category['count']; ?>)</span>
                </a>
                <?php if (isset($child_categories[$id]) && !empty($child_categories[$id])) : ?>
                <span class="toggle-subcategories" data-parent-id="<?php echo esc_attr($id); ?>">+</span>
                <ul class="subcategory-list" id="subcategory-list-<?php echo esc_attr($id); ?>">
                  <?php foreach ($child_categories[$id] as $child_id => $child) :
                    $child_active = ($current_filters['category'] == $child['slug']) ? ' active' : '';
                    $child_data_brands = '';
                    $child_data_attributes = '';

                    if (isset($category_brands[$child_id]) && !empty($category_brands[$child_id])) {
                      $child_data_brands = implode(',', $category_brands[$child_id]);
                    } else if (isset($category_brands[$id]) && !empty($category_brands[$id])) {
                      // If subcategory has no brands, inherit from parent category
                      $child_data_brands = implode(',', $category_brands[$id]);
                    }

                    if (isset($category_attributes[$child_id]) && !empty($category_attributes[$child_id])) {
                      $child_attr_data = array();
                      foreach ($category_attributes[$child_id] as $attr_name => $term_ids) {
                        if (!empty($term_ids)) {
                          $child_attr_data[] = $attr_name . ':' . implode(',', $term_ids);
                        }
                      }
                      $child_data_attributes = implode(';', $child_attr_data);
                    }

                    // If subcategory has no attributes or empty attributes, inherit from parent category
                    if (empty($child_data_attributes) && isset($category_attributes[$id]) && !empty($category_attributes[$id])) {
                      $parent_attr_data = array();
                      foreach ($category_attributes[$id] as $attr_name => $term_ids) {
                        if (!empty($term_ids)) {
                          $parent_attr_data[] = $attr_name . ':' . implode(',', $term_ids);
                        }
                      }
                      $child_data_attributes = implode(';', $parent_attr_data);
                    }
                  ?>
                  <li class="subcategory-item<?php echo $child_active; ?>">
                    <a href="<?php echo esc_url(get_term_link($child_id, 'product_cat')); ?>" class="category-link"
                      data-category-id="<?php echo esc_attr($child_id); ?>"
                      data-category-slug="<?php echo esc_attr($child['slug']); ?>"
                      data-brands="<?php echo esc_attr($child_data_brands); ?>"
                      data-attributes="<?php echo esc_attr($child_data_attributes); ?>">
                      <?php echo esc_html($child['name']); ?>
                      <span class="category-count">(<?php echo $child['count']; ?>)</span>
                    </a>
                  </li>
                  <?php endforeach; ?>
                </ul>
                <?php endif; ?>
              </li>
              <?php endforeach; ?>
            </ul>
          </div>
        </div>
        <?php endif; ?>

        <!-- Dynamic Attribute Filters -->
        <?php
        // Show attributes section on category and brand pages if there are attributes
        $show_attributes = !empty($attributes) && (is_product_category() || is_tax('product_brand'));
        $attributes_class = $show_attributes ? 'attributes-section show-filtered' : 'attributes-section';
        ?>
        <div id="attributes-section" class="<?php echo esc_attr($attributes_class); ?>">
          <?php foreach ($attributes as $attribute_name => $attribute) :
            // Skip if no terms
            if (empty($attribute['terms'])) continue;

            $param_name = 'filter_' . str_replace('pa_', '', $attribute_name);
            $current_value = isset($current_filters['attributes'][$attribute_name]) ? $current_filters['attributes'][$attribute_name] : '';
            $current_values = !empty($current_value) ? explode(',', $current_value) : array();

            // Show attribute filter on category and brand pages
            $attribute_class = $show_attributes ? 'attribute-filter show-filtered' : 'attribute-filter';
          ?>
          <div class="<?php echo esc_attr($attribute_class); ?>"
            data-attribute="<?php echo esc_attr($attribute_name); ?>">
            <h4 class="section-title"><?php echo esc_html($attribute['name']); ?></h4>
            <ul class="attribute-list">
              <?php foreach ($attribute['terms'] as $term_id => $term) :
                $checked = in_array($term['slug'], $current_values) ? ' checked' : '';
              ?>
              <li class="attribute-item<?php echo $checked ? ' active' : ''; ?>"
                data-term-id="<?php echo esc_attr($term_id); ?>">
                <?php
                // Build URL with search parameters preserved
                $attr_url_args = array($param_name => $term['slug']);
                if (isset($_GET['s']) && !empty($_GET['s'])) {
                  $attr_url_args['s'] = $_GET['s'];
                  $attr_url_args['post_type'] = 'product';
                }
                $attr_url = add_query_arg($attr_url_args, wc_get_page_permalink('shop'));
                ?>
                <a href="<?php echo esc_url($attr_url); ?>" class="attribute-link">
                  <?php echo esc_html($term['name']); ?>
                  <span class="attribute-count">(<?php echo $term['count']; ?>)</span>
                </a>
              </li>
              <?php endforeach; ?>
            </ul>
          </div>
          <?php endforeach; ?>
        </div>

        <!-- No Apply Button needed since we're using direct links -->

        <?php if (!empty($_GET)) : ?>
        <div class="reset-filters-container">
          <?php
          // Build reset URL - preserve search but remove filters
          $reset_url_args = array();
          if (isset($_GET['s']) && !empty($_GET['s'])) {
            $reset_url_args['s'] = $_GET['s'];
            $reset_url_args['post_type'] = 'product';
          }
          $reset_url = !empty($reset_url_args) ? add_query_arg($reset_url_args, wc_get_page_permalink('shop')) : wc_get_page_permalink('shop');
          ?>
          <a href="<?php echo esc_url($reset_url); ?>" class="reset-filters-btn">
            <i class="bi bi-x-circle"></i> Reset All Filters
            <?php if ($filter_count > 0) : ?>
            <span class="filter-count-badge"><?php echo esc_html($filter_count); ?></span>
            <?php endif; ?>
          </a>
        </div>
        <?php endif; ?>
      </form>
    </div>

    <!-- Main content area -->

    <div class="col-lg-9">
      <div class="col-lg-12 shop-main-content bg-white woocommerce">
        <!-- Shop Header - Clean Design -->
        <div class="shop-header-clean">
          <!-- Dynamic Breadcrumb Navigation -->
          <nav class="shop-breadcrumb" aria-label="Breadcrumb">
            <ol class="breadcrumb">
              <!-- Home Link -->
              <li class="breadcrumb-item">
                <a href="<?php echo esc_url(home_url('/')); ?>"><?php esc_html_e('Home', 'tendeal'); ?></a>
              </li>
              <?php
              // Dynamic breadcrumb based on current page context

              // 1. Search Results
              if (isset($_GET['s']) && !empty($_GET['s'])) {
                echo '<li class="breadcrumb-item">';
                echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Shop', 'tendeal') . '</a>';
                echo '</li>';
                echo '<li class="breadcrumb-item active" aria-current="page">' . esc_html__('Search Results', 'tendeal') . '</li>';
              }

              // 2. Product Category Page
              elseif (is_product_category()) {
                $current_category = get_queried_object();

                // Add Shop link
                echo '<li class="breadcrumb-item">';
                echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Shop', 'tendeal') . '</a>';
                echo '</li>';

                // Add Categories link
                echo '<li class="breadcrumb-item">';
                echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Categories', 'tendeal') . '</a>';
                echo '</li>';

                // Add parent category if exists
                if ($current_category->parent) {
                  $parent_category = get_term($current_category->parent, 'product_cat');
                  echo '<li class="breadcrumb-item">';
                  echo '<a href="' . esc_url(get_term_link($parent_category)) . '">' . esc_html($parent_category->name) . '</a>';
                  echo '</li>';
                }

                // Current category
                echo '<li class="breadcrumb-item active" aria-current="page">' . esc_html($current_category->name) . '</li>';
              }

              // 3. Product Brand Page
              elseif (is_tax('product_brand')) {
                $current_brand = get_queried_object();

                // Add Shop link
                echo '<li class="breadcrumb-item">';
                echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Shop', 'tendeal') . '</a>';
                echo '</li>';

                // Add Brands link
                echo '<li class="breadcrumb-item">';
                echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Brands', 'tendeal') . '</a>';
                echo '</li>';

                // Current brand
                echo '<li class="breadcrumb-item active" aria-current="page">' . esc_html($current_brand->name) . '</li>';
              }

              // 4. Filtered by Category (via URL parameter)
              elseif (!empty($current_filters['category'])) {
                $category_term = get_term_by('slug', $current_filters['category'], 'product_cat');

                if ($category_term) {
                  // Add Shop link
                  echo '<li class="breadcrumb-item">';
                  echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Shop', 'tendeal') . '</a>';
                  echo '</li>';

                  // Add Categories link
                  echo '<li class="breadcrumb-item">';
                  echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Categories', 'tendeal') . '</a>';
                  echo '</li>';

                  // Add parent category if exists
                  if ($category_term->parent) {
                    $parent_category = get_term($category_term->parent, 'product_cat');
                    echo '<li class="breadcrumb-item">';
                    echo '<a href="' . esc_url(get_term_link($parent_category)) . '">' . esc_html($parent_category->name) . '</a>';
                    echo '</li>';
                  }

                  // Current category
                  echo '<li class="breadcrumb-item active" aria-current="page">' . esc_html($category_term->name) . '</li>';
                }
              }

              // 5. Filtered by Brand (via URL parameter)
              elseif (!empty($current_filters['brand'])) {
                $brand_term = get_term_by('slug', $current_filters['brand'], 'product_brand');

                if ($brand_term) {
                  // Add Shop link
                  echo '<li class="breadcrumb-item">';
                  echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Shop', 'tendeal') . '</a>';
                  echo '</li>';

                  // Add Brands link
                  echo '<li class="breadcrumb-item">';
                  echo '<a href="' . esc_url(wc_get_page_permalink('shop')) . '">' . esc_html__('Brands', 'tendeal') . '</a>';
                  echo '</li>';

                  // Current brand
                  echo '<li class="breadcrumb-item active" aria-current="page">' . esc_html($brand_term->name) . '</li>';
                }
              }

              // 6. Main Shop Page (default)
              else {
                echo '<li class="breadcrumb-item active" aria-current="page">' . esc_html__('Shop', 'tendeal') . '</li>';
              }
              ?>
            </ol>
          </nav>

          <!-- Page Title and Controls Row -->
          <div class="shop-header-row">
            <!-- Page Title -->
            <div class="shop-title-area">
              <h1 class="shop-page-title">
                <?php
                // Check if this is a search
                if (isset($_GET['s']) && !empty($_GET['s'])) {
                  printf(
                    esc_html__( 'Search Results for: %s', 'tendeal' ),
                    '<span class="search-term">' . esc_html($_GET['s']) . '</span>'
                  );
                } elseif (is_product_category()) {
                  echo esc_html(single_term_title('', false));
                } elseif (is_tax('product_brand')) {
                  echo esc_html(single_term_title('', false));
                } elseif (!empty($current_filters['category'])) {
                  $category_term = get_term_by('slug', $current_filters['category'], 'product_cat');
                  if ($category_term) {
                    echo esc_html($category_term->name);
                  } else {
                    echo esc_html($current_filters['category']);
                  }
                } elseif (!empty($current_filters['brand'])) {
                  $brand_term = get_term_by('slug', $current_filters['brand'], 'product_brand');
                  if ($brand_term) {
                    echo esc_html($brand_term->name);
                  } else {
                    echo esc_html($current_filters['brand']);
                  }
                } else {
                  echo 'Shop';
                }
                ?>
              </h1>
            </div>

            <!-- Header Controls -->
            <div class="shop-header-controls">
              <!-- View Mode -->
              <div class="view-mode-selector">
                <span class="view-label"><?php esc_html_e('View :', 'tendeal'); ?></span>
                <div class="view-buttons">
                  <button class="view-btn list-view" data-view="list"
                    title="<?php esc_attr_e('List View', 'tendeal'); ?>">
                    <i data-feather="list" class="feather-sm"></i>
                  </button>
                  <button class="view-btn grid-view active" data-view="grid"
                    title="<?php esc_attr_e('Grid View', 'tendeal'); ?>">
                    <i data-feather="grid" class="feather-sm"></i>
                  </button>
                </div>
              </div>

              <!-- Sort Dropdown -->
              <div class="sort-dropdown">
                <select id="shop-orderby-clean" class="form-select" name="orderby">
                  <option value="menu_order"
                    <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'menu_order'); ?>>
                    <?php esc_html_e('Order by', 'tendeal'); ?>
                  </option>
                  <option value="popularity"
                    <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'popularity'); ?>>
                    <?php esc_html_e('Popularity', 'tendeal'); ?>
                  </option>
                  <option value="rating" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'rating'); ?>>
                    <?php esc_html_e('Rating', 'tendeal'); ?>
                  </option>
                  <option value="date" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'date'); ?>>
                    <?php esc_html_e('Latest', 'tendeal'); ?>
                  </option>
                  <option value="price" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'price'); ?>>
                    <?php esc_html_e('Price: Low to High', 'tendeal'); ?>
                  </option>
                  <option value="price-desc"
                    <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'price-desc'); ?>>
                    <?php esc_html_e('Price: High to Low', 'tendeal'); ?>
                  </option>
                </select>
              </div>

              <!-- Mobile Filter Button -->
              <button class="mobile-filter-btn d-lg-none" type="button" data-bs-toggle="offcanvas"
                data-bs-target="#shop-filters-offcanvas">
                <i class="bi bi-funnel"></i>
                <?php esc_html_e('Filters', 'tendeal'); ?>
                <?php if ($filter_count > 0) : ?>
                <span class="filter-badge"><?php echo esc_html($filter_count); ?></span>
                <?php endif; ?>
              </button>
            </div>
          </div>
        </div>

        <!-- Loading Overlay -->
        <div id="shop-loading" class="shop-loading-overlay" style="display: none;">
          <div class="loading-spinner">
            <div class="spinner-border" role="status">
              <span class="visually-hidden"><?php esc_html_e('Loading...', 'tendeal'); ?></span>
            </div>
            <p><?php esc_html_e('Loading products...', 'tendeal'); ?></p>
          </div>
        </div>

        <div id="shop-products" class="products columns-3" data-view="grid">
          <?php
        // Set the number of columns to 3
        add_filter('loop_shop_columns', function() { return 3; });

        // Override the product loop start and end to ensure our grid layout
        add_filter('woocommerce_product_loop_start', function() {
          return '<ul class="products columns-3">';
        });

        if ($products_query->have_posts()) {
          woocommerce_product_loop_start();

          while ($products_query->have_posts()) {
            $products_query->the_post();

            // Use the updated product card template if available
            if (locate_template('woocommerce/content-product-card-updated.php')) {
              wc_get_template('content-product-card-updated.php');
            } else {
              wc_get_template_part('content', 'product');
            }
          }

          woocommerce_product_loop_end();

          // Pagination Info
          $current_page = max(1, get_query_var('paged'));
          $total_products = $products_query->found_posts;
          $products_per_page = $query_args['posts_per_page'];
          $start_product = (($current_page - 1) * $products_per_page) + 1;
          $end_product = min($current_page * $products_per_page, $total_products);

          // if ($total_products > 0) {
          //   $pagination_info_class = (is_product_category() || is_tax('product_brand')) ? 'pagination-info filtered' : 'pagination-info';
          //   echo '<div class="' . esc_attr($pagination_info_class) . '">';
          //   printf(
          //     esc_html__('Showing %d-%d of %d products', 'tendeal'),
          //     $start_product,
          //     $end_product,
          //     $total_products
          //   );
          //   echo '</div>';
          // }

          // Modern Pagination
          if ($products_query->max_num_pages > 1) {
            // Build pagination arguments that preserve all current filters
            $pagination_args = array();

            // Preserve search query
            if (isset($_GET['s']) && !empty($_GET['s'])) {
              $pagination_args['s'] = sanitize_text_field($_GET['s']);
              $pagination_args['post_type'] = 'product';
            }

            // Preserve brand filter
            if (isset($_GET['brand']) && !empty($_GET['brand'])) {
              $pagination_args['brand'] = sanitize_text_field($_GET['brand']);
            }

            // Preserve category filter (for shop page with category parameter)
            if (isset($_GET['product_cat']) && !empty($_GET['product_cat'])) {
              $pagination_args['product_cat'] = sanitize_text_field($_GET['product_cat']);
            }

            // Preserve price filters
            if (isset($_GET['min_price']) && !empty($_GET['min_price'])) {
              $pagination_args['min_price'] = sanitize_text_field($_GET['min_price']);
            }
            if (isset($_GET['max_price']) && !empty($_GET['max_price'])) {
              $pagination_args['max_price'] = sanitize_text_field($_GET['max_price']);
            }

            // Preserve rating filter
            if (isset($_GET['rating']) && !empty($_GET['rating'])) {
              $pagination_args['rating'] = sanitize_text_field($_GET['rating']);
            }

            // Preserve attribute filters
            foreach ($_GET as $key => $value) {
              if (strpos($key, 'filter_') === 0 && !empty($value)) {
                $pagination_args[$key] = sanitize_text_field($value);
              }
            }

            // Preserve sorting
            if (isset($_GET['orderby']) && !empty($_GET['orderby'])) {
              $pagination_args['orderby'] = sanitize_text_field($_GET['orderby']);
            }

            // Preserve products per page
            if (isset($_GET['per_page']) && !empty($_GET['per_page'])) {
              $pagination_args['per_page'] = sanitize_text_field($_GET['per_page']);
            }

            // Build base URL
            $base_url = '';
            if (is_product_category()) {
              // For category pages, use the category URL
              $current_category = get_queried_object();
              $base_url = get_term_link($current_category);
            } elseif (is_tax('product_brand')) {
              // For brand pages, use the brand URL
              $current_brand = get_queried_object();
              $base_url = get_term_link($current_brand);
            } else {
              // For shop page, use shop URL
              $base_url = wc_get_page_permalink('shop');
            }

            // Add query parameters to base URL
            if (!empty($pagination_args)) {
              $base_url = add_query_arg($pagination_args, $base_url);
            }

            // Create pagination base with page placeholder
            $pagination_base = add_query_arg('paged', '%#%', $base_url);

            echo '<nav class="woocommerce-pagination" aria-label="Product Pagination">';
            echo paginate_links(array(
              'base' => $pagination_base,
              'format' => '',
              'current' => max(1, get_query_var('paged')),
              'total' => $products_query->max_num_pages,
              'prev_text' => '<i class="bi bi-chevron-left"></i> Previous',
              'next_text' => 'Next <i class="bi bi-chevron-right"></i>',
              'type' => 'list',
              'end_size' => 2,
              'mid_size' => 2,
              'add_args' => false, // We've already added args to the base
            ));
            echo '</nav>';

            // Add JavaScript to remove any duplicate pagination
            echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
              // Remove duplicate pagination elements
              const paginations = document.querySelectorAll(".woocommerce-pagination");
              if (paginations.length > 1) {
                // Keep the first one, remove the rest
                for (let i = 1; i < paginations.length; i++) {
                  paginations[i].remove();
                }
              }
            });
            </script>';
          }

          wp_reset_postdata();
        } else {
          ?>
          <div class="no-products">
            <div class="no-products-content">
              <i class="bi bi-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
              <h3><?php esc_html_e('No products found', 'tendeal'); ?></h3>
              <p><?php esc_html_e('No products match your current filters.', 'tendeal'); ?></p>

              <div class="no-products-actions">
                <?php
                // Build reset URL - preserve search but remove filters
                $reset_url_args = array();
                if (isset($_GET['s']) && !empty($_GET['s'])) {
                  $reset_url_args['s'] = $_GET['s'];
                  $reset_url_args['post_type'] = 'product';
                }
                $reset_url = !empty($reset_url_args) ? add_query_arg($reset_url_args, wc_get_page_permalink('shop')) : wc_get_page_permalink('shop');
                ?>
                <a href="<?php echo esc_url($reset_url); ?>" class="btn btn-primary">
                  <?php esc_html_e('Clear Filters', 'tendeal'); ?>
                </a>
                <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="btn btn-outline-secondary">
                  <?php esc_html_e('Browse All Products', 'tendeal'); ?>
                </a>
              </div>

              <!-- Popular categories suggestion -->
              <div class="suggested-categories">
                <h4><?php esc_html_e('Popular Categories', 'tendeal'); ?></h4>
                <div class="category-suggestions">
                  <?php
                  $popular_categories = get_terms(array(
                    'taxonomy' => 'product_cat',
                    'hide_empty' => true,
                    'number' => 4,
                    'orderby' => 'count',
                    'order' => 'DESC',
                    'parent' => 0
                  ));

                  if (!empty($popular_categories)) :
                    foreach ($popular_categories as $category) :
                  ?>
                  <a href="<?php echo esc_url(get_term_link($category)); ?>" class="category-suggestion">
                    <?php echo esc_html($category->name); ?>
                    <span class="product-count">(<?php echo $category->count; ?>)</span>
                  </a>
                  <?php
                    endforeach;
                  endif;
                  ?>
                </div>
              </div>
            </div>
          </div>
          <?php
        }
        ?>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Mobile Filters Offcanvas -->
<div class="offcanvas offcanvas-start" tabindex="-1" id="shop-filters-offcanvas"
  aria-labelledby="shop-filters-offcanvas-label">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="shop-filters-offcanvas-label">
      <i class="bi bi-funnel"></i>
      <?php esc_html_e('Filters', 'tendeal'); ?>
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <!-- Copy of the filters sidebar for mobile -->
    <div class="mobile-filters">
      <!-- This will be populated by JavaScript with the sidebar content -->
    </div>
  </div>
</div>

<?php
// Only call get_footer() if this template is being used directly
if ($is_direct_template) {
    get_footer();
}
?>