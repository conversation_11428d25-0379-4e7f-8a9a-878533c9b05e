{{- $url := split .Permalink "/" -}}
{{- $page_version := index $url (sub (len $url) 4) -}}
{{- $group_slug := index $url (sub (len $url) 3) -}}
{{- $page_slug := index $url (sub (len $url) 2) -}}

{{- $versions_link := "" -}}
{{- if and (eq .Layout "docs") (eq $page_version .Site.Params.docs_version) -}}
  {{- $versions_link = printf "%s/%s/" $group_slug $page_slug -}}
{{- else if (eq .Layout "single") }}
  {{- $versions_link = printf "%s/" $page_slug -}}
{{- end }}

<li class="nav-item dropdown">
  <button type="button" class="btn btn-link nav-link py-2 px-0 px-lg-2 dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-display="static">
    <span class="d-lg-none" aria-hidden="true">Bootstrap</span><span class="visually-hidden">Bootstrap&nbsp;</span> v{{ .Site.Params.docs_version }} <span class="visually-hidden">(switch to other versions)</span>
  </button>
  <ul class="dropdown-menu dropdown-menu-end">
    <li><h6 class="dropdown-header">v5 releases</h6></li>
    <li>
      <a class="dropdown-item d-flex align-items-center justify-content-between active" aria-current="true" href="{{ if .IsHome }}/{{ else }}/docs/{{ .Site.Params.docs_version }}/{{ $versions_link }}{{ end }}">
        Latest ({{ .Site.Params.docs_version }}.x)
        <svg class="bi"><use xlink:href="#check2"></use></svg>
      </a>
    </li>
    <li>
      {{- if (eq .Page.Params.added "5.3") }}
        <div class="dropdown-item disabled">v5.2.3</div>
      {{- else }}
        <a class="dropdown-item" href="https://getbootstrap.com/docs/5.2/{{ $versions_link }}">v5.2.3</a>
      {{- end }}
    </li>
    <li>
      {{- if or (eq .Page.Params.added "5.2") (eq .Page.Params.added "5.3") }}
        <div class="dropdown-item disabled">v5.1.3</div>
      {{- else }}
        <a class="dropdown-item" href="https://getbootstrap.com/docs/5.1/{{ $versions_link }}">v5.1.3</a>
      {{- end }}
    </li>
    <li>
      {{- if or (eq .Page.Params.added "5.1") (eq .Page.Params.added "5.2") (eq .Page.Params.added "5.3") }}
        <div class="dropdown-item disabled">v5.0.2</div>
      {{- else }}
        <a class="dropdown-item" href="https://getbootstrap.com/docs/5.0/{{ $versions_link }}">v5.0.2</a>
      {{- end }}
    </li>
    <li><hr class="dropdown-divider"></li>
    <li><h6 class="dropdown-header">Previous releases</h6></li>
    <li><a class="dropdown-item" href="https://getbootstrap.com/docs/4.6/">v4.6.x</a></li>
    <li><a class="dropdown-item" href="https://getbootstrap.com/docs/3.4/">v3.4.1</a></li>
    <li><a class="dropdown-item" href="https://getbootstrap.com/2.3.2/">v2.3.2</a></li>
    <li><hr class="dropdown-divider"></li>
    <li><a class="dropdown-item" href="/docs/versions/">All versions</a></li>
  </ul>
</li>
