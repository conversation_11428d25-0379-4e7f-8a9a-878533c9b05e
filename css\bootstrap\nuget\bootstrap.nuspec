<?xml version="1.0"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <id>bootstrap</id>
    <!-- pulled from package.json -->
    <version>5</version>
    <title>Bootstrap CSS</title>
    <authors>The Bootstrap Authors</authors>
    <owners>bootstrap</owners>
    <description>The most popular front-end framework for developing responsive, mobile first projects on the web.</description>
    <releaseNotes>https://blog.getbootstrap.com/</releaseNotes>
    <summary>Bootstrap framework in CSS. Includes JavaScript.</summary>
    <language>en-us</language>
    <projectUrl>https://getbootstrap.com/</projectUrl>
    <repository type="git" url="https://github.com/twbs/bootstrap.git" branch="main" />
    <icon>bootstrap.png</icon>
    <license type="expression">MIT</license>
    <copyright>Copyright 2011-2024</copyright>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <tags>css mobile-first responsive front-end framework web</tags>
    <contentFiles>
      <files include="**/*" buildAction="Content" />
    </contentFiles>
  </metadata>
  <files>
    <file src="nuget\bootstrap.png" target="" />

    <file src="dist\css\*.*" target="content\Content" />
    <file src="dist\js\bootstrap*.js" target="content\Scripts" />
    <file src="dist\js\bootstrap*.js.map" target="content\Scripts" />

    <file src="dist\css\*.*" target="contentFiles\any\any\wwwroot\css" />
    <file src="dist\js\bootstrap*.js" target="contentFiles\any\any\wwwroot\js" />
    <file src="dist\js\bootstrap*.js.map" target="contentFiles\any\any\wwwroot\js" />
  </files>
</package>
