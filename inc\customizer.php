<?php
/**
 * tendeal Theme Customizer
 *
 * @package tendeal
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function tendeal_customize_register( $wp_customize ) {
	$wp_customize->get_setting( 'blogname' )->transport         = 'postMessage';
	$wp_customize->get_setting( 'blogdescription' )->transport  = 'postMessage';
	$wp_customize->get_setting( 'header_textcolor' )->transport = 'postMessage';

	if ( isset( $wp_customize->selective_refresh ) ) {
		$wp_customize->selective_refresh->add_partial(
			'blogname',
			array(
				'selector'        => '.site-title a',
				'render_callback' => 'tendeal_customize_partial_blogname',
			)
		);
		$wp_customize->selective_refresh->add_partial(
			'blogdescription',
			array(
				'selector'        => '.site-description',
				'render_callback' => 'tendeal_customize_partial_blogdescription',
			)
		);
	}

	// Add Footer Section
	$wp_customize->add_section(
		'tendeal_footer_section',
		array(
			'title'       => __( 'Footer Settings', 'tendeal' ),
			'priority'    => 120,
			'description' => __( 'Customize the footer content', 'tendeal' ),
		)
	);

	// Footer About Text
	$wp_customize->add_setting(
		'footer_about_text',
		array(
			'default'           => 'Tendeal always provides quality products and services via its international e-commerce platform which increases the happiness of global customers.',
			'sanitize_callback' => 'sanitize_text_field',
			'transport'         => 'postMessage',
		)
	);

	$wp_customize->add_control(
		'footer_about_text',
		array(
			'label'    => __( 'About Text', 'tendeal' ),
			'section'  => 'tendeal_footer_section',
			'type'     => 'textarea',
		)
	);

	// Footer Address
	$wp_customize->add_setting(
		'footer_address',
		array(
			'default'           => 'Lusail Marina Tower 50, Floor No. 5, Doha, Qatar',
			'sanitize_callback' => 'sanitize_text_field',
			'transport'         => 'postMessage',
		)
	);

	$wp_customize->add_control(
		'footer_address',
		array(
			'label'    => __( 'Address', 'tendeal' ),
			'section'  => 'tendeal_footer_section',
			'type'     => 'text',
		)
	);

	// Footer Phone
	$wp_customize->add_setting(
		'footer_phone',
		array(
			'default'           => '+97451040008',
			'sanitize_callback' => 'sanitize_text_field',
			'transport'         => 'postMessage',
		)
	);

	$wp_customize->add_control(
		'footer_phone',
		array(
			'label'    => __( 'Phone Number', 'tendeal' ),
			'section'  => 'tendeal_footer_section',
			'type'     => 'text',
		)
	);

	// Footer Email
	$wp_customize->add_setting(
		'footer_email',
		array(
			'default'           => '<EMAIL>',
			'sanitize_callback' => 'sanitize_email',
			'transport'         => 'postMessage',
		)
	);

	$wp_customize->add_control(
		'footer_email',
		array(
			'label'    => __( 'Email Address', 'tendeal' ),
			'section'  => 'tendeal_footer_section',
			'type'     => 'email',
		)
	);

	// Footer Copyright Text
	$wp_customize->add_setting(
		'footer_copyright_text',
		array(
			'default'           => 'Copyright © ' . date('Y') . ' Tendeal',
			'sanitize_callback' => 'sanitize_text_field',
			'transport'         => 'postMessage',
		)
	);

	$wp_customize->add_control(
		'footer_copyright_text',
		array(
			'label'    => __( 'Copyright Text', 'tendeal' ),
			'section'  => 'tendeal_footer_section',
			'type'     => 'text',
			'description' => __( 'Use %year% to insert the current year dynamically', 'tendeal' ),
		)
	);
}
add_action( 'customize_register', 'tendeal_customize_register' );

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function tendeal_customize_partial_blogname() {
	bloginfo( 'name' );
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function tendeal_customize_partial_blogdescription() {
	bloginfo( 'description' );
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function tendeal_customize_preview_js() {
	wp_enqueue_script( 'tendeal-customizer', get_template_directory_uri() . '/js/customizer.js', array( 'customize-preview' ), _S_VERSION, true );
}
add_action( 'customize_preview_init', 'tendeal_customize_preview_js' );

/**
 * Get the footer copyright text with dynamic year replacement
 *
 * @return string Processed copyright text
 */
function tendeal_get_copyright_text() {
	$copyright_text = get_theme_mod('footer_copyright_text', 'Copyright © %year% Tendeal');
	$copyright_text = str_replace('%year%', date('Y'), $copyright_text);
	return $copyright_text;
}
