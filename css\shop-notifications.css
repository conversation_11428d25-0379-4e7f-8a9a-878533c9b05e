/* Shop Notifications */

.shop-notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  width: 300px;
}

.notification {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 10px;
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  position: relative;
  border-left: 4px solid #ccc;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification-success {
  border-left-color: #12b76a;
}

.notification-success::before {
  content: '✓';
  color: #12b76a;
  margin-right: 10px;
  font-weight: bold;
}

.notification-error {
  border-left-color: #f04438;
}

.notification-error::before {
  content: '✕';
  color: #f04438;
  margin-right: 10px;
  font-weight: bold;
}

.notification-info {
  border-left-color: #3498db;
}

.notification-info::before {
  content: 'ℹ';
  color: #3498db;
  margin-right: 10px;
  font-weight: bold;
}

/* Loading State for Buttons */
.action-btn.loading {
  position: relative;
  color: transparent !important;
}

.action-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  margin-left: -8px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-top-color: #fff;
  /* No spinning animation */
}