<?php
/**
 * Template for displaying product search results
 * This template is used when searching specifically for products
 *
 * @package tendeal
 */

get_header();

// Get search query
$search_query = get_search_query();
$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

// Build product search query
$args = array(
    'post_type' => 'product',
    'posts_per_page' => 12,
    'paged' => $paged,
    'post_status' => 'publish',
    's' => $search_query,
    'meta_query' => array(
        array(
            'key' => '_visibility',
            'value' => array('catalog', 'visible'),
            'compare' => 'IN'
        )
    )
);

// Execute the search query
$products_query = new WP_Query($args);
?>

<main id="primary" class="site-main">
    <div class="container">
        <!-- Search Header -->
        <div class="search-header">
            <div class="row">
                <div class="col-12">
                    <h1 class="search-title">
                        <?php
                        printf( 
                            esc_html__( 'Product Search Results for: %s', 'tendeal' ), 
                            '<span class="search-term">' . esc_html($search_query) . '</span>' 
                        );
                        ?>
                    </h1>
                    <p class="search-results-count">
                        <?php
                        printf( 
                            _n( 'Found %d product', 'Found %d products', $products_query->found_posts, 'tendeal' ), 
                            $products_query->found_posts 
                        );
                        ?>
                    </p>
                </div>
            </div>
        </div>

        <?php if ($products_query->have_posts()) : ?>
            <!-- Products Grid -->
            <div class="products-search-results">
                <div class="row">
                    <?php
                    while ($products_query->have_posts()) :
                        $products_query->the_post();
                        global $product;
                        
                        // Ensure we have a valid product
                        $product = wc_get_product(get_the_ID());
                        if ($product && $product->is_visible()) :
                    ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <?php
                            // Use the updated product card template
                            if (locate_template('woocommerce/content-product-card-updated.php')) {
                                wc_get_template('content-product-card-updated.php');
                            } else {
                                wc_get_template_part('content', 'product');
                            }
                            ?>
                        </div>
                    <?php
                        endif;
                    endwhile;
                    ?>
                </div>
            </div>

            <!-- Pagination -->
            <div class="search-pagination">
                <?php
                echo paginate_links(array(
                    'total' => $products_query->max_num_pages,
                    'current' => $paged,
                    'format' => '?paged=%#%',
                    'add_args' => array(
                        's' => $search_query,
                        'post_type' => 'product'
                    ),
                    'prev_text' => '<i data-feather="chevron-left"></i> ' . __('Previous', 'tendeal'),
                    'next_text' => __('Next', 'tendeal') . ' <i data-feather="chevron-right"></i>',
                ));
                ?>
            </div>

        <?php else : ?>
            <!-- No Results -->
            <div class="no-search-results">
                <div class="row">
                    <div class="col-12 text-center">
                        <div class="no-results-content">
                            <i data-feather="search" class="no-results-icon"></i>
                            <h2><?php esc_html_e('No products found', 'tendeal'); ?></h2>
                            <p><?php esc_html_e('Sorry, no products match your search criteria. Please try different keywords.', 'tendeal'); ?></p>
                            
                            <!-- Search suggestions -->
                            <div class="search-suggestions">
                                <h4><?php esc_html_e('Search suggestions:', 'tendeal'); ?></h4>
                                <ul>
                                    <li><?php esc_html_e('Check your spelling', 'tendeal'); ?></li>
                                    <li><?php esc_html_e('Try more general keywords', 'tendeal'); ?></li>
                                    <li><?php esc_html_e('Try different keywords', 'tendeal'); ?></li>
                                </ul>
                            </div>
                            
                            <!-- New search form -->
                            <div class="new-search-form">
                                <h4><?php esc_html_e('Try a new search:', 'tendeal'); ?></h4>
                                <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                                    <div class="search-form-wrapper">
                                        <input type="search" class="search-field" placeholder="<?php echo esc_attr_x('Search products...', 'placeholder', 'tendeal'); ?>" name="s" />
                                        <input type="hidden" name="post_type" value="product" />
                                        <button type="submit" class="search-submit">
                                            <i data-feather="search"></i>
                                            <?php esc_html_e('Search', 'tendeal'); ?>
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Browse categories -->
                            <div class="browse-categories">
                                <h4><?php esc_html_e('Or browse our categories:', 'tendeal'); ?></h4>
                                <div class="category-links">
                                    <?php
                                    $product_categories = get_terms(array(
                                        'taxonomy' => 'product_cat',
                                        'hide_empty' => true,
                                        'number' => 6,
                                        'parent' => 0
                                    ));
                                    
                                    if (!empty($product_categories)) :
                                        foreach ($product_categories as $category) :
                                    ?>
                                        <a href="<?php echo esc_url(get_term_link($category)); ?>" class="category-link">
                                            <?php echo esc_html($category->name); ?>
                                        </a>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <?php wp_reset_postdata(); ?>
    </div>
</main>

<style>
/* Product Search Results Styling */
.search-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
}

.search-title {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #333;
}

.search-term {
    color: #ea9c00;
    font-weight: bold;
}

.search-results-count {
    color: #666;
    font-size: 14px;
}

.products-search-results .row {
    margin: 0 -15px;
}

.products-search-results .col-lg-3,
.products-search-results .col-md-4,
.products-search-results .col-sm-6 {
    padding: 0 15px;
}

.search-pagination {
    text-align: center;
    margin-top: 40px;
    padding: 20px 0;
}

.search-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 4px;
    background: #f8f9fa;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s;
}

.search-pagination .page-numbers:hover,
.search-pagination .page-numbers.current {
    background: #ea9c00;
    color: white;
}

.no-search-results {
    padding: 60px 0;
}

.no-results-content {
    max-width: 600px;
    margin: 0 auto;
}

.no-results-icon {
    width: 64px;
    height: 64px;
    color: #ccc;
    margin-bottom: 20px;
}

.search-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.search-suggestions li {
    padding: 5px 0;
    color: #666;
}

.new-search-form {
    margin: 30px 0;
}

.category-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.category-link {
    display: inline-block;
    padding: 8px 16px;
    background: #f8f9fa;
    color: #333;
    text-decoration: none;
    border-radius: 20px;
    transition: all 0.2s;
}

.category-link:hover {
    background: #ea9c00;
    color: white;
}

@media (max-width: 767.98px) {
    .search-title {
        font-size: 1.5rem;
    }
    
    .products-search-results .col-lg-3,
    .products-search-results .col-md-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}
</style>

<?php
get_footer();
?>
