<?php
/**
 * Template Name: Custom Sellers Page
 * 
 * A custom template for displaying all sellers/vendors in ROG Gaming style
 */
?>

<?php get_header(); ?>

<main class="custom-sellers-container container">
  <div class="custom-sellers-header">
    <div class="custom-breadcrumb">
      <a href="<?php echo esc_url(home_url('/')); ?>">Home</a> <span class="separator">/</span> <span
        class="current">Sellers</span>
    </div>
    <h1 class="custom-sellers-title">Our Sellers</h1>
    <!-- <p class="custom-sellers-description">Discover our trusted sellers offering high-quality products</p> -->
  </div>

  <div class="custom-sellers-content">
    <!-- <div class="custom-sellers-filters">
      <div class="custom-search-box">
        <input type="text" id="seller-search" placeholder="Search sellers...">
        <button type="button" id="search-button"><i class="bi bi-search"></i></button>
      </div>
    </div> -->

    <div class="custom-sellers-grid">
      <?php echo do_shortcode('[wcfm_stores has_search="no" has_radius="no" has_country="no" has_state="no" has_orderby="no" per_row="3"]'); ?>
    </div>
  </div>
</main>

<script>
jQuery(document).ready(function($) {
  // Search functionality
  $('#search-button, #seller-search').on('keyup click', function(e) {
    if (e.type === 'click' || e.keyCode === 13) {
      const searchTerm = $('#seller-search').val().toLowerCase();

      $('.wcfmmp-store-wrap .wcfmmp-store-wrap-content').each(function() {
        const storeName = $(this).find('.store-name').text().toLowerCase();
        const storeDesc = $(this).find('.store-desc').text().toLowerCase();

        if (storeName.includes(searchTerm) || storeDesc.includes(searchTerm)) {
          $(this).closest('li').show();
        } else {
          $(this).closest('li').hide();
        }
      });
    }
  });
});
</script>

<?php get_footer(); ?>