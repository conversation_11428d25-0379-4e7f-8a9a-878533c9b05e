<?php
/**
 * Template Name: WCFM Store Template
 *
 * A custom template for displaying WCFM vendor store pages
 * This template ensures that the shop content doesn't appear after the footer
 */

get_header();

// Debug information
$debug = false;
if ($debug) {
    echo '<div style="background: #f8f8f8; padding: 15px; margin: 15px; border: 1px solid #ddd;">';
    echo '<h3>Debug Information</h3>';
    echo '<p>REQUEST_URI: ' . esc_html($_SERVER['REQUEST_URI']) . '</p>';
    echo '<p>QUERY_STRING: ' . esc_html($_SERVER['QUERY_STRING']) . '</p>';
    echo '<p>GET params: ' . esc_html(print_r($_GET, true)) . '</p>';

    if (function_exists('wcfmmp_is_store_page')) {
        echo '<p>wcfmmp_is_store_page(): ' . (wcfmmp_is_store_page() ? 'true' : 'false') . '</p>';
    } else {
        echo '<p>wcfmmp_is_store_page() function does not exist</p>';
    }

    echo '</div>';
}

// Get the store ID and name from URL
$store_id = 0;
$store_name = '';

// Method 1: Try to get from query parameters
if (isset($_GET['wcfm_store'])) {
    $store_name = sanitize_text_field($_GET['wcfm_store']);
    if (function_exists('wcfmmp_get_store_id_by_name')) {
        $store_id = wcfmmp_get_store_id_by_name($store_name);
    }
}

// Method 2: Try to get from URL path
if (!$store_id && function_exists('wcfmmp_get_store_url_base')) {
    $store_url_base = wcfmmp_get_store_url_base();
    $request_uri = isset($_SERVER['REQUEST_URI']) ? sanitize_text_field($_SERVER['REQUEST_URI']) : '';

    if (strpos($request_uri, $store_url_base) !== false) {
        $store_name = basename(rtrim($request_uri, '/'));
        if (function_exists('wcfmmp_get_store_id_by_name')) {
            $store_id = wcfmmp_get_store_id_by_name($store_name);
        }
    }
}

// Method 3: Try to get from global WCFM variables
if (!$store_id && function_exists('wcfm_get_option') && isset($GLOBALS['WCFMmp']) && isset($GLOBALS['WCFMmp']->wcfmmp_store)) {
    global $WCFMmp;
    $store_id = $WCFMmp->wcfmmp_store->get_current_store_id();
    if ($store_id && function_exists('wcfm_get_vendor_store_name')) {
        $store_name = wcfm_get_vendor_store_name($store_id);
    }
}

// Display the store content if we have a valid store ID
if ($store_id) {
        // Get store info
        $store_user = wcfmmp_get_store($store_id);
        $store_info = $store_user->get_shop_info();
        $store_name = wcfm_get_vendor_store_name($store_id);
        $store_url = wcfmmp_get_store_url($store_id);

        // Get store banner and logo
        $store_banner = $store_user->get_banner();
        $store_logo = $store_user->get_avatar();

        // Get store description
        $store_description = $store_user->get_shop_description();

        // Get store address
        $store_address = $store_user->get_address_string();

        // Get store rating
        $store_rating = 0;
        if (function_exists('wcfm_get_vendor_store_rating_info')) {
            $rating_info = wcfm_get_vendor_store_rating_info($store_id);
            $store_rating = isset($rating_info['avg_rating']) ? floatval($rating_info['avg_rating']) : 0;
        }

        // Get store products count
        $store_products_count = count_user_posts($store_id, 'product');

        // Display store header
        ?>
<div class="wcfm-store-custom-template container">
  <div class="wcfm-store-header">
    <?php if ($store_banner) : ?>
    <div class="store-banner" style="background-image: url('<?php echo esc_url($store_banner); ?>');">
      <div class="store-banner-overlay"></div>
    </div>
    <?php endif; ?>

    <div class="store-info-container">
      <div class="store-logo">
        <?php if ($store_logo) : ?>
        <img src="<?php echo esc_url($store_logo); ?>" alt="<?php echo esc_attr($store_name); ?>" />
        <?php else : ?>
        <img src="<?php echo esc_url(wc_placeholder_img_src()); ?>" alt="<?php echo esc_attr($store_name); ?>" />
        <?php endif; ?>
      </div>

      <div class="store-details">
        <h1 class="store-name"><?php echo esc_html($store_name); ?></h1>

        <?php if ($store_rating > 0) : ?>
        <div class="store-rating">
          <?php
                            $full_stars = floor($store_rating);
                            $half_star = ($store_rating - $full_stars) >= 0.5;

                            for ($i = 1; $i <= 5; $i++) {
                                if ($i <= $full_stars) {
                                    echo '<i class="bi bi-star-fill"></i>';
                                } elseif ($i == $full_stars + 1 && $half_star) {
                                    echo '<i class="bi bi-star-half"></i>';
                                } else {
                                    echo '<i class="bi bi-star"></i>';
                                }
                            }
                            ?>
          <span class="rating-value"><?php echo number_format($store_rating, 1); ?></span>
        </div>
        <?php endif; ?>

        <?php if ($store_address) : ?>
        <div class="store-address">
          <i class="bi bi-geo-alt"></i> <?php echo esc_html($store_address); ?>
        </div>
        <?php endif; ?>

        <div class="store-products-count">
          <i class="bi bi-box"></i>
          <?php echo sprintf(_n('%s product', '%s products', $store_products_count, 'tendeal'), $store_products_count); ?>
        </div>
      </div>
    </div>
  </div>

  <?php if ($store_description) : ?>
  <div class="store-description">
    <h2><?php esc_html_e('About the Store', 'tendeal'); ?></h2>
    <div class="description-content">
      <?php echo wpautop(wp_kses_post($store_description)); ?>
    </div>
  </div>
  <?php endif; ?>

  <div class="store-products">
    <h2><?php esc_html_e('Products', 'tendeal'); ?></h2>

    <?php
                // Use the WCFM store content shortcode to display products
                echo do_shortcode('[wcfm_store_products store="' . esc_attr($store_name) . '"]');
                ?>
  </div>
</div>
<?php
    } else {
        // If store not found but we're on a store page, use the default WCFM store template
        // This will let WCFM handle the store display with its own template
        if (function_exists('wcfmmp_is_store_page') && wcfmmp_is_store_page()) {
            // Just output the default WCFM store content
            echo '<div class="wcfm-store-default-content container">';
            if (function_exists('wcfmmp_get_store')) {
                // Try to use the WCFM shortcode directly
                echo do_shortcode('[wcfm_store]');
            } else {
                // Fallback to a basic message
                ?>
<div class="wcfm-store-not-found">
  <h1><?php esc_html_e('Store Not Found', 'tendeal'); ?></h1>
  <p><?php esc_html_e('The store you are looking for does not exist or has been removed.', 'tendeal'); ?></p>
  <a href="<?php echo esc_url(get_permalink(wc_get_page_id('shop'))); ?>"
    class="button"><?php esc_html_e('Return to Shop', 'tendeal'); ?></a>
</div>
<?php
            }
            echo '</div>';
        } else {
            // If not on a store page at all
            ?>
<div class="wcfm-store-not-found container">
  <h1><?php esc_html_e('Store Not Found', 'tendeal'); ?></h1>
  <p><?php esc_html_e('The store you are looking for does not exist or has been removed.', 'tendeal'); ?></p>
  <a href="<?php echo esc_url(get_permalink(wc_get_page_id('shop'))); ?>"
    class="button"><?php esc_html_e('Return to Shop', 'tendeal'); ?></a>
</div>
<?php
        }
    }
} else {
    // If WCFM is not active or not on a store page
    ?>
<div class="wcfm-not-active container">
  <h1><?php esc_html_e('Store Not Available', 'tendeal'); ?></h1>
  <p><?php esc_html_e('The store functionality is not available or you are not on a store page.', 'tendeal'); ?></p>
  <a href="<?php echo esc_url(get_permalink(wc_get_page_id('shop'))); ?>"
    class="button"><?php esc_html_e('Return to Shop', 'tendeal'); ?></a>
</div>
<?php
}

get_footer();