<?php
/**
 * Checkout Page
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/checkout.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.5.0
 */

defined( 'ABSPATH' ) || exit;

// Enqueue checkout styles and scripts
wp_enqueue_style( 'tendeal-checkout', get_template_directory_uri() . '/css/checkout.css', array(), '1.0.0' );
wp_enqueue_script( 'tendeal-checkout', get_template_directory_uri() . '/js/checkout.js', array( 'jquery' ), '1.0.0', true );

// Localize checkout script
wp_localize_script( 'tendeal-checkout', 'checkoutData', array(
    'ajax_url' => admin_url( 'admin-ajax.php' ),
    'nonce' => wp_create_nonce( 'checkout_nonce' ),
    'strings' => array(
        'loading' => __( 'Loading...', 'tendeal' ),
        'error' => __( 'An error occurred. Please try again.', 'tendeal' ),
        'select_address' => __( 'Please select an address', 'tendeal' ),
        'fill_required_fields' => __( 'Please fill in all required fields', 'tendeal' )
    )
));

// Before proceeding, make sure we have a proper cart instance and check if there's anything to checkout.
if ( ! WC()->cart || WC()->cart->is_empty() ) {
    wc_get_template( 'cart/cart-empty.php' );
    return;
}

// If not logged in, show the standard login/register form.  Important to be outside the main form.
if ( ! is_user_logged_in() ) {
	wc_get_template( 'checkout/form-checkout.php' );
	return;
}

?>

<div class="checkout-container">
  <div class="container">
    <!-- Checkout Header -->
    <div class="checkout-header">
      <h1 class="checkout-title">
        <i data-feather="shopping-bag" class="feather-sm me-2"></i>
        <?php esc_html_e( 'Checkout', 'tendeal' ); ?>
      </h1>
      <p class="checkout-subtitle"><?php esc_html_e( 'Review your order and complete your purchase', 'tendeal' ); ?></p>
    </div>

    <!-- Notices -->
    <div class="woocommerce-notices-wrapper">
      <?php wc_print_notices(); ?>
    </div>

    <div class="woocommerce-checkout-wrapper">
      <div class="woocommerce-checkout" id="checkout">

        <form name="checkout" method="post" class="checkout woocommerce-checkout"
          action="<?php echo esc_url( wc_get_checkout_url() ); ?>" enctype="multipart/form-data">

          <div class="row">
            <!-- Left Column - Customer Details -->
            <div class="col-lg-7 col-md-12">
              <div class="checkout-section" id="customer_details">
                <?php do_action( 'woocommerce_checkout_before_customer_details' ); ?>

                <!-- Billing Address Section -->
                <div class="checkout-step active" id="billing-step">
                  <div class="step-header">
                    <h3 class="step-title">
                      <i data-feather="map-pin" class="feather-sm me-2"></i>
                      <?php esc_html_e( 'Delivery Address', 'tendeal' ); ?>
                    </h3>
                    <p class="step-description"><?php esc_html_e( 'Where should we deliver your order?', 'tendeal' ); ?></p>
                  </div>

                  <div class="step-content">
                    <div class="woocommerce-billing-fields">
                      <?php do_action( 'woocommerce_checkout_billing' ); ?>
                    </div>

                    <!-- Shipping Fields -->
                    <div class="woocommerce-shipping-fields">
                      <?php if ( true === WC()->cart->needs_shipping() && ! is_cart() ) : ?>
                      <div class="ship-to-different-address">
                        <label class="checkbox-wrapper">
                          <input id="ship-to-different-address-checkbox" class="input-checkbox"
                            <?php checked( apply_filters( 'woocommerce_ship_to_different_address_checked', 'shipping' === get_option( 'woocommerce_default_address_fields' ) ), 1 ); ?>
                            type="checkbox" name="ship_to_different_address" value="1" />
                          <span class="checkmark"></span>
                          <?php esc_html_e( 'Ship to a different address?', 'tendeal' ); ?>
                        </label>
                      </div>
                      <?php endif; ?>
                      <div class="shipping_address">
                        <?php do_action( 'woocommerce_checkout_shipping' ); ?>
                      </div>
                    </div>
                  </div>
                </div>

                <?php do_action( 'woocommerce_checkout_after_customer_details' ); ?>
              </div>
            </div>

            <!-- Right Column - Order Review -->
            <div class="col-lg-5 col-md-12">
              <div class="checkout-sidebar">
                <div class="order-review-section">
                  <div class="section-header">
                    <h3 class="section-title">
                      <i data-feather="shopping-cart" class="feather-sm me-2"></i>
                      <?php esc_html_e( 'Order Summary', 'tendeal' ); ?>
                    </h3>
                  </div>

                  <?php do_action( 'woocommerce_checkout_before_order_review' ); ?>

                  <div id="order_review" class="woocommerce-checkout-review-order">
                    <?php do_action( 'woocommerce_checkout_order_review' ); ?>
                  </div>

                  <?php do_action( 'woocommerce_checkout_after_order_review' ); ?>
                </div>
              </div>
            </div>
          </div>
        </form>

        <?php do_action( 'woocommerce_after_checkout_form', $checkout ); ?>
      </div>
    </div>
  </div>
</div>

<script>
// Initialize Feather icons
document.addEventListener('DOMContentLoaded', function() {
  if (typeof feather !== 'undefined') {
    feather.replace();
  }
});
</script>