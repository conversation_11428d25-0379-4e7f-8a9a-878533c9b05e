<?php
/**
 * Checkout Page
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/checkout.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.5.0
 */

defined( 'ABSPATH' ) || exit;

// Before proceeding, make sure we have a proper cart instance and check if there's anything to checkout.
if ( ! WC()->cart || WC()->cart->is_empty() ) {
    return; // Exit if the cart is empty.
}

// If not logged in, show the standard login/register form.  Important to be outside the main form.
if ( ! is_user_logged_in() ) {
	wc_get_template( 'checkout/form-checkout.php' );
	return;
}

?>

<div class="woocommerce-checkout-wrapper">
  <div class="woocommerce-checkout" id="checkout">
    <div class="woocommerce-notices-wrapper">
      <?php wc_print_notices(); ?>
    </div>

    <form name="checkout" method="post" class="checkout woocommerce-checkout"
      action="<?php echo esc_url( wc_get_checkout_url() ); ?>" enctype="multipart/form-data">

      <div class="col2-set" id="customer_details">
        <div class="col-1" id="customer_address">

          <h1>l;ksddskf;dskf;dkf;lsdfsdfsdfsd</h1>
          <?php do_action( 'woocommerce_checkout_before_customer_details' ); ?>

          <div class="woocommerce-billing-fields">
            <h3><?php esc_html_e( 'Billing Address', 'woocommerce' ); ?></h3>
            <?php do_action( 'woocommerce_checkout_billing' ); ?>
          </div>

          <div class="woocommerce-shipping-fields">
            <?php if ( true === WC()->cart->needs_shipping() && ! is_cart() ) : ?>
            <h3 id="ship-to-different-address">
              <label class="checkbox">
                <input id="ship-to-different-address-checkbox" class="input-checkbox"
                  <?php checked( apply_filters( 'woocommerce_ship_to_different_address_checked', 'shipping' === get_option( 'woocommerce_default_address_fields' ) ), 1 ); ?>
                  type="checkbox" name="ship_to_different_address" value="1" />
                <?php esc_html_e( 'Ship to a different address?', 'woocommerce' ); ?>
              </label>
            </h3>
            <?php endif; ?>
            <div class="shipping_address">
              <?php do_action( 'woocommerce_checkout_shipping' ); ?>
            </div>
          </div>

          <?php do_action( 'woocommerce_checkout_after_customer_details' ); ?>
        </div>
      </div>

      <div class="col-2" id="order_payment_wrapper">
        <h3 id="order_review_heading"><?php esc_html_e( 'Your order', 'woocommerce' ); ?></h3>

        <?php do_action( 'woocommerce_checkout_before_order_review' ); ?>

        <div id="order_review" class="woocommerce-checkout-review-order">
          <?php do_action( 'woocommerce_checkout_order_review' ); ?>
        </div>

        <?php do_action( 'woocommerce_checkout_after_order_review' ); ?>
      </div>
    </form>

    <?php do_action( 'woocommerce_after_checkout_form', $checkout ); ?>
  </div>
</div>