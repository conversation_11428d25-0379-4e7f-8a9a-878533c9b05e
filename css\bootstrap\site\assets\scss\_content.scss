//
// Bootstrap docs content theming
//

.bd-content {
  > h2,
  > h3,
  > h4 {
    --bs-heading-color: var(--bs-emphasis-color);
  }

  // Offset content from fixed navbar when jumping to headings
  > h2:not(:first-child) {
    margin-top: 3rem;
  }

  > h3 {
    margin-top: 2rem;
  }

  > ul li,
  > ol li {
    margin-bottom: .25rem;

    // stylelint-disable selector-max-type, selector-max-compound-selectors
    > p ~ ul {
      margin-top: -.5rem;
      margin-bottom: 1rem;
    }
    // stylelint-enable selector-max-type, selector-max-compound-selectors
  }

  // Override Bootstrap defaults
  > .table,
  > .table-responsive .table {
    --bs-table-border-color: var(--bs-border-color);

    max-width: 100%;
    margin-bottom: 1.5rem;
    @include font-size(.875rem);

    @include media-breakpoint-down(lg) {
      &.table-bordered {
        border: 0;
      }
    }

    thead {
      border-bottom: 2px solid currentcolor;
    }

    tbody:not(:first-child) {
      border-top: 2px solid currentcolor;
    }

    th,
    td {
      &:first-child {
        padding-left: 0;
      }

      &:not(:last-child) {
        padding-right: 1.5rem;
      }
    }

    th {
      color: var(--bs-emphasis-color);
    }

    strong {
      color: var(--bs-emphasis-color);
    }

    // Prevent breaking of code
    // stylelint-disable-next-line selector-max-compound-selectors
    th,
    td:first-child > code {
      white-space: nowrap;
    }
  }
}

.table-options {
  td:nth-child(2) {
    min-width: 160px;
  }
}

.table-options td:last-child,
.table-utilities td:last-child {
  min-width: 280px;
}

.table-swatches {
  th {
    color: var(--bs-emphasis-color);
  }

  td code {
    white-space: nowrap;
  }
}

.bd-title {
  --bs-heading-color: var(--bs-emphasis-color);
  @include font-size(3rem);
}

.bd-lead {
  @include font-size(1.5rem);
  font-weight: 300;
}

.bi {
  width: 1em;
  height: 1em;
  vertical-align: -.125em;
  fill: currentcolor;
}

.border-lg-start {
  @include media-breakpoint-up(lg) {
    border-left: var(--bs-border-width) solid var(--bs-border-color);
  }
}

// stylelint-disable selector-no-qualifying-type
.bd-summary-link {
  color: var(--bs-link-color);

  &:hover,
  details[open] > & {
    color: var(--bs-link-hover-color);
  }
}
// stylelint-enable selector-no-qualifying-type

// scss-docs-start custom-color-mode
[data-bs-theme="blue"] {
  --bs-body-color: var(--bs-white);
  --bs-body-color-rgb: #{to-rgb($white)};
  --bs-body-bg: var(--bs-blue);
  --bs-body-bg-rgb: #{to-rgb($blue)};
  --bs-tertiary-bg: #{$blue-600};

  .dropdown-menu {
    --bs-dropdown-bg: #{mix($blue-500, $blue-600)};
    --bs-dropdown-link-active-bg: #{$blue-700};
  }

  .btn-secondary {
    --bs-btn-bg: #{mix($gray-600, $blue-400, .5)};
    --bs-btn-border-color: #{rgba($white, .25)};
    --bs-btn-hover-bg: #{darken(mix($gray-600, $blue-400, .5), 5%)};
    --bs-btn-hover-border-color: #{rgba($white, .25)};
    --bs-btn-active-bg: #{darken(mix($gray-600, $blue-400, .5), 10%)};
    --bs-btn-active-border-color: #{rgba($white, .5)};
    --bs-btn-focus-border-color: #{rgba($white, .5)};
    --bs-btn-focus-box-shadow: 0 0 0 .25rem rgba(255, 255, 255, .2);
  }
}
// scss-docs-end custom-color-mode
