<?php
/**
 * Optimized Product Card Template for Large Catalogs
 *
 * This template is optimized for performance with large product catalogs
 * - Minimal database queries
 * - Lazy loading images
 * - Cached product data
 * - Optimized markup
 *
 * @package Tendeal
 */

defined('ABSPATH') || exit;

global $product;

// Ensure we have a valid product
if (empty($product) || !$product->is_visible()) {
    return;
}

// Cache product data to minimize repeated function calls
$product_id = $product->get_id();
$product_data = array(
    'id' => $product_id,
    'name' => $product->get_name(),
    'permalink' => $product->get_permalink(),
    'price_html' => $product->get_price_html(),
    'image_id' => $product->get_image_id(),
    'gallery_ids' => $product->get_gallery_image_ids(),
    'rating_count' => $product->get_rating_count(),
    'average_rating' => $product->get_average_rating(),
    'is_on_sale' => $product->is_on_sale(),
    'is_in_stock' => $product->is_in_stock(),
    'stock_status' => $product->get_stock_status(),
    'type' => $product->get_type()
);

// Get optimized image data
$image_size = 'woocommerce_thumbnail';
$main_image = wp_get_attachment_image_src($product_data['image_id'], $image_size);
$main_image_alt = get_post_meta($product_data['image_id'], '_wp_attachment_image_alt', true);

// Get hover image (first gallery image)
$hover_image = null;
if (!empty($product_data['gallery_ids'])) {
    $hover_image = wp_get_attachment_image_src($product_data['gallery_ids'][0], $image_size);
}

// Generate placeholder image for lazy loading
$placeholder_image = 'data:image/svg+xml;base64,' . base64_encode('<svg width="300" height="300" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#f8f9fa"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#6c757d">Loading...</text></svg>');
?>

<div class="product-card optimized-card" data-product-id="<?php echo esc_attr($product_data['id']); ?>"
  data-price="<?php echo esc_attr($product->get_price()); ?>"
  data-rating="<?php echo esc_attr($product_data['average_rating']); ?>" itemscope
  itemtype="https://schema.org/Product">

  <!-- Product Image Container -->
  <div class="product-image-container">
    <a href="<?php echo esc_url($product_data['permalink']); ?>" class="product-image-link"
      aria-label="<?php echo esc_attr(sprintf(__('View %s', 'tendeal'), $product_data['name'])); ?>">

      <!-- Main Product Image -->
      <img class="product-image main-image lazy" src="<?php echo esc_url($placeholder_image); ?>"
        data-src="<?php echo esc_url($main_image ? $main_image[0] : wc_placeholder_img_src()); ?>"
        alt="<?php echo esc_attr($main_image_alt ?: $product_data['name']); ?>"
        width="<?php echo esc_attr($main_image ? $main_image[1] : 300); ?>"
        height="<?php echo esc_attr($main_image ? $main_image[2] : 300); ?>" loading="lazy" itemprop="image">

      <!-- Hover Image (if available) -->
      <?php if ($hover_image) : ?>
      <img class="product-image hover-image lazy" src="<?php echo esc_url($placeholder_image); ?>"
        data-src="<?php echo esc_url($hover_image[0]); ?>" alt="<?php echo esc_attr($product_data['name']); ?>"
        width="<?php echo esc_attr($hover_image[1]); ?>" height="<?php echo esc_attr($hover_image[2]); ?>"
        loading="lazy">
      <?php endif; ?>
    </a>

    <!-- Product Badges -->
    <div class="product-badges">
      <?php if ($product_data['is_on_sale']) : ?>
      <span class="badge sale-badge"><?php esc_html_e('Sale', 'tendeal'); ?></span>
      <?php endif; ?>

      <?php if (!$product_data['is_in_stock']) : ?>
      <span class="badge stock-badge out-of-stock"><?php esc_html_e('Out of Stock', 'tendeal'); ?></span>
      <?php elseif ($product_data['stock_status'] === 'onbackorder') : ?>
      <span class="badge stock-badge backorder"><?php esc_html_e('Backorder', 'tendeal'); ?></span>
      <?php endif; ?>
    </div>

    <!-- Quick Action Buttons -->
    <div class="product-actions">
      <button type="button" class="btn btn-sm btn-outline-primary quick-view"
        data-product-id="<?php echo esc_attr($product_data['id']); ?>"
        aria-label="<?php echo esc_attr(sprintf(__('Quick view %s', 'tendeal'), $product_data['name'])); ?>">
        <i class="fas fa-eye" aria-hidden="true"></i>
      </button>

      <button type="button" class="btn btn-sm btn-outline-secondary add-to-wishlist"
        data-product-id="<?php echo esc_attr($product_data['id']); ?>"
        aria-label="<?php echo esc_attr(sprintf(__('Add %s to wishlist', 'tendeal'), $product_data['name'])); ?>">
        <i class="fas fa-heart" aria-hidden="true"></i>
      </button>
    </div>
  </div>

  <!-- Product Info -->
  <div class="product-info">
    <!-- Product Title -->
    <h3 class="product-title" itemprop="name">
      <a href="<?php echo esc_url($product_data['permalink']); ?>" class="product-title-link">
        <?php echo esc_html($product_data['name']); ?>
      </a>
    </h3>

    <!-- Product Rating -->
    <?php if ($product_data['rating_count'] > 0) : ?>
    <div class="product-rating" itemprop="aggregateRating" itemscope itemtype="https://schema.org/AggregateRating">
      <div class="stars-container">
        <?php
                    $rating = $product_data['average_rating'];
                    for ($i = 1; $i <= 5; $i++) {
                        if ($i <= $rating) {
                            echo '<span class="star filled" aria-hidden="true">★</span>';
                        } elseif ($i - 0.5 <= $rating) {
                            echo '<span class="star half-filled" aria-hidden="true">★</span>';
                        } else {
                            echo '<span class="star empty" aria-hidden="true">★</span>';
                        }
                    }
                    ?>
      </div>
      <span class="rating-count">
        (<?php echo esc_html($product_data['rating_count']); ?>)
      </span>
      <meta itemprop="ratingValue" content="<?php echo esc_attr($rating); ?>">
      <meta itemprop="reviewCount" content="<?php echo esc_attr($product_data['rating_count']); ?>">
    </div>
    <?php else : ?>
    <!-- Show empty stars for products without reviews -->
    <div class="product-rating no-rating">
      <div class="stars-container">
        <?php for ($i = 1; $i <= 5; $i++) : ?>
        <span class="star empty" aria-hidden="true">★</span>
        <?php endfor; ?>
      </div>
      <span class="rating-count"><?php esc_html_e('No reviews', 'tendeal'); ?></span>
    </div>
    <?php endif; ?>

    <!-- Product Price -->
    <div class="product-price" itemprop="offers" itemscope itemtype="https://schema.org/Offer">
      <?php echo $product_data['price_html']; ?>
      <meta itemprop="price" content="<?php echo esc_attr($product->get_price()); ?>">
      <meta itemprop="priceCurrency" content="<?php echo esc_attr(get_woocommerce_currency()); ?>">
      <meta itemprop="availability"
        content="<?php echo esc_attr($product_data['is_in_stock'] ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'); ?>">
    </div>

    <!-- Add to Cart Button -->
    <div class="product-add-to-cart">
      <?php
            // Optimize add to cart button rendering
            if ($product_data['is_in_stock']) {
                if ($product_data['type'] === 'simple') {
                    ?>
      <button type="button" class="btn btn-primary btn-block add-to-cart-btn"
        data-product-id="<?php echo esc_attr($product_data['id']); ?>" data-quantity="1">
        <i class="fas fa-shopping-cart" aria-hidden="true"></i>
        <?php esc_html_e('Add to Cart', 'tendeal'); ?>
      </button>
      <?php
                } else {
                    ?>
      <a href="<?php echo esc_url($product_data['permalink']); ?>" class="btn btn-primary btn-block">
        <?php esc_html_e('Select Options', 'tendeal'); ?>
      </a>
      <?php
                }
            } else {
                ?>
      <button type="button" class="btn btn-secondary btn-block" disabled>
        <?php esc_html_e('Out of Stock', 'tendeal'); ?>
      </button>
      <?php
            }
            ?>
    </div>
  </div>

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "<?php echo esc_js($product_data['name']); ?>",
    "image": "<?php echo esc_js($main_image ? $main_image[0] : wc_placeholder_img_src()); ?>",
    "url": "<?php echo esc_js($product_data['permalink']); ?>",
    <?php if ($product_data['rating_count'] > 0) : ?> "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "<?php echo esc_js($product_data['average_rating']); ?>",
      "reviewCount": "<?php echo esc_js($product_data['rating_count']); ?>"
    },
    <?php endif; ?> "offers": {
      "@type": "Offer",
      "price": "<?php echo esc_js($product->get_price()); ?>",
      "priceCurrency": "<?php echo esc_js(get_woocommerce_currency()); ?>",
      "availability": "<?php echo esc_js($product_data['is_in_stock'] ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'); ?>"
    }
  }
  </script>
</div>