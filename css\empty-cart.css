/* Empty Cart Styles - Layout matching design */
.cart-container {
  padding: 40px 0;
  min-height: 60vh;
}

.empty-cart-content {
  padding: 40px 0;
}

/* Message Section */
.empty-cart-message {
  margin-bottom: 30px;
}

.empty-cart-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1e25;
  margin-bottom: 15px;
  line-height: 1.2;
}

.empty-cart-subtitle {
  font-size: 1rem;
  color: #6c757d;
  line-height: 1.6;
  margin: 0;
  max-width: 500px;
}

/* Action Buttons */
.empty-cart-actions {
  margin-bottom: 30px;
}

.empty-cart-btn-primary {
  background-color: #ea9c00;
  border-color: #ea9c00;
  color: white;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.empty-cart-btn-primary:hover {
  background-color: #d08a00;
  border-color: #d08a00;
  color: white;
  text-decoration: none;
}

/* Cart Sidebar Styles */
.cart-sidebar {
  padding: 0;
}

.cart-sidebar h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1e25;
  margin-bottom: 15px;
}

/* Coupon Section */
.cart-coupon {
  /* background: #f8f9fa; */
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.cart-coupon input {
  border-radius: 8px;
  border: 1px solid #ddd;
  padding: 12px 15px;
  margin-bottom: 15px;
  font-size: 0.95rem;
}

.cart-coupon button {
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 500;
  border: 1px solid #6c757d;
  background: transparent;
  color: #6c757d;
  transition: all 0.3s ease;
}

.cart-coupon button:hover {
  background: #6c757d;
  color: white;
}

/* Cart Totals */
.cart-totals {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.cart-totals-content {
  margin-top: 15px;
}

.cart-total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.cart-total-row:last-of-type {
  border-bottom: none;
  font-weight: 600;
}

.cart-total-label {
  color: #6c757d;
  font-size: 0.95rem;
}

.cart-total-value {
  color: #1a1e25;
  font-weight: 500;
}

.checkout-btn {
  background-color: #ea9c00;
  border-color: #ea9c00;
  color: white;
  padding: 15px;
  font-weight: 600;
  border-radius: 8px;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Payment Methods */
.payment-methods {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
}

.payment-description {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 15px;
  line-height: 1.5;
}

.payment-icons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.payment-icon {
  height: 30px;
  width: auto;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  padding: 5px;
  background: white;
}

/* Responsive Design */
@media (max-width: 991px) {
  .cart-container {
    padding: 20px 0;
  }

  .cart-sidebar {
    margin-top: 30px;
  }
}

@media (max-width: 768px) {
  .cart-container {
    padding: 15px 0;
  }

  .empty-cart-content {
    padding: 20px 0;
  }

  .empty-cart-title {
    font-size: 2rem;
  }

  .empty-cart-subtitle {
    font-size: 0.95rem;
  }

  .cart-sidebar {
    margin-top: 25px;
  }

  .cart-coupon,
  .cart-totals,
  .payment-methods {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .empty-cart-title {
    font-size: 1.8rem;
  }

  .empty-cart-btn-primary {
    padding: 10px 20px;
    font-size: 0.95rem;
  }

  .cart-coupon,
  .cart-totals,
  .payment-methods {
    padding: 12px;
  }

  .payment-icons {
    gap: 8px;
  }

  .payment-icon {
    height: 25px;
  }
}