<?php
/**
 * Orders
 *
 * Shows orders on the account page.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/orders.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.5.0
 */

defined( 'ABSPATH' ) || exit;

do_action( 'woocommerce_before_account_orders', $has_orders ); ?>

<div class="orders-page-header">
  <div class="breadcrumb">
    <span class="breadcrumb-item">
      <a href="<?php echo esc_url( wc_get_account_endpoint_url( 'dashboard' ) ); ?>">Home</a>
    </span>
    <span class="breadcrumb-separator">></span>
    <span class="breadcrumb-item current">Orders</span>
  </div>
  <h1 class="orders-title">Orders</h1>
</div>

<?php if ( $has_orders ) : ?>

<!-- Order Status Filter Tabs -->
<div class="order-status-filters">
  <div class="filter-tabs">
    <button class="filter-tab active" data-status="all">
      <span class="tab-text">All</span>
      <span class="tab-count">(<?php echo count( $customer_orders->orders ); ?>)</span>
    </button>
    <?php
            // Get order status counts
            $status_counts = array();
            foreach ( $customer_orders->orders as $customer_order ) {
                $order = wc_get_order( $customer_order );
                $status = $order->get_status();
                if ( ! isset( $status_counts[ $status ] ) ) {
                    $status_counts[ $status ] = 0;
                }
                $status_counts[ $status ]++;
            }

            $status_labels = array(
                'pending' => 'Not paid',
                'processing' => 'Processing',
                'shipped' => 'Shipped',
                'completed' => 'Complete',
                'cancelled' => 'Cancelled'
            );

            foreach ( $status_labels as $status => $label ) :
                $count = isset( $status_counts[ $status ] ) ? $status_counts[ $status ] : 0;
            ?>
    <button class="filter-tab" data-status="<?php echo esc_attr( $status ); ?>">
      <span class="tab-text"><?php echo esc_html( $label ); ?></span>
      <span class="tab-count">(<?php echo $count; ?>)</span>
    </button>
    <?php endforeach; ?>
  </div>

  <!-- Search and Time Filter -->
  <div class="order-filters-right">
    <div class="order-search">
      <input type="text" id="order-search" placeholder="Order id or product name" class="search-input">
      <i data-feather="search" class="search-icon"></i>
    </div>
    <div class="time-filter">
      <select id="time-filter" class="time-select">
        <option value="all">All time</option>
        <option value="week">Last week</option>
        <option value="month">Last month</option>
        <option value="3months">Last 3 months</option>
        <option value="year">Last year</option>
      </select>
      <i data-feather="chevron-down" class="select-icon"></i>
    </div>
  </div>
</div>

<!-- Orders Grid -->
<div class="orders-grid">
  <?php
        foreach ( $customer_orders->orders as $customer_order ) {
            $order = wc_get_order( $customer_order );
            $items = $order->get_items();
            $first_item = reset( $items );
            $product = $first_item ? wc_get_product( $first_item->get_product_id() ) : null;
            $item_count = $order->get_item_count() - $order->get_item_count_refunded();
            ?>
  <div class="order-card" data-status="<?php echo esc_attr( $order->get_status() ); ?>"
    data-order-id="<?php echo esc_attr( $order->get_order_number() ); ?>">
    <!-- Order Status Badge -->
    <!-- <div class="order-status-badge order-status-<?php echo esc_attr( $order->get_status() ); ?>">
      <?php echo esc_html( wc_get_order_status_name( $order->get_status() ) ); ?>
    </div> -->

    <!-- Order Header -->
    <div class="order-header">
      <div class="order-status-badge order-status-<?php echo esc_attr( $order->get_status() ); ?>">
        <?php echo esc_html( wc_get_order_status_name( $order->get_status() ) ); ?>
      </div>
      <div style="
    display: flex;
    gap: 33px;
">
        <div class="order-meta">
          <span class="order-date">Ordered:
            <?php echo esc_html( $order->get_date_created()->date_i18n( 'M j, Y' ) ); ?></span>
          <span class="order-id">Order ID: <?php echo esc_html( $order->get_order_number() ); ?></span>
          <!-- <span class="order-copy">Copy</span> -->
        </div>
        <a href="<?php echo esc_url( $order->get_view_order_url() ); ?>" class="order-details-link">
          Details <i data-feather="arrow-right" class="details-icon"></i>
        </a>
      </div>

    </div>

    <!-- Product Info -->
    <?php if ( $product ) : ?>
    <div class="order-product">
      <div class="product-image">
        <?php echo $product->get_image( 'thumbnail' ); ?>
      </div>
      <div class="product-details">
        <h4 class="product-name"><?php echo esc_html( $product->get_name() ); ?></h4>
        <p class="product-description">
          <?php
                            $description = $product->get_short_description();
                            if ( empty( $description ) ) {
                                $description = wp_trim_words( $product->get_description(), 15 );
                            }
                            echo esc_html( wp_trim_words( $description, 15 ) );
                            ?>
        </p>
        <div class="product-price">
          <span class="price-label">Total:</span>
          <span class="price-amount"><?php echo $order->get_formatted_order_total(); ?></span>
          <span class="currency">QAR</span>
        </div>
      </div>
    </div>
    <?php endif; ?>

    <!-- Order Actions -->
    <div class="order-actions">
      <?php
                    $actions = wc_get_account_orders_actions( $order );
                    $status = $order->get_status();

                    // Custom action buttons based on order status
                    if ( $status === 'completed' ) : ?>
      <button class="action-btn primary">Confirm received order</button>
      <button class="action-btn secondary">
        <i data-feather="map-pin" class="btn-icon"></i>
        Track order
      </button>
      <?php elseif ( $status === 'processing' ) : ?>
      <button class="action-btn secondary">
        <i data-feather="map-pin" class="btn-icon"></i>
        Track order
      </button>
      <button class="action-btn danger">Cancel order</button>
      <?php else : ?>
      <?php if ( ! empty( $actions ) ) : ?>
      <?php foreach ( $actions as $key => $action ) : ?>
      <a href="<?php echo esc_url( $action['url'] ); ?>" class="action-btn secondary">
        <?php echo esc_html( $action['name'] ); ?>
      </a>
      <?php endforeach; ?>
      <?php endif; ?>
      <?php endif; ?>

      <!-- Additional actions for completed orders -->
      <?php if ( $status === 'completed' && $product ) : ?>
      <div class="additional-actions">
        <button class="action-btn outline">
          <i data-feather="shopping-cart" class="btn-icon"></i>
          Add to cart
        </button>
        <button class="action-btn outline">
          <i data-feather="trash-2" class="btn-icon"></i>
          Delete
        </button>
        <button class="action-btn outline">
          <i data-feather="star" class="btn-icon"></i>
          Add review
        </button>
      </div>
      <?php endif; ?>
    </div>
  </div>
  <?php
        }
        ?>
</div>

<!-- View More Button -->
<?php if ( 1 < $customer_orders->max_num_pages ) : ?>
<div class="orders-pagination">
  <button class="view-more-btn" id="view-more-orders">
    <i data-feather="plus" class="view-more-icon"></i>
    View more
  </button>
</div>
<?php endif; ?>

<?php else : ?>

<div class="no-orders-state">
  <div class="no-orders-icon">
    <i data-feather="package" class="empty-icon"></i>
  </div>
  <h3 class="no-orders-title">No orders yet</h3>
  <p class="no-orders-text">You haven't placed any orders yet. Start shopping to see your orders here.</p>
  <a href="<?php echo esc_url( apply_filters( 'woocommerce_return_to_shop_redirect', wc_get_page_permalink( 'shop' ) ) ); ?>"
    class="browse-products-btn">
    Browse products
  </a>
</div>

<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Filter functionality
  const filterTabs = document.querySelectorAll('.filter-tab');
  const orderCards = document.querySelectorAll('.order-card');
  const searchInput = document.getElementById('order-search');

  // Tab filtering
  filterTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // Remove active class from all tabs
      filterTabs.forEach(t => t.classList.remove('active'));
      // Add active class to clicked tab
      this.classList.add('active');

      const status = this.dataset.status;

      orderCards.forEach(card => {
        if (status === 'all' || card.dataset.status === status) {
          card.style.display = 'block';
        } else {
          card.style.display = 'none';
        }
      });
    });
  });

  // Search functionality
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();

      orderCards.forEach(card => {
        const orderId = card.dataset.orderId.toLowerCase();
        const productName = card.querySelector('.product-name');
        const productNameText = productName ? productName.textContent.toLowerCase() : '';

        if (orderId.includes(searchTerm) || productNameText.includes(searchTerm)) {
          card.style.display = 'block';
        } else {
          card.style.display = 'none';
        }
      });
    });
  }

  // Copy order ID functionality
  document.querySelectorAll('.order-copy').forEach(copyBtn => {
    copyBtn.addEventListener('click', function() {
      const orderCard = this.closest('.order-card');
      const orderId = orderCard.dataset.orderId;

      navigator.clipboard.writeText(orderId).then(() => {
        this.textContent = 'Copied!';
        setTimeout(() => {
          this.textContent = 'Copy';
        }, 2000);
      });
    });
  });

  // Initialize Feather icons
  if (typeof feather !== 'undefined') {
    feather.replace();
  }
});
</script>

<?php do_action( 'woocommerce_after_account_orders', $has_orders ); ?>