/**
 * Shop Page with Filters JavaScript
 */

(function($) {
  'use strict';

  // Initialize when document is ready
  $(document).ready(function() {
    // Initialize filters
    initFilters();

    // Initialize view mode switching
    initViewMode();

    // Initialize sorting
    initSorting();

    // Initialize product actions
    initProductActions();
  });

  /**
   * Initialize filters
   */
  function initFilters() {
    // Filter section toggle
    $('.filter-title').on('click', function() {
      $(this).toggleClass('collapsed');
      $(this).next().toggle();
    });

    // Apply filters button
    $('#apply-filters').on('click', function() {
      applyFilters();
    });

    // Reset filters button
    $('#reset-filters').on('click', function() {
      resetFilters();
    });

    // Price slider (if not using WooCommerce price filter)
    if ($('.price-slider').length && !$('.price_slider').length) {
      $('.price-slider').slider({
        range: true,
        min: 0,
        max: 1000,
        values: [0, 1000],
        slide: function(event, ui) {
          $('.min-price').text('$' + ui.values[0]);
          $('.max-price').text('$' + ui.values[1]);
        }
      });
    }
  }

  /**
   * Apply all filters
   */
  function applyFilters() {
    // Get all selected filters
    const selectedCategories = getSelectedValues('.category-checkbox:checked');
    const selectedBrands = getSelectedValues('.brand-checkbox:checked');
    const selectedColors = getSelectedValues('.color-checkbox:checked');
    const selectedRatings = getSelectedValues('.rating-checkbox:checked');

    // Get price range
    let minPrice = 0;
    let maxPrice = 9999999;

    if ($('.price_slider').length) {
      // WooCommerce price slider
      const priceText = $('.price_slider_amount .price_label').text();
      const priceMatch = priceText.match(/(\d+\.?\d*)/g);

      if (priceMatch && priceMatch.length >= 2) {
        minPrice = parseFloat(priceMatch[0]);
        maxPrice = parseFloat(priceMatch[1]);
      }
    } else if ($('.price-slider').length) {
      // Custom price slider
      minPrice = $('.price-slider').slider('values', 0);
      maxPrice = $('.price-slider').slider('values', 1);
    }

    // Filter products
    filterProducts(selectedCategories, selectedBrands, selectedColors, selectedRatings, minPrice, maxPrice);
  }

  /**
   * Reset all filters
   */
  function resetFilters() {
    // Uncheck all checkboxes
    $('.filter-checkbox').prop('checked', false);

    // Reset price slider
    if ($('.price_slider').length) {
      // Trigger WooCommerce price slider reset
      $('.price_slider_amount button[type="submit"]').click();
    } else if ($('.price-slider').length) {
      // Reset custom price slider
      $('.price-slider').slider('values', [0, 1000]);
      $('.min-price').text('$0');
      $('.max-price').text('$1000');
    }

    // Show all products
    $('.product-card').show();

    // Update product count
    updateProductCount();
  }

  /**
   * Filter products based on selected filters
   */
  function filterProducts(categories, brands, colors, ratings, minPrice, maxPrice) {
    // Show all products first
    $('.product-card').show();

    // Filter by category
    if (categories.length > 0) {
      $('.product-card').each(function() {
        const productCategories = $(this).data('categories').toLowerCase().split(',');
        let categoryMatch = false;

        categories.forEach(function(category) {
          if (productCategories.some(cat => cat.toLowerCase().includes(category.toLowerCase()))) {
            categoryMatch = true;
          }
        });

        if (!categoryMatch) {
          $(this).hide();
        }
      });
    }

    // Filter by brand
    if (brands.length > 0) {
      $('.product-card:visible').each(function() {
        const productBrands = $(this).data('brands').toLowerCase().split(',');
        let brandMatch = false;

        brands.forEach(function(brand) {
          if (productBrands.some(b => b.toLowerCase().includes(brand.toLowerCase()))) {
            brandMatch = true;
          }
        });

        if (!brandMatch) {
          $(this).hide();
        }
      });
    }

    // Filter by color
    if (colors.length > 0) {
      $('.product-card:visible').each(function() {
        const productColors = $(this).data('colors').toLowerCase().split(',');
        let colorMatch = false;

        colors.forEach(function(color) {
          if (productColors.some(c => c.toLowerCase().includes(color.toLowerCase()))) {
            colorMatch = true;
          }
        });

        if (!colorMatch) {
          $(this).hide();
        }
      });
    }

    // Filter by rating
    if (ratings.length > 0) {
      $('.product-card:visible').each(function() {
        const productRating = parseInt($(this).data('rating'));

        if (!ratings.includes(productRating.toString())) {
          $(this).hide();
        }
      });
    }

    // Filter by price
    $('.product-card:visible').each(function() {
      const productPrice = parseFloat($(this).data('price'));

      if (productPrice < minPrice || productPrice > maxPrice) {
        $(this).hide();
      }
    });

    // Update product count
    updateProductCount();
  }

  /**
   * Get selected values from checkboxes
   */
  function getSelectedValues(selector) {
    const values = [];

    $(selector).each(function() {
      values.push($(this).val());
    });

    return values;
  }

  /**
   * Update product count
   */
  function updateProductCount() {
    const visibleProducts = $('.product-card:visible').length;
    const totalProducts = $('.product-card').length;

    if (visibleProducts === totalProducts) {
      $('#product-count').text('Showing all ' + totalProducts + ' products');
    } else {
      $('#product-count').text('Showing ' + visibleProducts + ' of ' + totalProducts + ' products');
    }
  }

  /**
   * Initialize view mode switching
   */
  function initViewMode() {
    $('.view-mode-btn').on('click', function() {
      const viewMode = $(this).data('view');

      // Update active button
      $('.view-mode-btn').removeClass('active');
      $(this).addClass('active');

      // Update view mode
      if (viewMode === 'grid') {
        $('#shop-products').removeClass('list-view');
      } else {
        $('#shop-products').addClass('list-view');
      }
    });
  }

  /**
   * Initialize sorting
   */
  function initSorting() {
    $('#shop-orderby').on('change', function() {
      const orderby = $(this).val();
      const products = $('.product-card').get();

      // Sort products
      products.sort(function(a, b) {
        switch (orderby) {
          case 'price':
            return $(a).data('price') - $(b).data('price');
          case 'price-desc':
            return $(b).data('price') - $(a).data('price');
          case 'rating':
            return $(b).data('rating') - $(a).data('rating');
          case 'date':
            // For simplicity, we'll just use the DOM order for date sorting
            return $(b).index() - $(a).index();
          case 'popularity':
            // For simplicity, we'll use rating as a proxy for popularity
            return $(b).data('rating') - $(a).data('rating');
          default:
            // Default sorting (menu_order)
            return $(a).index() - $(b).index();
        }
      });

      // Reorder products
      const productsContainer = $('#shop-products');
      $.each(products, function(i, product) {
        productsContainer.append(product);
      });
    });
  }

  /**
   * Initialize product actions
   */
  function initProductActions() {
    // Add to cart button
    $(document).on('click', '.add-to-cart, .add-to-cart-btn', function(e) {
      e.preventDefault();
      const productId = $(this).data('product-id');

      // Add loading state
      $(this).addClass('loading');

      // AJAX add to cart
      $.ajax({
        url: wc_add_to_cart_params.ajax_url,
        type: 'POST',
        data: {
          action: 'woocommerce_ajax_add_to_cart',
          product_id: productId,
          quantity: 1
        },
        success: function(response) {
          if (response.success) {
            // Show success message
            showNotification('Product added to cart', 'success');

            // Update cart fragments
            $(document.body).trigger('wc_fragment_refresh');
          } else {
            showNotification('Failed to add product to cart', 'error');
          }
        },
        error: function() {
          showNotification('Error occurred. Please try again.', 'error');
        },
        complete: function() {
          // Remove loading state
          $('.add-to-cart[data-product-id="' + productId + '"], .add-to-cart-btn[data-product-id="' + productId + '"]').removeClass('loading');
        }
      });
    });

    // Add to wishlist button
    $(document).on('click', '.add-to-wishlist', function(e) {
      e.preventDefault();
      const productId = $(this).data('product-id');

      // Toggle active state
      $(this).toggleClass('active');

      if ($(this).hasClass('active')) {
        showNotification('Product added to wishlist', 'success');
      } else {
        showNotification('Product removed from wishlist', 'info');
      }
    });

    // Quick view button
    $(document).on('click', '.quick-view', function(e) {
      e.preventDefault();
      const productId = $(this).data('product-id');

      // Show loading state
      $(this).addClass('loading');

      // AJAX quick view
      $.ajax({
        url: wc_add_to_cart_params.ajax_url,
        type: 'POST',
        data: {
          action: 'product_quick_view',
          product_id: productId
        },
        success: function(response) {
          if (response.success) {
            // Show quick view modal
            $('body').append(response.data.html);
            $('#quick-view-modal').modal('show');
          } else {
            showNotification('Failed to load product details', 'error');
          }
        },
        error: function() {
          showNotification('Error occurred. Please try again.', 'error');
        },
        complete: function() {
          // Remove loading state
          $('.quick-view[data-product-id="' + productId + '"]').removeClass('loading');
        }
      });
    });
  }

  /**
   * Show notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error, info)
   */
  function showNotification(message, type) {
    // Create notification container if it doesn't exist
    if ($('.notification-container').length === 0) {
      $('body').append('<div class="notification-container"></div>');
    }

    const $notificationContainer = $('.notification-container');

    // Create notification element
    const $notification = $('<div class="notification notification-' + type + '">' + message + '</div>');

    // Add to container
    $notificationContainer.append($notification);

    // Show notification
    setTimeout(function() {
      $notification.addClass('show');
    }, 10);

    // Remove notification after delay
    setTimeout(function() {
      $notification.removeClass('show');
      setTimeout(function() {
        $notification.remove();
      }, 300);
    }, 3000);
  }

})(jQuery);
