/* Header Account Styles */

/* Account dropdown */
.site-header__list li.account-menu-item {
  position: relative;
}

.site-header__list li.account-menu-item:hover .account-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.site-header__list li.account-menu-item .account-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  width: 220px;
  padding: 10px 0;
  z-index: 100;
  margin-top: 10px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.site-header__list li.account-menu-item .account-dropdown:before {
  content: '';
  position: absolute;
  top: -10px;
  right: 20px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid white;
}

.site-header__list li.account-menu-item .account-dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.site-header__list li.account-menu-item .account-dropdown ul li {
  margin: 0;
  display: block;
}

.site-header__list li.account-menu-item .account-dropdown ul li a {
  padding: 10px 20px;
  display: flex;
  align-items: center;
  color: #333;
  font-size: 14px;
  transition: background-color 0.2s;
}

.site-header__list li.account-menu-item .account-dropdown ul li a:hover {
  background-color: rgba(234, 156, 0, 0.1);
  color: #ea9c00;
}

.site-header__list li.account-menu-item .account-dropdown ul li a i {
  margin-right: 10px;
  font-size: 16px;
}

/* Login button */
.site-header__list li .login-button {
  background-color: #ea9c00;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.site-header__list li .login-button:hover {
  background-color: #c88500;
  color: white;
}

/* Cart count */
.site-header__list li .cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ea9c00;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Links */
.site-header__list li a {
  display: flex;
  align-items: center;
  color: inherit;
  text-decoration: none;
}

.site-header__list li a:hover {
  color: #ea9c00;
}

/* Responsive styles */
@media (max-width: 768px) {
  .site-header__list li.account-menu-item .account-dropdown {
    position: fixed;
    top: auto;
    left: 0;
    right: 0;
    width: 100%;
    margin-top: 0;
    border-radius: 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  .site-header__list li.account-menu-item .account-dropdown:before {
    display: none;
  }
}
