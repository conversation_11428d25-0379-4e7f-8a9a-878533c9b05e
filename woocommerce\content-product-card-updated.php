<?php
/**
 * Updated Product Card Template
 *
 * Matches the design shown in the image with modern styling
 */

if (!defined('ABSPATH')) {
    exit;
}

global $product, $woocommerce_loop, $post;

// Comprehensive product object validation and initialization
if (empty($post)) {
    return; // No post data available
}

// Ensure we have a valid product object
if (empty($product) || !is_a($product, 'WC_Product')) {
    $product = wc_get_product($post->ID);
}

// Final validation - ensure we have a valid product and it's visible
if (!$product || !is_a($product, 'WC_Product') || !$product->is_visible()) {
    return;
}

// Ensure the global product is properly set
$GLOBALS['product'] = $product;

// Get product data with fallbacks
$product_id = $product->get_id();
$product_name = $product->get_name() ?: 'Product Name';
$product_link = $product->get_permalink() ?: '#';

// Get product image with fallback
$image_id = get_post_thumbnail_id($product_id);
if ($image_id) {
    $image_data = wp_get_attachment_image_src($image_id, 'woocommerce_thumbnail');
    $image_url = $image_data ? $image_data[0] : wc_placeholder_img_src();
} else {
    $image_url = wc_placeholder_img_src();
}

// Get pricing data with fallbacks
$regular_price = $product->get_regular_price();
$sale_price = $product->get_sale_price();
$current_price = $product->get_price();
$price_html = $product->get_price_html();

// Ensure we have a price to display
if (empty($price_html) && $current_price) {
    $price_html = wc_price($current_price);
} elseif (empty($price_html)) {
    $price_html = '<span class="price">Price not available</span>';
}

$short_description = $product->get_short_description();
$average_rating = $product->get_average_rating();
$review_count = $product->get_review_count();
$stock_status = $product->get_stock_status();
$is_in_stock = $product->is_in_stock();

// Calculate discount percentage
$discount_percentage = 0;
if ($product->is_on_sale() && $regular_price && $sale_price && $regular_price > $sale_price) {
    $discount_percentage = round((($regular_price - $sale_price) / $regular_price) * 100);
}

// Truncate description
$short_desc_excerpt = wp_trim_words($short_description, 15, '...');

// Enhanced debug information (only visible in browser console)
if (WP_DEBUG) {
    $debug_info = array(
        'Product_ID' => $product_id,
        'Product_Name' => $product_name,
        'Post_ID' => $post->ID ?? 'N/A',
        'Product_Type' => $product->get_type(),
        'Price_HTML' => $price_html,
        'Stock_Status' => $stock_status,
        'Is_Visible' => $product->is_visible() ? 'Yes' : 'No',
        'Loop_Index' => wc_get_loop_prop('loop', 0)
    );
    echo "<!-- Product Debug Info: " . json_encode($debug_info) . " -->";
}
?>

<div class="modern-product-card" data-product-id="<?php echo esc_attr($product_id); ?>">
  <!-- Product Image Container -->
  <div class="product-image-container">
    <!-- Compare Button (Top Left) -->
    <div class="product-actions-top">
      <?php if (class_exists('YITH_Woocompare')) : ?>
      <button class="compare-btn yith-compare-btn" data-product-id="<?php echo esc_attr($product_id); ?>"
        title="Compare">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
          stroke-linecap="round" stroke-linejoin="round">
          <polyline points="17,1 21,5 17,9"></polyline>
          <path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
          <polyline points="7,23 3,19 7,15"></polyline>
          <path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
        </svg>
      </button>
      <?php endif; ?>
    </div>

    <!-- Discount Badge (Top Right) -->
    <?php if ($discount_percentage > 0) : ?>
    <div class="discount-badge">-<?php echo esc_html($discount_percentage); ?>%</div>
    <?php endif; ?>

    <!-- Product Image -->
    <a href="<?php echo esc_url($product_link); ?>" class="product-image-link">
      <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product_name); ?>" class="product-image"
        onerror="this.src='<?php echo esc_url(wc_placeholder_img_src()); ?>'">
    </a>
  </div>

  <!-- Product Details -->
  <div class="product-details">
    <!-- Product Title -->
    <h3 class="product-title">
      <a href="<?php echo esc_url($product_link); ?>"><?php echo esc_html($product_name); ?></a>
    </h3>

    <!-- Product Rating -->
    <div class="product-rating">
      <?php if ($review_count > 0) : ?>
      <div class="star-rating">
        <?php for ($i = 1; $i <= 5; $i++) : ?>
        <?php if ($i <= $average_rating) : ?>
        <span class="star filled">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26">
            </polygon>
          </svg>
        </span>
        <?php elseif ($i - 0.5 <= $average_rating) : ?>
        <span class="star half">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="url(#half-star-gradient)" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26">
            </polygon>
          </svg>
        </span>
        <?php else : ?>
        <span class="star empty">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26">
            </polygon>
          </svg>
        </span>
        <?php endif; ?>
        <?php endfor; ?>
      </div>
      <span class="review-count"><?php echo esc_html($review_count); ?> Reviews</span>
      <?php else : ?>
      <!-- Gray stars when no reviews -->
      <div class="star-rating no-reviews">
        <?php for ($i = 1; $i <= 5; $i++) : ?>
        <span class="star gray">
          <i data-feather="star" class="feather-sm"></i>
          <!-- <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26">
            </polygon>
          </svg> -->
        </span>
        <?php endfor; ?>
      </div>
      <span class="review-count gray">No Reviews</span>
      <?php endif; ?>
    </div>

    <!-- Product Price -->
    <div class="product-price">
      <?php if ($product->is_on_sale() && $sale_price && $regular_price) : ?>
      <span class="current-price"><?php echo wc_price($sale_price); ?></span>
      <span class="original-price"><?php echo wc_price($regular_price); ?></span>
      <?php elseif ($current_price) : ?>
      <span class="current-price"><?php echo wc_price($current_price); ?></span>
      <?php else : ?>
      <?php echo $price_html; ?>
      <?php endif; ?>
    </div>



    <!-- Secondary Actions Section (Like Design Image) -->
    <div class="product-secondary-actions">
      <!-- Add to Cart Action -->
      <div class="secondary-action-item">
        <?php if ($is_in_stock) : ?>

        <?php if ($product->get_type() === 'simple') : ?>
        <button class="add-to-cart-btn" data-product-id="<?php echo esc_attr($product_id); ?>">
          <!--  -->
          <i data-feather="shopping-cart" class="feather-sm"></i>
          <!-- <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <circle cx="9" cy="21" r="1"></circle>
            <circle cx="20" cy="21" r="1"></circle>
            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
          </svg> -->
          Add to cart
        </button>
        <?php else : ?>
        <a href="<?php echo esc_url($product_link); ?>" class="add-to-cart-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          View Options
        </a>
        <?php endif; ?>
        <!-- <button class="secondary-add-cart-btn" data-product-id="<?php echo esc_attr($product_id); ?>">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <circle cx="9" cy="21" r="1"></circle>
            <circle cx="20" cy="21" r="1"></circle>
            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
          </svg>
          Add to cart
        </button> -->


        <?php else : ?>
        <button class="secondary-out-stock-btn" disabled>
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          Out of Stock
        </button>
        <?php endif; ?>
      </div>

      <!-- Remove from Favorite Action -->
      <div class="secondary-action-item">
        <!-- <button class="secondary-favorite-btn" data-product-id="<?php echo esc_attr($product_id); ?>">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <path
              d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z">
            </path>
          </svg>
          Add to Favorite
        </button> -->

        <?php echo do_shortcode('[yith_wcwl_add_to_wishlist]'); ?>
      </div>
    </div>
  </div>
</div>

<style>
/* WooCommerce Grid Layout Fix */
.woocommerce ul.products {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 20px !important;
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.woocommerce ul.products li.product {
  width: auto !important;
  float: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Responsive Grid */
@media (max-width: 1200px) {
  .woocommerce ul.products {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 16px !important;
  }
}

@media (max-width: 768px) {
  .woocommerce ul.products {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 12px !important;
  }
}

@media (max-width: 480px) {
  .woocommerce ul.products {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }
}

/* Front Page and Shortcode Support */
.woocommerce.columns-1 ul.products,
.woocommerce.columns-2 ul.products,
.woocommerce.columns-3 ul.products,
.woocommerce.columns-4 ul.products,
.woocommerce.columns-5 ul.products,
.woocommerce.columns-6 ul.products {
  display: grid !important;
  gap: 20px !important;
}

.woocommerce.columns-1 ul.products {
  grid-template-columns: 1fr !important;
}

.woocommerce.columns-2 ul.products {
  grid-template-columns: repeat(2, 1fr) !important;
}

.woocommerce.columns-3 ul.products {
  grid-template-columns: repeat(3, 1fr) !important;
}

.woocommerce.columns-4 ul.products {
  grid-template-columns: repeat(4, 1fr) !important;
}

.woocommerce.columns-5 ul.products {
  grid-template-columns: repeat(5, 1fr) !important;
}

.woocommerce.columns-6 ul.products {
  grid-template-columns: repeat(6, 1fr) !important;
}

/* Responsive adjustments for columns */
@media (max-width: 1200px) {

  .woocommerce.columns-4 ul.products,
  .woocommerce.columns-5 ul.products,
  .woocommerce.columns-6 ul.products {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (max-width: 768px) {

  .woocommerce.columns-3 ul.products,
  .woocommerce.columns-4 ul.products,
  .woocommerce.columns-5 ul.products,
  .woocommerce.columns-6 ul.products {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 480px) {

  .woocommerce.columns-2 ul.products,
  .woocommerce.columns-3 ul.products,
  .woocommerce.columns-4 ul.products,
  .woocommerce.columns-5 ul.products,
  .woocommerce.columns-6 ul.products {
    grid-template-columns: 1fr !important;
  }
}

/* Modern Product Card Styles */
.modern-product-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: none;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: none;
  min-height: 400px;
}

/* Ensure content is always visible */
.modern-product-card .product-title,
.modern-product-card .product-price,
.modern-product-card .product-image {
  opacity: 1;
  visibility: visible;
}

/* Handle empty states */
.modern-product-card:empty {
  display: none;
}

.modern-product-card:hover {
  box-shadow: none;
  transform: translateY(-2px);
}

/* Product Image Container */
.product-image-container {
  position: relative;
  background: #ffff;
  padding: 16px;
  padding-bottom: 0px;
  text-align: center;
  min-height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image-link {
  display: block;
  width: 100%;
  height: 100%;
}

.product-image {
  width: 100%;
  max-width: 100%;
  max-height: 186px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.modern-product-card:hover .product-image {
  transform: scale(1.05);
}

/* Product Actions Top */
.product-actions-top {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 2;
}

.compare-btn {
  padding: 5px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: var(--bs-border-width) solid var(--bs-border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: none;
}

.compare-btn:hover {
  background: #ea9c00;
  color: white;
  transform: scale(1.1);
}

.compare-btn svg {
  width: 32px;
  height: 32px;
}

/* Discount Badge */
.discount-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #000;
  color: #fff;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
  z-index: 2;
}

/* Product Details */
.product-details {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-title {
  margin: 0 0 12px 0;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.3;
  height: 2.6em;
  /* Fixed height for exactly 2 lines */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  letter-spacing: -0.01em;
  word-spacing: 0.02em;
}

.product-title a {
  color: #2c3e50;
  text-decoration: none;
  transition: all 0.3s ease;
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-line-clamp: 2 !important;
  line-height: 2;
}

.product-title a:hover {
  color: #ea9c00;
  text-shadow: 0 1px 2px rgba(234, 156, 0, 0.1);
  transform: translateY(-0.5px);
}

.product-title a:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* Enhanced title readability */
.product-title {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Focus state for accessibility */
.product-title a:focus {
  outline: 2px solid #ea9c00;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Product Rating */
.product-rating {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.star-rating {
  display: flex;
  gap: 2px;
}

.star svg {
  width: 16px;
  height: 16px;
}

.star.filled {
  color: #ea9c00;
}

.star.half {
  color: #ea9c00;
}

.star.empty {
  color: #ddd;
}

.star.gray {
  color: #ccc;
}

.star-rating.no-reviews .star {
  color: #ccc;
}

.review-count {
  font-size: 12px;
  color: #666;
}

.review-count.gray {
  color: #999;
}

/* Product Price */
.product-price {
  /* margin-bottom: 16px; */
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

/* Product Actions Bottom */
.product-actions-bottom {
  display: flex;
  gap: 12px;
  margin-top: auto;
  align-items: stretch;
}

/* Button Containers */
.add-to-cart-container {
  flex: 1;
  display: flex;
}

.wishlist-container {
  flex: 1;
  display: flex;
}

.yith-wcwl-icon-svg__wrapper img {
  height: 16px !important;
}

.add-to-cart-btn,
.out-of-stock-btn {
  width: 100%;
  padding: 0px;
  border: none;
  border-radius: 26px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-height: 48px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.add-to-cart-btn {
  background: transparent;
  color: #9C6800;
  box-shadow: none;
  border: none;
}

.add-to-cart-btn:hover {
  background: transparent;
  color: #d68900;
  transform: translateY(-1px);
  box-shadow: none;
}

.add-to-cart-btn:active {
  transform: translateY(0);
}

.out-of-stock-btn {
  background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
  color: #999;
  cursor: not-allowed;
  box-shadow: none;
  border: 2px solid #e9e9e9;
}

.wishlist-btn {
  width: 100%;
  padding: 14px 20px;
  border: none;
  background: transparent;
  border-radius: 26px;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #ea9c00;
  position: relative;
  overflow: hidden;
  min-height: 48px;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-decoration: none;
}

.wishlist-btn:hover {
  background: rgba(234, 156, 0, 0.1);
  color: #d68900;
  transform: translateY(-1px);
  box-shadow: none;
}

.wishlist-btn.active {
  background: rgba(234, 156, 0, 0.2);
  color: #d68900;
}

@keyframes heartBeat {

  0%,
  100% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.1);
  }

  50% {
    transform: scale(1.05);
  }

  75% {
    transform: scale(1.1);
  }
}

.wishlist-btn svg,
.add-to-cart-btn svg,
.out-of-stock-btn svg {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

/* Loading States */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.loading svg {
  /* No spinning animation */
}

.success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-image-container {
    min-height: 160px;
    padding: 15px;
  }

  .product-image {
    max-height: 120px;
  }

  .product-details {
    padding: 15px;
  }

  .product-title {
    font-size: 13px;
    height: 2.3em;
    /* Adjust height for smaller font size */
    line-height: 1.25;
    font-weight: 500;
    letter-spacing: -0.005em;
  }

  .product-title a:hover {
    transform: translateY(-0.3px);
  }

  .current-price {
    font-size: 16px;
  }

  .add-to-cart-btn,
  .out-of-stock-btn {
    padding: 12px 16px;
    font-size: 12px;
    min-width: 0;
    flex-shrink: 1;
  }

  .wishlist-btn {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
  }

  .product-actions-bottom {
    gap: 8px;
  }

  .product-secondary-actions {
    margin-top: 8px;
    gap: 6px;
  }

  .secondary-action-item {
    flex: 1;
  }

  .secondary-add-cart-btn,
  .secondary-out-stock-btn,
  .secondary-favorite-btn {
    padding: 6px 8px;
    font-size: 10px;
    min-height: 28px;
  }
}

/* Secondary Actions Section */
.product-secondary-actions {
  display: flex;
  gap: 8px;
  /* margin-top: 12px; */
  align-items: stretch;
}

.secondary-action-item {
  flex: 1;
  display: flex;
}

.secondary-add-cart-btn,
.secondary-out-stock-btn,
.secondary-favorite-btn {
  width: 100%;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-height: 32px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* Add to Cart Secondary Button */
.secondary-add-cart-btn {
  background: #f8f9fa;
  color: #ea9c00;
  border: 1px solid #ea9c00;
}

.secondary-add-cart-btn:hover {
  background: #ea9c00;
  color: white;
}

/* Out of Stock Secondary Button */
.secondary-out-stock-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
  cursor: not-allowed;
}

/* Remove from Favorite Secondary Button */
.secondary-favorite-btn {
  background: #fff;
  color: #dc3545;
  border: 1px solid #dc3545;
}

.secondary-favorite-btn:hover {
  background: #dc3545;
  color: white;
}

.secondary-add-cart-btn svg,
.secondary-out-stock-btn svg,
.secondary-favorite-btn svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}
</style>

<!-- SVG Definitions for Half Stars -->
<svg style="display: none;">
  <defs>
    <linearGradient id="half-star-gradient">
      <stop offset="50%" stop-color="#ea9c00" />
      <stop offset="50%" stop-color="#ddd" />
    </linearGradient>
  </defs>
</svg>