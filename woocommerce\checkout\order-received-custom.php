<?php
/**
 * "Order received" message.
 *
 * This is a custom version of the WooCommerce order-received.php template
 * with additional error checking.
 *
 * @package TenDeal
 * @version 1.0.0
 *
 * @var WC_Order|false $order
 */

defined('ABSPATH') || exit;
?>

<div class="order-received-message">
    <?php
    /**
     * Filter the message shown after a checkout is complete.
     *
     * @since 2.2.0
     *
     * @param string         $message The message.
     * @param WC_Order|false $order   The order created during checkout, or false if order data is not available.
     */
    $message = apply_filters(
        'woocommerce_thankyou_order_received_text',
        esc_html(__('Thank you. Your order has been received.', 'woocommerce')),
        isset($order) ? $order : false
    );

    // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
    echo $message;
    ?>
</div>
