<?php
/**
 * The template for displaying search results pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#search-result
 *
 * @package tendeal
 */

get_header();

// Check if this is a product search
$is_product_search = (isset($_GET['post_type']) && $_GET['post_type'] === 'product') ||
                     (function_exists('wc_get_page_id') && is_search());

?>

<main id="primary" class="site-main">
  <div class="container">
    <?php if ( have_posts() ) : ?>

    <header class="page-header">
      <h1 class="page-title">
        <?php
                    /* translators: %s: search query. */
                    printf( esc_html__( 'Search Results for: %s', 'tendeal' ), '<span>' . get_search_query() . '</span>' );
                    ?>
      </h1>
      <p class="search-results-count">
        <?php
                    global $wp_query;
                    printf(
                        _n( 'Found %d result', 'Found %d results', $wp_query->found_posts, 'tendeal' ),
                        $wp_query->found_posts
                    );
                    ?>
      </p>
    </header><!-- .page-header -->

    <?php if ($is_product_search) : ?>
    <!-- Product Search Results -->
    <div class="search-results-products">
      <div class="row">
        <?php
                        while ( have_posts() ) :
                            the_post();
                            global $product;

                            // Ensure we have a valid product
                            if (get_post_type() === 'product') {
                                $product = wc_get_product(get_the_ID());
                                if ($product && $product->is_visible()) {
                                    echo '<div class="col-lg-3 col-md-4 col-sm-6 mb-4">';
                                    wc_get_template_part( 'content', 'product' );
                                    echo '</div>';
                                }
                            } else {
                                // Non-product results
                                echo '<div class="col-12 mb-4">';
                                get_template_part( 'template-parts/content', 'search' );
                                echo '</div>';
                            }
                        endwhile;
                        ?>
      </div>
    </div>
    <?php else : ?>
    <!-- Regular Search Results -->
    <div class="search-results-general">
      <?php
                    while ( have_posts() ) :
                        the_post();
                        get_template_part( 'template-parts/content', 'search' );
                    endwhile;
                    ?>
    </div>
    <?php endif; ?>

    <?php
            // Pagination
            the_posts_navigation();
            ?>

    <?php else : ?>

    <header class="page-header">
      <h1 class="page-title">
        <?php
                    /* translators: %s: search query. */
                    printf( esc_html__( 'Search Results for: %s', 'tendeal' ), '<span>' . get_search_query() . '</span>' );
                    ?>
      </h1>
    </header><!-- .page-header -->

    <?php get_template_part( 'template-parts/content', 'none' ); ?>

    <?php endif; ?>
  </div>
</main><!-- #main -->

<?php
get_sidebar();
get_footer();