<?php
/**
 * Template Name: Modern Shop with Filters
 *
 * A modern shop page template with sidebar filters for brands, reviews, price, and demand.
 *
 * @package Teandeal
 */

get_header();

// Enqueue the updated sidebar CSS
wp_enqueue_style('tendeal-shop-sidebar-updated', get_stylesheet_directory_uri() . '/css/shop-sidebar-updated.css', array(), _S_VERSION);
?>

<div class="container shop-container">
  <div class="row">
    <!-- Sidebar with filters -->
    <div class="col-lg-3 shop-sidebar">
      <!-- Brands Section -->
      <div class="brands-section">
        <h4 class="section-title">Brands</h4>
        <div class="brand-logo-grid">
          <?php
          // Check if product_brand taxonomy exists (WooCommerce Brands plugin)
          if (taxonomy_exists('product_brand')) {
            $brands = get_terms(array(
              'taxonomy' => 'product_brand',
              'hide_empty' => true,
              'number' => 12 // Limit to 12 brands for the grid
            ));

            if (!empty($brands) && !is_wp_error($brands)) {
              foreach ($brands as $brand) {
                $brand_logo = '';
                // Get brand thumbnail/logo if available
                $thumbnail_id = get_term_meta($brand->term_id, 'thumbnail_id', true);
                if ($thumbnail_id) {
                  $brand_logo = wp_get_attachment_url($thumbnail_id);
                }
                
                echo '<div class="brand-logo-item" data-brand="' . esc_attr($brand->slug) . '">';
                if ($brand_logo) {
                  echo '<img src="' . esc_url($brand_logo) . '" alt="' . esc_attr($brand->name) . '">';
                } else {
                  // If no logo, display the brand name
                  echo '<span>' . esc_html($brand->name) . '</span>';
                }
                echo '</div>';
              }
            } else {
              echo '<p>No brands found.</p>';
            }
          } else {
            // Fallback to display some sample brand logos
            $sample_brands = array(
              'acer' => 'Acer',
              'asus' => 'Asus',
              'dell' => 'Dell',
              'samsung' => 'Samsung',
              'apple' => 'Apple',
              'sony' => 'Sony',
              'lg' => 'LG Electronics',
              'huawei' => 'Huawei',
              'oppo' => 'Oppo'
            );
            
            foreach ($sample_brands as $slug => $name) {
              $logo_path = get_template_directory_uri() . '/img/brands/' . $slug . '.png';
              echo '<div class="brand-logo-item" data-brand="' . esc_attr($slug) . '">';
              echo '<img src="' . esc_url($logo_path) . '" alt="' . esc_attr($name) . '" onerror="this.onerror=null; this.src=\'' . get_template_directory_uri() . '/img/brands/placeholder.png\';">';
              echo '</div>';
            }
          }
          ?>
        </div>
      </div>

      <!-- Reviews Filter -->
      <div class="reviews-filter">
        <h4 class="section-title">Reviews</h4>
        <ul class="star-rating-filter">
          <?php for ($i = 5; $i >= 1; $i--) : ?>
            <li class="star-rating-item">
              <input type="checkbox" id="rating-<?php echo $i; ?>" name="rating[]" value="<?php echo $i; ?>">
              <label for="rating-<?php echo $i; ?>">
                <div class="stars-container">
                  <?php
                  // Display stars
                  for ($j = 1; $j <= 5; $j++) {
                    if ($j <= $i) {
                      echo '<i class="bi bi-star-fill"></i>';
                    } else {
                      echo '<i class="bi bi-star"></i>';
                    }
                  }
                  ?>
                  <span class="star-rating-count">(<?php echo rand(10, 500); ?>)</span>
                </div>
              </label>
            </li>
          <?php endfor; ?>
        </ul>
      </div>

      <!-- Price Range Filter -->
      <div class="price-range-filter">
        <h4 class="section-title">Price</h4>
        <div class="price-slider-container">
          <div class="price-range-inputs">
            <div class="price-input-group">
              <label class="price-input-label">Min</label>
              <input type="text" class="price-input" id="min-price" placeholder="0">
            </div>
            <div class="price-input-group">
              <label class="price-input-label">Max</label>
              <input type="text" class="price-input" id="max-price" placeholder="2000">
            </div>
          </div>
          <div class="price-slider" id="price-range-slider"></div>
        </div>
      </div>

      <!-- Demand Filter -->
      <div class="demand-filter">
        <h4 class="section-title">Demand</h4>
        <ul class="demand-options">
          <li class="demand-option">
            <input type="radio" id="demand-highest" name="demand" value="highest">
            <label for="demand-highest">Highest in demand</label>
          </li>
          <li class="demand-option">
            <input type="radio" id="demand-lowest" name="demand" value="lowest">
            <label for="demand-lowest">Lowest in demand</label>
          </li>
        </ul>
      </div>

      <!-- Categories (Hidden by default, can be toggled) -->
      <div class="filter-section" style="display: none;">
        <h4 class="filter-title">Categories</h4>
        <div class="category-filter">
          <?php
          $product_categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => true,
            'parent' => 0,
            'exclude' => array(get_option('default_product_cat')) // Exclude "Uncategorized"
          ));

          if (!empty($product_categories) && !is_wp_error($product_categories)) {
            echo '<ul class="category-list">';
            foreach ($product_categories as $category) {
              echo '<li class="category-item">';
              echo '<input type="checkbox" id="category-' . esc_attr($category->slug) . '" name="category[]" value="' . esc_attr($category->slug) . '" class="filter-checkbox">';
              echo '<label for="category-' . esc_attr($category->slug) . '">' . esc_html($category->name) . '</label>';
              echo '</li>';
            }
            echo '</ul>';
          }
          ?>
        </div>
      </div>

      <!-- Color Filter (Hidden by default, can be toggled) -->
      <div class="filter-section" style="display: none;">
        <h4 class="filter-title">Color</h4>
        <div class="color-filter">
          <?php
          // Check if there's a color attribute
          $attribute_taxonomies = wc_get_attribute_taxonomies();
          $color_attribute = false;
          
          // Look for a color attribute
          foreach ($attribute_taxonomies as $tax) {
            if (strpos(strtolower($tax->attribute_name), 'color') !== false || 
                strpos(strtolower($tax->attribute_name), 'colour') !== false) {
              $color_attribute = $tax->attribute_name;
              break;
            }
          }
          
          if ($color_attribute) {
            $taxonomy = 'pa_' . $color_attribute;
            $colors = get_terms(array(
              'taxonomy' => $taxonomy,
              'hide_empty' => true
            ));
            
            if (!empty($colors) && !is_wp_error($colors)) {
              echo '<ul class="color-list">';
              foreach ($colors as $color) {
                // Try to get color value from term meta or use the name
                $color_value = get_term_meta($color->term_id, 'color', true);
                if (!$color_value) {
                  // If no color meta, try to convert name to a CSS color
                  $color_value = $color->name;
                }
                
                echo '<li class="color-item">';
                echo '<input type="checkbox" id="color-' . esc_attr($color->slug) . '" name="color[]" value="' . esc_attr($color->slug) . '" class="filter-checkbox">';
                echo '<label for="color-' . esc_attr($color->slug) . '">';
                echo '<span class="color-swatch" style="background-color: ' . esc_attr($color_value) . ';"></span>';
                echo esc_html($color->name);
                echo '</label>';
                echo '</li>';
              }
              echo '</ul>';
            }
          } else {
            echo '<p>No color attributes found. Please create color attributes for your products.</p>';
          }
          ?>
        </div>
      </div>

      <!-- Apply Button -->
      <button id="apply-filters" class="filter-apply-btn">Apply</button>
    </div>

    <!-- Main content area -->
    <div class="col-lg-9 shop-main-content">
      <div class="shop-header">
        <h1 class="shop-title">Shop</h1>
        <div class="shop-controls">
          <div class="product-count">
            <span id="product-count">Showing all products</span>
          </div>
          <div class="shop-sorting">
            <select id="shop-orderby" class="form-select">
              <option value="menu_order">Default sorting</option>
              <option value="popularity">Sort by popularity</option>
              <option value="rating">Sort by average rating</option>
              <option value="date">Sort by latest</option>
              <option value="price">Sort by price: low to high</option>
              <option value="price-desc">Sort by price: high to low</option>
            </select>
          </div>
          <div class="shop-view-mode">
            <button class="view-mode-btn grid-view active" data-view="grid"><i
                class="bi bi-grid-3x3-gap-fill"></i></button>
            <button class="view-mode-btn list-view" data-view="list"><i class="bi bi-list"></i></button>
          </div>
        </div>
      </div>

      <div id="shop-products" class="products-grid">
        <?php
        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
        $args = array(
          'post_type' => 'product',
          'posts_per_page' => 12,
          'paged' => $paged,
        );
        
        $products_query = new WP_Query($args);
        
        if ($products_query->have_posts()) {
          while ($products_query->have_posts()) {
            $products_query->the_post();
            global $product;
            
            // Make sure we have a valid product object
            if (!is_a($product, 'WC_Product')) {
              continue;
            }
            
            // Get product categories
            $categories = get_the_terms($product->get_id(), 'product_cat');
            $category_classes = '';
            $category_names = array();
            
            if (!empty($categories) && !is_wp_error($categories)) {
              foreach ($categories as $category) {
                $category_classes .= ' category-' . $category->slug;
                $category_names[] = $category->name;
              }
            }
            
            // Get product brands
            $brands = array();
            $brand_classes = '';
            $brand_names = array();
            
            if (taxonomy_exists('product_brand')) {
              $brands = get_the_terms($product->get_id(), 'product_brand');
              
              if (!empty($brands) && !is_wp_error($brands)) {
                foreach ($brands as $brand) {
                  $brand_classes .= ' brand-' . $brand->slug;
                  $brand_names[] = $brand->name;
                }
              }
            }
            
            // Get product colors
            $colors = array();
            $color_classes = '';
            $color_names = array();
            
            if (isset($color_attribute) && $color_attribute) {
              $colors = get_the_terms($product->get_id(), 'pa_' . $color_attribute);
              
              if (!empty($colors) && !is_wp_error($colors)) {
                foreach ($colors as $color) {
                  $color_classes .= ' color-' . $color->slug;
                  $color_names[] = $color->name;
                }
              }
            }
            
            // Get product rating
            $rating = $product->get_average_rating();
            $rating_class = 'rating-' . floor($rating);

            // Calculate discount percentage if on sale
            $regular_price = $product->get_regular_price();
            $sale_price = $product->get_sale_price();
            $discount_percentage = 0;
            
            if ($product->is_on_sale() && $regular_price > 0) {
              $discount_percentage = round(($regular_price - $sale_price) / $regular_price * 100);
            }
            ?>
        <div
          class="product-card<?php echo esc_attr($category_classes . $brand_classes . $color_classes . ' ' . $rating_class); ?>"
          data-price="<?php echo esc_attr($product->get_price()); ?>"
          data-categories="<?php echo esc_attr(implode(',', $category_names)); ?>"
          data-brands="<?php echo esc_attr(implode(',', $brand_names)); ?>"
          data-colors="<?php echo esc_attr(implode(',', $color_names)); ?>"
          data-rating="<?php echo esc_attr(floor($rating)); ?>">
          <div class="product-image">
            <?php if ($product->is_on_sale() && $discount_percentage > 0) : ?>
            <span class="discount-badge">-<?php echo $discount_percentage; ?>%</span>
            <?php endif; ?>
            <a href="<?php the_permalink(); ?>">
              <?php 
                  if (has_post_thumbnail()) {
                    echo woocommerce_get_product_thumbnail();
                  } else {
                    echo '<img src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                  }
                  ?>
            </a>
            <div class="product-actions">
              <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-cart-plus"></i>
              </button>
              <button class="action-btn add-to-wishlist" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-heart"></i>
              </button>
              <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-eye"></i>
              </button>
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-title">
              <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
            </h3>
            <div class="product-rating">
              <?php echo wc_get_rating_html($product->get_average_rating()); ?>
              <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
            </div>
            <div class="product-price">
              <?php echo $product->get_price_html(); ?>
            </div>
            <div class="product-meta">
              <?php if (!empty($brand_names)) : ?>
              <span class="product-brand"><?php echo esc_html($brand_names[0]); ?></span>
              <?php endif; ?>
            </div>
            <div class="product-buttons">
              <a href="<?php echo esc_url($product->add_to_cart_url()); ?>" class="btn-add-to-cart">Add to cart</a>
              <a href="#" class="btn-add-to-favorite" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-heart"></i>
              </a>
            </div>
          </div>
        </div>
        <?php
          }
          
          // Pagination
          echo '<div class="shop-pagination">';
          echo paginate_links(array(
            'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
            'format' => '?paged=%#%',
            'current' => max(1, get_query_var('paged')),
            'total' => $products_query->max_num_pages,
            'prev_text' => '<i class="bi bi-chevron-left"></i>',
            'next_text' => '<i class="bi bi-chevron-right"></i>',
          ));
          echo '</div>';
          
          wp_reset_postdata();
        } else {
          echo '<p class="no-products">No products found. Please check back later.</p>';
        }
        ?>
      </div>
    </div>
  </div>
</div>

<?php
get_footer();
?>
