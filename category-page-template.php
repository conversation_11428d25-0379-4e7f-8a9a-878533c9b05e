<?php
/**
 * Template Name: Category Grid Page
 *
 * A custom template for displaying product categories in a grid with icons.
 *
 * @package Tendeal
 */

get_header();
?>

<main id="primary" class="site-main">
  <div class="container">
    <!-- Breadcrumb -->
    <div class="breadcrumb-container py-3">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb m-0">
          <li class="breadcrumb-item"><a href="<?php echo esc_url(home_url('/')); ?>">Home</a></li>
          <li class="breadcrumb-item active" aria-current="page">Categories</li>
        </ol>
      </nav>
    </div>

    <!-- Categories Header -->
    <div class="categories-header text-center my-4">
      <h1 class="categories-title">Categories</h1>
    </div>

    <!-- Categories Grid -->
    <div class="categories-grid">
      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
        <?php
        // Define icon mapping for common category names
        $icon_mapping = array(
          'women' => 'dress-icon.svg',
          'woman' => 'dress-icon.svg',
          'fashion' => 'dress-icon.svg',
          'clothing' => 'shirt-icon.svg',
          'men' => 'shirt-icon.svg',
          'man' => 'shirt-icon.svg',
          'phone' => 'phone-icon.svg',
          'mobile' => 'phone-icon.svg',
          'communication' => 'phone-icon.svg',
          'computer' => 'computer-icon.svg',
          'laptop' => 'computer-icon.svg',
          'desktop' => 'computer-icon.svg',
          'office' => 'computer-icon.svg',
          'pet' => 'pet-icon.svg',
          'dog' => 'pet-icon.svg',
          'cat' => 'pet-icon.svg',
          'animal' => 'pet-icon.svg',
          'home' => 'home-icon.svg',
          'house' => 'home-icon.svg',
          'furniture' => 'home-icon.svg',
          'decor' => 'home-icon.svg',
          'jewelry' => 'jewelry-icon.svg',
          'watch' => 'jewelry-icon.svg',
          'electronic' => 'electronics-icon.svg',
          'gadget' => 'electronics-icon.svg',
          'beauty' => 'beauty-icon.svg',
          'health' => 'beauty-icon.svg',
          'cosmetic' => 'beauty-icon.svg',
          'sport' => 'sports-icon.svg',
          'fitness' => 'sports-icon.svg',
          'outdoor' => 'sports-icon.svg',
          'toy' => 'toys-icon.svg',
          'kid' => 'toys-icon.svg',
          'child' => 'toys-icon.svg',
          'baby' => 'toys-icon.svg',
          'bag' => 'bags-icon.svg',
          'shoe' => 'bags-icon.svg',
          'footwear' => 'bags-icon.svg',
          'accessory' => 'bags-icon.svg',
          'bank' => 'banking-icon.svg',
          'finance' => 'finance-icon.svg',
          'money' => 'finance-icon.svg',
          'agriculture' => 'agriculture-icon.svg',
          'food' => 'agriculture-icon.svg',
          'grocery' => 'agriculture-icon.svg',
          'tool' => 'tools-icon.svg',
          'diy' => 'tools-icon.svg',
          'car' => 'cars-icon.svg',
          'auto' => 'cars-icon.svg',
          'motorcycle' => 'cars-icon.svg',
          'vehicle' => 'cars-icon.svg',
          'business' => 'business-icon.svg',
          'economy' => 'economics-icon.svg',
          'economic' => 'economics-icon.svg'
        );

        // Get all product categories
        $product_categories = get_terms(array(
          'taxonomy' => 'product_cat',
          'hide_empty' => false,
          'parent' => 0, // Only get top-level categories
          'exclude' => array(get_option('default_product_cat')) // Exclude "Uncategorized"
        ));

        // Check if we have categories
        if (!empty($product_categories) && !is_wp_error($product_categories)) {
          foreach ($product_categories as $category) {
            // Get category URL
            $category_url = get_term_link($category);

            // Get category thumbnail if available
            $thumbnail_id = get_term_meta($category->term_id, 'thumbnail_id', true);
            $has_thumbnail = false;

            if ($thumbnail_id) {
              $thumbnail = wp_get_attachment_url($thumbnail_id);
              if ($thumbnail) {
                $has_thumbnail = true;
              }
            }

            // Determine icon based on category name
            $icon_filename = 'default-icon.svg';
            $category_name_lower = strtolower($category->name);

            foreach ($icon_mapping as $keyword => $icon) {
              if (strpos($category_name_lower, $keyword) !== false) {
                $icon_filename = $icon;
                break;
              }
            }

            // Icon path
            $icon_path = get_template_directory_uri() . '/img/category-icons/' . $icon_filename;

            // Fallback to a default icon
            $default_icon = get_template_directory_uri() . '/img/category-icons/default-icon.svg';

            // Get product count
            $product_count = $category->count;
        ?>
        <div class="col">
          <a href="<?php echo esc_url($category_url); ?>" class="category-card">
            <div class="category-icon">
              <?php if ($has_thumbnail) : ?>
              <img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr($category->name); ?>"
                class="category-thumbnail">
              <?php else : ?>
              <img src="<?php echo esc_url($icon_path); ?>" alt="<?php echo esc_attr($category->name); ?>"
                onerror="this.onerror=null; this.src='<?php echo esc_url($default_icon); ?>';">
              <?php endif; ?>
            </div>
            <h3 class="category-name"><?php echo esc_html($category->name); ?></h3>
            <?php if ($product_count > 0) : ?>
            <span
              class="product-count"><?php echo sprintf(_n('%s product', '%s products', $product_count, 'tendeal'), number_format_i18n($product_count)); ?></span>
            <?php endif; ?>
          </a>
        </div>
        <?php
          }
        } else {
          // Fallback if no categories are found
          echo '<div class="col-12 text-center"><p>No product categories found.</p></div>';
        }
        ?>
      </div>
    </div>
  </div>
</main>

<?php
get_footer();
?>