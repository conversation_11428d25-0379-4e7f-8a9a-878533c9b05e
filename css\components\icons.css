/* 
 * Feather Icons Styles
 * https://feathericons.com/
 */

/* Base icon styles */
.feather {
  width: 24px;
  height: 24px;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

/* Icon sizes */
.feather-sm {
  width: 16px;
  height: 16px;
}

.feather-md {
  width: 24px;
  height: 24px;
}

.feather-lg {
  width: 32px;
  height: 32px;
}

.feather-xl {
  width: 48px;
  height: 48px;
}

/* Icon colors - using theme variables */
.feather-primary {
  stroke: var(--bs-primary);
}

.feather-secondary {
  stroke: var(--bs-secondary);
}

.feather-success {
  stroke: var(--bs-success);
}

.feather-danger {
  stroke: var(--bs-danger);
}

.feather-warning {
  stroke: var(--bs-warning);
}

.feather-info {
  stroke: var(--bs-info);
}

.feather-light {
  stroke: var(--bs-light);
}

.feather-dark {
  stroke: var(--bs-dark);
}

.feather-white {
  stroke: var(--bs-white);
}

/* Icon with background */
.icon-bg {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--bs-icon-background);
}

.icon-bg-sm {
  width: 32px;
  height: 32px;
}

.icon-bg-lg {
  width: 64px;
  height: 64px;
}

/* Icon with text */
.icon-with-text {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

/* Icon animations */
.feather-spin {
  animation: feather-spin 2s linear infinite;
}

@keyframes feather-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.feather-pulse {
  animation: feather-pulse 1.5s ease-in-out infinite;
}

@keyframes feather-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* App bar specific icon styles */
.app-bar .feather {
  stroke-width: 1.5;
  margin-right: 0.25rem;
  vertical-align: text-bottom;
}

.app-bar__inner .feather {
  color: var(--bs-primary);
}

/* Button with icon */
button .feather,
.btn .feather {
  margin-right: 0.25rem;
  vertical-align: text-bottom;
}

/* Icon only button */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border-radius: 50%;
}

.btn-icon-sm {
  width: 2rem;
  height: 2rem;
}

.btn-icon-lg {
  width: 3rem;
  height: 3rem;
}

/* Icon in form controls */
.form-control-icon {
  position: relative;
}

.form-control-icon .feather {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-control-icon-left .feather {
  left: 0.75rem;
}

.form-control-icon-left input {
  padding-left: 2.5rem;
}

.form-control-icon-right .feather {
  right: 0.75rem;
}

.form-control-icon-right input {
  padding-right: 2.5rem;
}

/* Icon in navigation */
.nav-link .feather {
  margin-right: 0.5rem;
}

/* Icon in cards */
.card-header .feather {
  margin-right: 0.5rem;
}

/* Icon in alerts */
.alert .feather {
  margin-right: 0.5rem;
  vertical-align: text-bottom;
}

/* Icon in list groups */
.list-group-item .feather {
  margin-right: 0.5rem;
}
