<?php
/**
 * Empty cart page
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/cart-empty.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 7.0.1
 */

defined( 'ABSPATH' ) || exit;

// Remove the default empty cart message hook
remove_action( 'woocommerce_cart_is_empty', 'wc_empty_cart_message', 10 );
?>

<div class="cart-container container">

  <div class="col-12 col-lg-8">
    <div class="empty-cart-content">

      <div class="empty-cart-message">
        <h2 class="empty-cart-title"><?php esc_html_e( 'Sorry.', 'tendeal' ); ?></h2>
        <p class="empty-cart-subtitle">
          <?php esc_html_e( 'There are no products in the cart currently. Go to the home page and choose what suits you.', 'tendeal' ); ?>
        </p>
      </div>


      <div class="empty-cart-actions">
        <?php if ( wc_get_page_id( 'shop' ) > 0 ) : ?>
        <a href="<?php echo esc_url( apply_filters( 'woocommerce_return_to_shop_redirect', wc_get_page_permalink( 'shop' ) ) ); ?>"
          class="btn btn-warning empty-cart-btn-primary">
          <?php echo esc_html( apply_filters( 'woocommerce_return_to_shop_text', __( 'Go home page', 'tendeal' ) ) ); ?>
        </a>
        <?php endif; ?>
      </div>
    </div>
  </div>
  <aside class="cart-sidebar mt-4">


    <!-- Coupon Box -->
    <div class="cart-coupon">
      <span>Coupon Code</span>
      <input type="text" id="coupon_code" placeholder="Enter coupon code">
      <button type="button" id="apply_coupon" class="button">Apply Coupon</button>
      <p id="coupon_message" class="coupon-message"></p>
    </div>

    <!-- Simplified Cart Totals -->
    <div class="cart-simple-totals">
      <h4><?php esc_html_e( 'Order Summary', 'tendeal' ); ?></h4>
      <div class="totals-content">
        <?php
        // Get cart totals
        $cart = WC()->cart;
        $subtotal = $cart->get_subtotal();
        $shipping_total = $cart->get_shipping_total();
        $discount_total = $cart->get_discount_total();
        $applied_coupons = $cart->get_applied_coupons();

        // Get the actual total from WooCommerce (this is the final calculated amount)
        $cart_total_raw = $cart->get_total( 'raw' ); // Get as float, not formatted string
        ?>

        <!-- Subtotal -->
        <div class="total-row subtotal">
          <span class="total-label">

            <?php esc_html_e( 'Subtotal', 'tendeal' ); ?>
          </span>
          <span class="total-value">
            -
          </span>
        </div>

        <!-- Discount (only show if coupons are applied) -->
        <?php if ( !empty( $applied_coupons ) && $discount_total > 0 ) : ?>
        <div class="total-row discount-total">
          <span class="total-label">

            <?php esc_html_e( 'Discount', 'tendeal' ); ?>
            <?php if ( count( $applied_coupons ) === 1 ) : ?>
            <small>(<?php echo esc_html( $applied_coupons[0] ); ?>)</small>
            <?php endif; ?>
          </span>
          <span class="total-value discount-value">
            -<?php echo wp_kses_post( wc_price( $discount_total ) ); ?>
          </span>
        </div>
        <?php endif; ?>

        <!-- Shipping -->
        <?php if ( $cart->needs_shipping() && $cart->show_shipping() ) : ?>
        <div class="total-row shipping-total">
          <span class="total-label">

            <?php esc_html_e( 'Shipping', 'tendeal' ); ?>
          </span>
          <span class="total-value">
            <?php
              if ( $shipping_total > 0 ) {
                echo wp_kses_post( wc_price( $shipping_total ) );
              } else {
                esc_html_e( 'Free', 'tendeal' );
              }
              ?>
          </span>
        </div>
        <?php endif; ?>

        <!-- Total -->
        <div class="total-row order-total">
          <span class="total-label">

            <?php esc_html_e( 'Total', 'tendeal' ); ?>
          </span>
          <span class="total-value">
            -
          </span>
        </div>

        <div class="checkout-button-wrapper">
          <a href="#" class="btn-checkout">
            <i data-feather="credit-card"></i>
            <?php esc_html_e( 'Proceed to Checkout', 'tendeal' ); ?>
          </a>
        </div>
      </div>
    </div>


    <div class="payment-methods">
      <h2><?php esc_html_e( 'Payment Methods', 'woocommerce' ); ?></h2>
      <p><?php esc_html_e( 'Accepted payment methods will be displayed during checkout.', 'woocommerce' ); ?></p>
      <?php
    
    ?>
    </div>


  </aside>




  <!-- <div class="row">
    
    <div class="col-12 col-lg-8">
      <div class="empty-cart-content">
        
        <div class="empty-cart-message">
          <h2 class="empty-cart-title"><?php esc_html_e( 'Sorry.', 'tendeal' ); ?></h2>
          <p class="empty-cart-subtitle">
            <?php esc_html_e( 'There are no products in the cart currently. Go to the home page and choose what suits you.', 'tendeal' ); ?>
          </p>
        </div>

       
        <div class="empty-cart-actions">
          <?php if ( wc_get_page_id( 'shop' ) > 0 ) : ?>
            <a href="<?php echo esc_url( apply_filters( 'woocommerce_return_to_shop_redirect', wc_get_page_permalink( 'shop' ) ) ); ?>"
               class="btn btn-warning empty-cart-btn-primary">
              <?php echo esc_html( apply_filters( 'woocommerce_return_to_shop_text', __( 'Go home page', 'tendeal' ) ) ); ?>
            </a>
          <?php endif; ?>
        </div>
      </div>
    </div> -->





</div>