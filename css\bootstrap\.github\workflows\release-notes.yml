name: Release notes

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: read

jobs:
  update_release_draft:
    permissions:
      # allow release-drafter/release-drafter to create GitHub releases and add labels to PRs
      contents: write
      pull-requests: write
    runs-on: ubuntu-latest
    if: github.repository == 'twbs/bootstrap'
    steps:
      - uses: release-drafter/release-drafter@v6
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
