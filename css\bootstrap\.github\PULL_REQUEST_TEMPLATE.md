### Description

<!-- Describe your changes in detail -->

### Motivation & Context

<!-- Why is this change required? What problem does it solve? -->

### Type of changes

<!-- What types of changes does your code introduce? Put an `x` in all the boxes that apply. -->

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Refactoring (non-breaking change)
- [ ] Breaking change (fix or feature that would change existing functionality)

### Checklist

<!-- Go over all the following points, and put an `x` in all the boxes that apply. -->
<!-- If you're unsure about any of these, don't hesitate to ask. We're here to help! -->

- [ ] I have read the [contributing guidelines](https://github.com/twbs/bootstrap/blob/main/.github/CONTRIBUTING.md)
- [ ] My code follows the code style of the project _(using `npm run lint`)_
- [ ] My change introduces changes to the documentation
- [ ] I have updated the documentation accordingly
- [ ] I have added tests to cover my changes
- [ ] All new and existing tests passed

#### Live previews

<!-- Please add direct links where your modifications can be seen in the documentation -->

- <https://deploy-preview-{your_pr_number}--twbs-bootstrap.netlify.app/>

### Related issues

<!-- Please link any related issues here. -->
