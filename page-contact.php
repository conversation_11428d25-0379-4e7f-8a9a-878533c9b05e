<?php
/**
 * Template Name: Contact Page
 * 
 * A custom template for the Contact Us page.
 *
 * @package tendeal
 */

get_header();
?>

<main id="primary" class="site-main bg-white">
  <div class="container mx-auto p-4">
    <div class="breadcrumb-container contact-form-container py-3">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb m-0">
          <li class="breadcrumb-item"><a href="<?php echo esc_url(home_url('/')); ?>">Home</a></li>
          <li class="breadcrumb-item active" aria-current="page">Contact us</li>
        </ol>
      </nav>
    </div>

    <h1 class="contact-form-container text-3xl font-bold mb-6">Contact us</h1>

    <div class="contact-form-container  p-6 rounded ">
      <form id="contact-form" class="contact-form">
        <div class="form-group mb-4">
          <label for="full-name" class="block mb-2">Full name</label>
          <input type="text" id="full-name" name="full_name" class="form-control w-full p-2 border rounded"
            placeholder="Full name" required>
        </div>

        <div class="form-group mb-4">
          <label for="email-address" class="block mb-2">Email address</label>
          <input type="email" id="email-address" name="email" class="form-control w-full p-2 border rounded"
            placeholder="Email address" required>
        </div>

        <div class="form-group mb-4">
          <label for="subject" class="block mb-2">Subject</label>
          <input type="text" id="subject" name="subject" class="form-control w-full p-2 border rounded"
            placeholder="Subject..." required>
        </div>

        <div class="form-group mb-4">
          <label for="message" class="block mb-2">Message</label>
          <textarea id="message" name="message" class="form-control w-full p-2 border rounded" rows="5"
            placeholder="Your message..." required></textarea>
        </div>

        <div class="form-group">
          <button type="submit" id="send-button"
            class="btn bg-amber-500 hover:bg-amber-600 text-white font-bold py-2 px-6 rounded">Send</button>
        </div>

        <div id="form-message" class="mt-4 hidden"></div>
      </form>
    </div>
  </div>
</main>

<script>
jQuery(document).ready(function($) {
  $('#contact-form').on('submit', function(e) {
    e.preventDefault();

    const formData = $(this).serialize();
    const formMessage = $('#form-message');
    const sendButton = $('#send-button');

    // Disable button and show loading state
    sendButton.prop('disabled', true).text('Sending...');

    $.ajax({
      type: 'POST',
      url: '<?php echo admin_url('admin-ajax.php'); ?>',
      data: formData + '&action=submit_contact_form',
      success: function(response) {
        if (response.success) {
          formMessage.removeClass('hidden bg-red-100 text-red-700').addClass(
            'bg-green-100 text-green-700 p-3 rounded');
          formMessage.html('Thank you for your message. We will get back to you soon.');
          $('#contact-form')[0].reset();
        } else {
          formMessage.removeClass('hidden bg-green-100 text-green-700').addClass(
            'bg-red-100 text-red-700 p-3 rounded');
          formMessage.html('There was an error sending your message. Please try again.');
        }
      },
      error: function() {
        formMessage.removeClass('hidden bg-green-100 text-green-700').addClass(
          'bg-red-100 text-red-700 p-3 rounded');
        formMessage.html('There was an error sending your message. Please try again.');
      },
      complete: function() {
        // Re-enable button
        sendButton.prop('disabled', false).text('Send');
      }
    });
  });
});
</script>

<?php
get_footer();