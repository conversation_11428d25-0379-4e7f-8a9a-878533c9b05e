/* Clean Shop Header Design */

/* Shop Header Container */
.shop-header-clean {
    background: white;
    padding: 0;
    margin-bottom: 2rem;
}

/* Breadcrumb Navigation */
.shop-breadcrumb {
    padding: 1rem 0 0.5rem 0;
    /* border-bottom: 1px solid #e9ecef; */
    /* margin-bottom: 1.5rem; */
}

.shop-breadcrumb .breadcrumb {
    margin: 0;
    padding: 0;
    background: transparent;
    font-size: 0.9rem;
    font-weight: 500;
}

.shop-breadcrumb .breadcrumb-item {
    color: #6c757d;
}

.shop-breadcrumb .breadcrumb-item+.breadcrumb-item::before {
    content: ">";
    color: #6c757d;
    padding: 0 0.5rem;
}

.shop-breadcrumb .breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s;
}

.shop-breadcrumb .breadcrumb-item a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.shop-breadcrumb .breadcrumb-item.active {
    color: #C38200;
    font-weight: 500;
}

/* Header Row */
.shop-header-row {
    display: contents;
    justify-content: space-between;
    align-items: center;
    /* padding: 0 0 1.5rem 0; */
    /* border-bottom: 1px solid #e9ecef; */
    /* margin-bottom: 2rem; */
}

/* Page Title */
.shop-title-area {
    flex: 1;
}

.shop-page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #1D2939;
    margin: 0;
    line-height: 1.2;
}

.shop-page-title .search-term {
    color: #ea9c00;
}

/* Header Controls */
.shop-header-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    justify-content: space-between;
}

/* View Mode Selector */
.view-mode-selector {
    padding-top: 10px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.view-buttons {
    display: flex;
    /* border: 1px solid #ddd; */
    /* border-radius: 4px; */
    overflow: hidden;
}

.view-btn {
    background: white;
    border: none;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 1rem;
}

.view-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.view-btn.active {
    /* background: #ffff; */
    color: #EA9C00;
}

/* .view-btn+.view-btn {
    border-left: 1px solid #ddd;
} */

/* Sort Dropdown */
.sort-dropdown {
    position: relative;
}

.sort-dropdown select {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    font-size: 0.9rem;
    color: #333;
    cursor: pointer;
    min-width: 140px;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
}

.sort-dropdown select:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Mobile Filter Button */
.mobile-filter-btn {
    background: #C38200;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.mobile-filter-btn:hover {
    background: #d08a00;
    color: white;
}

.mobile-filter-btn i {
    font-size: 1rem;
}

.filter-badge {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    position: absolute;
    top: -5px;
    right: -5px;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .shop-header-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .shop-title-area {
        text-align: center;
    }

    .shop-page-title {
        font-size: 1.75rem;
    }

    .shop-header-controls {
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .view-mode-selector {
        order: 1;
    }

    .sort-dropdown {
        order: 2;
        flex: 1;
    }

    .sort-dropdown select {
        width: 100%;
        min-width: auto;
    }

    .mobile-filter-btn {
        order: 0;
        justify-content: center;
        width: 100%;
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 767.98px) {
    .shop-breadcrumb {
        padding: 0.75rem 0 0.5rem 0;
    }

    .shop-breadcrumb .breadcrumb {
        font-size: 0.8rem;
    }

    .shop-header-row {
        padding: 0 0 1rem 0;
    }

    .shop-page-title {
        font-size: 1.5rem;
    }

    .shop-header-controls {
        flex-direction: column;
        gap: 0.75rem;
    }

    .view-mode-selector {
        display: none;
        justify-content: center;
    }

    .mobile-filter-btn {
        font-size: 1rem;
        padding: 1rem;
    }
}

/* Integration with existing styles */
.shop-header-clean .breadcrumb-item a {
    color: #475467;
}

.shop-header-clean .breadcrumb-item.active {
    color: #C38200;
}

/* Hide old header styles when using clean header */
.shop-header-clean~.shop-header {
    display: none;
}

/* Ensure proper spacing */
.shop-header-clean+#shop-loading {
    margin-top: 0;
}

.shop-header-clean+#shop-products {
    margin-top: 0;
}

/* Clean header specific adjustments */
.shop-main-content .shop-header-clean {
    margin: -1rem -1rem 0rem -1rem;
    padding: 1rem;
    /* background: #f8f9fa; */
    /* border-bottom: 1px solid #e9ecef; */
}

@media (max-width: 767.98px) {
    .shop-main-content .shop-header-clean {
        margin: -0.75rem -0.75rem 1.5rem -0.75rem;
        padding: 0.75rem;
    }
}