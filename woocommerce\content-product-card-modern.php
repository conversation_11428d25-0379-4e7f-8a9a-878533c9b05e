<?php
/**
 * The template for displaying product content within modern cards
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product-card-modern.php.
 *
 * @package tendeal
 */

defined('ABSPATH') || exit;

global $product;

// Ensure visibility
if (empty($product) || !$product->is_visible()) {
    return;
}

// Get product data
$product_id = $product->get_id();
$product_name = $product->get_name();
$regular_price = $product->get_regular_price();
$sale_price = $product->get_sale_price();
$average_rating = $product->get_average_rating();
$review_count = $product->get_review_count();
$product_link = get_permalink($product_id);
$image_id = $product->get_image_id();
$image_url = wp_get_attachment_image_url($image_id, 'medium');
$short_description = $product->get_short_description();

// Limit short description to a specific length
$short_desc_excerpt = wp_trim_words($short_description, 10, '...');

// Calculate discount percentage if on sale
$discount_percentage = 0;
if ($product->is_on_sale() && $regular_price > 0) {
    $discount_percentage = round(($regular_price - $sale_price) / $regular_price * 100);
}

// Check if product is in wishlist
$in_wishlist = false;
if (function_exists('YITH_WCWL')) {
    $in_wishlist = YITH_WCWL()->is_product_in_wishlist($product_id);
}
?>

<div class="product-card-modern">
    <div class="card-actions">
        <button class="btn-refresh" aria-label="Refresh">
            <i class="bi bi-arrow-repeat"></i>
        </button>
        
        <?php if ($discount_percentage > 0) : ?>
            <div class="discount-badge">-<?php echo esc_html($discount_percentage); ?>%</div>
        <?php endif; ?>
    </div>
    
    <a href="<?php echo esc_url($product_link); ?>" class="product-image">
        <?php if ($image_url) : ?>
            <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product_name); ?>">
        <?php else : ?>
            <img src="<?php echo esc_url(wc_placeholder_img_src()); ?>" alt="<?php echo esc_attr($product_name); ?>">
        <?php endif; ?>
    </a>
    
    <div class="product-details">
        <h3 class="product-title">
            <a href="<?php echo esc_url($product_link); ?>"><?php echo esc_html($product_name); ?></a>
        </h3>
        
        <?php if ($short_description) : ?>
            <p class="product-description"><?php echo esc_html($short_desc_excerpt); ?></p>
        <?php endif; ?>
        
        <div class="product-rating">
            <div class="star-rating">
                <?php for ($i = 1; $i <= 5; $i++) : ?>
                    <?php if ($i <= $average_rating) : ?>
                        <i class="bi bi-star-fill"></i>
                    <?php elseif ($i - 0.5 <= $average_rating) : ?>
                        <i class="bi bi-star-half"></i>
                    <?php else : ?>
                        <i class="bi bi-star"></i>
                    <?php endif; ?>
                <?php endfor; ?>
            </div>
            <span class="review-count"><?php echo esc_html($review_count); ?> Reviews</span>
        </div>
        
        <div class="product-price">
            <?php if ($sale_price) : ?>
                <span class="current-price"><?php echo wc_price($sale_price); ?></span>
                <span class="regular-price"><?php echo wc_price($regular_price); ?></span>
            <?php else : ?>
                <span class="current-price"><?php echo wc_price($regular_price); ?></span>
            <?php endif; ?>
        </div>
        
        <div class="product-actions">
            <a href="<?php echo esc_url($product->add_to_cart_url()); ?>" class="btn-add-to-cart" data-product_id="<?php echo esc_attr($product_id); ?>">
                <i class="bi bi-cart-plus"></i> Add to cart
            </a>
            
            <?php if (function_exists('YITH_WCWL')) : ?>
                <button class="btn-toggle-wishlist <?php echo $in_wishlist ? 'in-wishlist' : ''; ?>" data-product_id="<?php echo esc_attr($product_id); ?>">
                    <?php if ($in_wishlist) : ?>
                        <i class="bi bi-x-circle"></i> Remove from favorite
                    <?php else : ?>
                        <i class="bi bi-heart"></i> Add to favorite
                    <?php endif; ?>
                </button>
            <?php endif; ?>
        </div>
    </div>
</div>
