/* Contact Page Styles */

.contact-form-container {
  max-width: 800px;
  margin: 0 auto;
  border-radius: 8px;
}

.contact-form label {
  font-weight: 500;
  color: #333;
}

.contact-form input,
.contact-form textarea {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 10px 12px;
  width: 100%;
  transition: border-color 0.3s;
}

.contact-form input:focus,
.contact-form textarea:focus {
  border-color: #f59e0b;
  outline: none;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.contact-form button {
  background-color: #f59e0b;
  color: white;
  font-weight: bold;
  padding: 10px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.contact-form button:hover {
  background-color: #d97706;
}

.contact-form button:disabled {
  background-color: #f59e0b;
  opacity: 0.7;
  cursor: not-allowed;
}

#form-message {
  padding: 12px;
  border-radius: 4px;
  margin-top: 16px;
}

#form-message.success {
  background-color: #ecfdf5;
  color: #047857;
}

#form-message.error {
  background-color: #fef2f2;
  color: #b91c1c;
}
