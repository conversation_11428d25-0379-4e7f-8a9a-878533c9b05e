<?php
/**
 * Template Name: Custum page template
 */

?>













<?php get_header();?>



<main class="container">

  <div class="row mt-4 mb-4 bg-white top-ads">


    <div class="right-banned col-8">
      <div class="col" style="    position: relative;">
        <img src="<?php echo get_template_directory_uri();?>/img/img10.png " alt="">
        <div class="top-left col">
          <h2>The latest electronic at affordable prices</h2>
          <h6>Discover the latest offers every week. And with huge discounts.</h6>
          <button type="button" class="btn btn-primary text-white font-medium  mt-4 mb-6">shop now</button>
        </div>
      </div>
    </div>

    <div class="left-banner col-4">

      <div class="row">
        <div class="col-12 mb-4" style="    position: relative;">
          <img src="<?php echo get_template_directory_uri();?>/img/img6.png " alt="">

          <div class="top-left col">
            <span class="text-primary font-medium ">Best Offer</span>
            <h5>Powerfull Apple watches for you</h5>
            <button type="button" class="btn btn-primary text-white font-medium  mt-4 mb-6">shop now</button>
          </div>
        </div>

        <div class="col-12" style="    position: relative;">
          <img src="<?php echo get_template_directory_uri();?>/img/img11.png " alt="">
          <div class="top-left col">
            <span class="text-primary font-medium ">Coupons</span>
            <h5>Latest technologies with low prices</h5>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-12">
    <span>home > Featured Products</span>
    <h1 class="mt-4 mb-4 " style="font-size:60px ">Featured Products</h1>
    <ul class="featured-list mb-4 mt-2" id="featured-tabs">
      <li class="featured-tab selected-ops" data-tab="featured">Featured recommended products</li>
      <li class="featured-tab" data-tab="discounted">Discounts starting from 50%</li>
      <li class="featured-tab" data-tab="upcoming">Upcoming products</li>
      <li class="featured-tab" data-tab="popular">Popular products</li>
    </ul>

    <!-- Category Navigation -->
    <div class="category-navigation mb-4" id="category-navigation">
      <div class="category-scroll-container">
        <ul class="category-list" id="category-list">
          <?php
          // Get initial categories for featured products
          $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => true,
            'number' => 10,
            'exclude' => array(get_option('default_product_cat'))
          ));

          if (!empty($categories) && !is_wp_error($categories)) {
            echo '<li class="category-item active" data-category="all">All</li>';
            foreach ($categories as $category) {
              echo '<li class="category-item" data-category="' . esc_attr($category->slug) . '">' . esc_html($category->name) . '</li>';
            }
          }
          ?>
        </ul>
      </div>
    </div>
  </div>

  <div class="row bg-white">
    <div class="main-content__offers__bottom row ">
      <div class="col-4 main-content__offers__bottom__left">
        <div class="row " style="    position: relative;">
          <img src="<?php echo get_template_directory_uri();?>/img/img4.png" alt="Snow" style="width:100%;">
          <div class="top-left col">

            <span class="text-primary font-medium">Laest products</span>
            <h5 class="text-dark">Featured products now</h5>
            <button type="button" class="btn btn-primary text-white font-medium  mt-4 mb-6">Shop now</button>



          </div>

          <div class="col"></div>



        </div>

      </div>
      <div class="col-md-8 main-content__offers__bottom__right">
        <div class="row " style="    position: relative;">
          <img src="<?php echo get_template_directory_uri();?>/img/img5.png" alt="Snow" style="width:90%;">
          <div class="top-left col">

            <span class="text-primary font-medium">Best Offers</span>
            <h5 class="text-dark">Best gaming offers for you</h5>
            <button type="button" class="btn btn-primary text-white font-medium  mt-4 mb-6">Shop now</button>



          </div>

          <div class="col"></div>



        </div>

      </div>


    </div>

    <!-- Dynamic Products Container -->
    <div class="row mt-4 mb-4">
      <div class="col-12">
        <div id="featured-products-loading" class="text-center" style="display: none;">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <p class="mt-2">Loading products...</p>
        </div>

        <div id="featured-products-container">
          <h1 class="mt-2 mb-4" id="products-section-title">Featured Products</h1>
          <div id="products-grid" class="row">
            <?php
            // Load initial featured products
            $args = array(
              'post_type' => 'product',
              'posts_per_page' => 8,
              'meta_key' => '_featured',
              'meta_value' => 'yes',
            );
            $featured_products = new WP_Query($args);

            if ($featured_products->have_posts()) {
              while ($featured_products->have_posts()) {
                $featured_products->the_post();
                global $product;

                if (!is_a($product, 'WC_Product')) {
                  continue;
                }

                // Use WooCommerce template or custom product card
                echo '<div class="col-lg-3 col-md-4 col-sm-6 mb-4">';
                wc_get_template_part('content', 'product');
                echo '</div>';
              }
              wp_reset_postdata();
            } else {
              echo '<div class="col-12"><p class="text-center">No featured products found.</p></div>';
            }
            ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>

<?php get_footer();?>