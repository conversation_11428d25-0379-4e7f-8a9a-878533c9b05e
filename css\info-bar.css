/* Info Bar Styling */

.info-bar {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 8px 0;
}

.info-bar__list {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: flex;
  align-items: center;
  gap: 20px;
}

.info-bar__list li {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
  transition: color 0.2s ease;
}

.info-bar__list li a {
  display: flex;
  align-items: center;
  color: #555;
  text-decoration: none;
  transition: color 0.2s ease;
}

.info-bar__list li a:hover {
  color: #ea9c00;
}

.info-bar__list li .bi {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 6px;
  font-size: 16px;
  color: #ea9c00;
}

.info-bar__list select {
  border-radius: 20px;
  font-size: 14px;
  padding: 4px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  color: #555;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.info-bar__list select:focus {
  outline: none;
  border-color: #ea9c00;
  box-shadow: 0 0 0 2px rgba(234, 156, 0, 0.2);
}

/* Polylang language switcher */
.info-bar__list .language-switcher select {
  padding-right: 30px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23555' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 12px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Right side info bar */
.info-bar .text-md-end .info-bar__list {
  justify-content: flex-end;
}

.info-bar .text-md-end .info-bar__list li {
  font-weight: 500;
}

/* Phone number styling */
.info-bar .text-md-end .info-bar__list li a {
  font-weight: 600;
  color: #ea9c00;
}

.info-bar .text-md-end .info-bar__list li a:hover {
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 768px) {
  .info-bar__list {
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }

  .info-bar .text-md-end .info-bar__list {
    justify-content: center;
    margin-top: 10px;
  }

  .info-bar__column {
    flex-wrap: wrap;
  }
}

@media (max-width: 576px) {
  .info-bar__list {
    gap: 10px;
  }

  .info-bar__list li {
    font-size: 16px;
  }

  .info-bar__list select {
    font-size: 16px;
    padding: 3px 10px;
  }
}