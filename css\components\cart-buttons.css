/* 
 * <PERSON><PERSON> Styles with Feather Icons
 * This CSS adds icons to cart buttons using CSS pseudo-elements
 */

/* Base styles for cart buttons */
.add_to_cart_button,
.single_add_to_cart_button {
  position: relative;
  padding-left: 2.5rem !important; /* Make space for the icon */
}

/* Add icon using CSS - fallback if JavaScript approach doesn't work */
.add_to_cart_button::before,
.single_add_to_cart_button::before {
  content: "";
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-shopping-cart'%3E%3Ccircle cx='9' cy='21' r='1'%3E%3C/circle%3E%3Ccircle cx='20' cy='21' r='1'%3E%3C/circle%3E%3Cpath d='M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Hide the CSS icon if JavaScript adds the actual Feather icon */
.add_to_cart_button:has(i[data-feather="shopping-cart"])::before,
.single_add_to_cart_button:has(i[data-feather="shopping-cart"])::before {
  display: none;
}

/* RFQ button styles */
.rfq-button {
  position: relative;
  padding-left: 2.5rem !important;
}

.rfq-button[href="#"]:first-of-type::before {
  content: "";
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-file-text'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Buy Now button styles */
.rfq-button[href="#"]:last-of-type::before {
  content: "";
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-credit-card'%3E%3Crect x='1' y='4' width='22' height='16' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='1' y1='10' x2='23' y2='10'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Hide CSS icons if JavaScript adds the actual Feather icons */
.rfq-button:has(i[data-feather])::before {
  display: none;
}

/* Wishlist button styles */
.yith-wcwl-add-button .add_to_wishlist {
  position: relative;
  padding-left: 1.5rem !important;
}

.yith-wcwl-add-button .add_to_wishlist::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-heart'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Compare button styles */
.woosc-btn {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: #333;
  margin-right: 1rem;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.woosc-btn:hover {
  color: var(--bs-primary);
  text-decoration: none;
}

.woosc-btn i {
  margin-right: 0.25rem;
}

/* Share button styles */
.share-button {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  color: #333;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.share-button:hover {
  color: var(--bs-primary);
}

.share-button i {
  margin-right: 0.25rem;
}

/* Custom buttons wrapper */
.custom-buttons-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

/* Hide CSS icons if JavaScript adds the actual Feather icons */
.woosc-btn:has(i[data-feather])::before,
.yith-wcwl-add-button .add_to_wishlist:has(i[data-feather])::before,
.share-button:has(i[data-feather])::before {
  display: none;
}
