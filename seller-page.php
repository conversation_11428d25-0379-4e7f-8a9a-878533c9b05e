<?php
/**
 * Template Name: Sellers Page
 *
 * A custom template for displaying seller categories in a grid layout
 */
?>

<?php get_header(); ?>

<main class="seller-categories-container container">
  <div class="seller-categories-header">
    <div class="seller-breadcrumb">
      <a href="<?php echo esc_url(home_url('/')); ?>"><?php esc_html_e('Home', 'tendeal'); ?></a>
      <span class="separator"><i class="bi bi-chevron-right"></i></span>
      <span class="current"><?php esc_html_e('All Sellers', 'tendeal'); ?></span>
    </div>
    <h1 class="seller-categories-title"><?php esc_html_e('All Sellers', 'tendeal'); ?></h1>
  </div>

  <div class="seller-categories-grid">
    <?php
    // Check if WCFM plugin is active
    if (function_exists('wcfm_get_vendor_store_by_vendor')) {
      // Get all vendors/sellers
      $vendors = get_wcfm_vendors();

      if (!empty($vendors)) {
        $count = 0;
        $max_display = 16; // Maximum number of sellers to display initially

        foreach ($vendors as $vendor_id => $vendor_data) {
          // Get vendor/seller details
          $store_name = wcfm_get_vendor_store_name($vendor_id);
          $store_url = wcfmmp_get_store_url($vendor_id);

          // Get vendor rating
          $vendor_rating = 0;
          if (function_exists('wcfm_get_vendor_store_rating_badge')) {
            $rating_info = wcfm_get_vendor_store_rating_info($vendor_id);
            $vendor_rating = isset($rating_info['avg_rating']) ? floatval($rating_info['avg_rating']) : 0;
          }

          // Get vendor description
          $vendor_description = '';
          $vendor_user = get_userdata($vendor_id);
          if ($vendor_user) {
            $vendor_description = $vendor_user->description;
            if (empty($vendor_description)) {
              $vendor_description = 'A seller specialized in quality products';
            } else {
              // Truncate description to 100 characters
              $vendor_description = substr(strip_tags($vendor_description), 0, 100);
              if (strlen($vendor_description) >= 100) {
                $vendor_description .= '...';
              }
            }
          }

          // Get vendor category
          $vendor_category = '';
          $vendor_taxonomy_terms = wp_get_post_terms($vendor_id, 'wcfm_vendor_groups', array('fields' => 'names'));
          if (!empty($vendor_taxonomy_terms) && !is_wp_error($vendor_taxonomy_terms)) {
            $vendor_category = $vendor_taxonomy_terms[0];
          }

          // Get vendor logo/image
          $store_logo = '';
          if (function_exists('wcfmmp_get_store_logo')) {
            $store_logo = wcfmmp_get_store_logo($vendor_id);
            if (!$store_logo) {
              $store_logo = get_stylesheet_directory_uri() . '/images/categories/default-store.jpg';
            }
          } else {
            // Default images based on store name or category
            $default_images = array(
              'gaming' => get_stylesheet_directory_uri() . '/images/categories/gaming-laptop.jpg',
              'game' => get_stylesheet_directory_uri() . '/images/categories/nintendo-switch.jpg',
              'luxury' => get_stylesheet_directory_uri() . '/images/categories/watch.jpg',
              'phone' => get_stylesheet_directory_uri() . '/images/categories/smartphone.jpg',
              'mobile' => get_stylesheet_directory_uri() . '/images/categories/smartphone.jpg',
              'access' => get_stylesheet_directory_uri() . '/images/categories/headphones.jpg',
              'tablet' => get_stylesheet_directory_uri() . '/images/categories/tablet.jpg',
              'console' => get_stylesheet_directory_uri() . '/images/categories/xbox.jpg',
              'monitor' => get_stylesheet_directory_uri() . '/images/categories/gaming-monitor.jpg',
              'default' => get_stylesheet_directory_uri() . '/images/categories/default-store.jpg'
            );

            $store_logo = $default_images['default'];
            foreach ($default_images as $key => $image) {
              if (stripos($store_name, $key) !== false || (isset($vendor_category) && stripos($vendor_category, $key) !== false)) {
                $store_logo = $image;
                break;
              }
            }
          }

          // Display the seller card
          $count++;
          $hidden_class = ($count > $max_display) ? 'hidden-seller' : '';
          ?>
    <div class="seller-category-card <?php echo esc_attr($hidden_class); ?>">
      <a href="<?php echo esc_url($store_url); ?>" class="seller-category-link">
        <div class="seller-category-image">
          <div class="image-circle">
            <img src="<?php echo esc_url($store_logo); ?>" alt="<?php echo esc_attr($store_name); ?>">
          </div>
        </div>
        <div class="seller-category-info">
          <h3 class="seller-category-name"><?php echo esc_html($store_name); ?></h3>
          <p class="seller-category-description"><?php echo esc_html($vendor_description); ?></p>
          <div class="seller-category-rating">
            <div class="rating-stars">
              <?php
                    $full_stars = floor($vendor_rating);
                    $half_star = ($vendor_rating - $full_stars) >= 0.5;

                    for ($i = 1; $i <= 5; $i++) {
                      if ($i <= $full_stars) {
                        echo '<i class="bi bi-star-fill"></i>';
                      } elseif ($i == $full_stars + 1 && $half_star) {
                        echo '<i class="bi bi-star-half"></i>';
                      } else {
                        echo '<i class="bi bi-star"></i>';
                      }
                    }
                    ?>
            </div>
            <span class="rating-value"><?php echo number_format($vendor_rating, 1); ?></span>
          </div>
        </div>
      </a>
    </div>
    <?php
        }
      } else {
        echo '<div class="no-sellers-found">';
        echo '<p>' . esc_html__('No sellers found.', 'tendeal') . '</p>';
        echo '</div>';
      }
    } else {
      // WCFM not active, display a message
      echo '<div class="wcfm-not-active">';
      echo '<p>' . esc_html__('The WooCommerce Frontend Manager plugin is required to display sellers.', 'tendeal') . '</p>';
      echo '</div>';
    }
    ?>
  </div>

  <div class="seller-categories-footer">
    <button class="view-more-btn" id="view-more-sellers"><?php esc_html_e('View more', 'tendeal'); ?></button>
  </div>
</main>

<script>
jQuery(document).ready(function($) {
  // View more button functionality
  $('#view-more-sellers').on('click', function() {
    $('.hidden-seller').fadeIn(400).removeClass('hidden-seller');
    $(this).fadeOut(400);
  });
});
</script>

<?php get_footer(); ?>