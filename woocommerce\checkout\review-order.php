<?php
/**
 * Review order table
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/review-order.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 5.2.0
 */

defined( 'ABSPATH' ) || exit;

// Group cart items by vendor
$cart_items_by_vendor = array();
$vendor_totals = array();

foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
	$_product = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );

	if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_checkout_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
		// Get vendor ID (adjust based on your multi-vendor setup)
		$vendor_id = get_post_meta( $_product->get_id(), '_vendor_id', true );
		if ( empty( $vendor_id ) ) {
			$vendor_id = 'default';
		}

		// Get vendor name
		$vendor_name = get_post_meta( $_product->get_id(), '_vendor_name', true );
		if ( empty( $vendor_name ) ) {
			$vendor_name = get_bloginfo( 'name' );
		}

		if ( !isset( $cart_items_by_vendor[$vendor_id] ) ) {
			$cart_items_by_vendor[$vendor_id] = array(
				'name' => $vendor_name,
				'items' => array(),
				'subtotal' => 0
			);
		}

		$cart_items_by_vendor[$vendor_id]['items'][$cart_item_key] = $cart_item;
		$cart_items_by_vendor[$vendor_id]['subtotal'] += $cart_item['line_total'];
	}
}
?>

<div class="checkout-order-review">
	<?php do_action( 'woocommerce_review_order_before_cart_contents' ); ?>

	<!-- Order Items by Vendor -->
	<div class="order-items-section">
		<?php foreach ( $cart_items_by_vendor as $vendor_id => $vendor_data ) : ?>
		<div class="vendor-order-section" data-vendor="<?php echo esc_attr( $vendor_id ); ?>">
			<div class="vendor-header">
				<div class="vendor-info">
					<i data-feather="store" class="feather-sm me-2"></i>
					<h4 class="vendor-name"><?php echo esc_html( $vendor_data['name'] ); ?></h4>
				</div>
				<div class="vendor-item-count">
					<?php printf( _n( '%d item', '%d items', count( $vendor_data['items'] ), 'tendeal' ), count( $vendor_data['items'] ) ); ?>
				</div>
			</div>

			<div class="vendor-items">
				<?php foreach ( $vendor_data['items'] as $cart_item_key => $cart_item ) : ?>
					<?php
					$_product = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
					?>
					<div class="order-item" data-cart-item="<?php echo esc_attr( $cart_item_key ); ?>">
						<div class="item-image">
							<?php
							$thumbnail = apply_filters( 'woocommerce_cart_item_thumbnail', $_product->get_image( 'woocommerce_thumbnail' ), $cart_item, $cart_item_key );
							if ( ! $thumbnail ) {
								$thumbnail = '<img src="' . esc_url( wc_placeholder_img_src( 'woocommerce_thumbnail' ) ) . '" alt="' . esc_attr__( 'Placeholder', 'woocommerce' ) . '" class="woocommerce-placeholder wp-post-image" />';
							}
							echo $thumbnail;
							?>
						</div>
						<div class="item-details">
							<h5 class="item-name">
								<?php echo wp_kses_post( apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key ) ); ?>
							</h5>
							<div class="item-meta">
								<?php echo wc_get_formatted_cart_item_data( $cart_item ); ?>
							</div>
							<div class="item-quantity">
								<?php echo sprintf( __( 'Qty: %s', 'tendeal' ), $cart_item['quantity'] ); ?>
							</div>
						</div>
						<div class="item-price">
							<?php echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key ); ?>
						</div>
					</div>
				<?php endforeach; ?>
			</div>
		</div>
		<?php endforeach; ?>
	</div>

	<?php do_action( 'woocommerce_review_order_after_cart_contents' ); ?>

	<!-- Order Totals Section -->
	<div class="order-totals-section">
		<div class="totals-header">
			<h4 class="totals-title">
				<i data-feather="calculator" class="feather-sm me-2"></i>
				<?php esc_html_e( 'Order Summary', 'tendeal' ); ?>
			</h4>
		</div>

		<div class="totals-breakdown">
			<!-- Subtotal -->
			<div class="total-row subtotal-row">
				<span class="total-label">
					<i data-feather="shopping-cart" class="feather-xs me-2"></i>
					<?php esc_html_e( 'Subtotal', 'woocommerce' ); ?>
				</span>
				<span class="total-value"><?php wc_cart_totals_subtotal_html(); ?></span>
			</div>

			<!-- Coupons -->
			<?php foreach ( WC()->cart->get_coupons() as $code => $coupon ) : ?>
			<div class="total-row discount-row coupon-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
				<span class="total-label">
					<i data-feather="tag" class="feather-xs me-2"></i>
					<?php wc_cart_totals_coupon_label( $coupon ); ?>
				</span>
				<span class="total-value discount-value"><?php wc_cart_totals_coupon_html( $coupon ); ?></span>
			</div>
			<?php endforeach; ?>

			<!-- Shipping -->
			<?php if ( WC()->cart->needs_shipping() && WC()->cart->show_shipping() ) : ?>
				<?php do_action( 'woocommerce_review_order_before_shipping' ); ?>

				<div class="total-row shipping-row">
					<span class="total-label">
						<i data-feather="truck" class="feather-xs me-2"></i>
						<?php esc_html_e( 'Shipping', 'woocommerce' ); ?>
					</span>
					<span class="total-value"><?php wc_cart_totals_shipping_html(); ?></span>
				</div>

				<?php do_action( 'woocommerce_review_order_after_shipping' ); ?>
			<?php endif; ?>

			<!-- Fees -->
			<?php foreach ( WC()->cart->get_fees() as $fee ) : ?>
			<div class="total-row fee-row">
				<span class="total-label">
					<i data-feather="plus-circle" class="feather-xs me-2"></i>
					<?php echo esc_html( $fee->name ); ?>
				</span>
				<span class="total-value"><?php wc_cart_totals_fee_html( $fee ); ?></span>
			</div>
			<?php endforeach; ?>

			<!-- Tax -->
			<?php if ( wc_tax_enabled() && ! WC()->cart->display_prices_including_tax() ) : ?>
				<?php if ( 'itemized' === get_option( 'woocommerce_tax_total_display' ) ) : ?>
					<?php foreach ( WC()->cart->get_tax_totals() as $code => $tax ) : ?>
					<div class="total-row tax-row tax-rate-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
						<span class="total-label">
							<i data-feather="percent" class="feather-xs me-2"></i>
							<?php echo esc_html( $tax->label ); ?>
						</span>
						<span class="total-value"><?php echo wp_kses_post( $tax->formatted_amount ); ?></span>
					</div>
					<?php endforeach; ?>
				<?php else : ?>
				<div class="total-row tax-row">
					<span class="total-label">
						<i data-feather="percent" class="feather-xs me-2"></i>
						<?php echo esc_html( WC()->countries->tax_or_vat() ); ?>
					</span>
					<span class="total-value"><?php wc_cart_totals_taxes_total_html(); ?></span>
				</div>
				<?php endif; ?>
			<?php endif; ?>

			<?php do_action( 'woocommerce_review_order_before_order_total' ); ?>

			<!-- Final Total -->
			<div class="total-row final-total-row">
				<span class="total-label">
					<i data-feather="credit-card" class="feather-xs me-2"></i>
					<?php esc_html_e( 'Total', 'woocommerce' ); ?>
				</span>
				<span class="total-value final-total"><?php wc_cart_totals_order_total_html(); ?></span>
			</div>

			<?php do_action( 'woocommerce_review_order_after_order_total' ); ?>
		</div>
	</div>
</div>
