<?php
/**
 * Single Product tabs
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/tabs/tabs.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Filter tabs and allow third parties to add their own.
 *
 * Each tab is an array containing title, callback and priority.
 *
 * @see woocommerce_default_product_tabs()
 */
$product_tabs = apply_filters( 'woocommerce_product_tabs', array() );

if ( ! empty( $product_tabs ) ) : ?>



<div class="woocommerce-tabs wc-tabs-wrappe">
  <ul class="nav nav-tabs" id="product-tabs" role="tablist">
    <?php foreach ($product_tabs as $key => $tab) : ?>
    <li class="nav-item" role="presentation">
      <button class="nav-link <?php echo esc_attr($key === array_key_first($product_tabs) ? 'active' : ''); ?>"
        id="tab-<?php echo esc_attr($key); ?>" data-bs-toggle="tab"
        data-bs-target="#content-<?php echo esc_attr($key); ?>" type="button" role="tab">
        <?php echo esc_html($tab['title']); ?>
      </button>
    </li>
    <?php endforeach; ?>
  </ul>

  <div class="tab-content mt-3" id="product-tabs-content">
    <?php foreach ($product_tabs as $key => $tab) : ?>
    <div class="tab-pane fade <?php echo esc_attr($key === array_key_first($product_tabs) ? 'show active' : ''); ?>"
      id="content-<?php echo esc_attr($key); ?>" role="tabpanel">
      <?php call_user_func($tab['callback'], $key, $tab); ?>
    </div>
    <?php endforeach; ?>
  </div>

</div>

<?php endif; ?>