<?php
/**
 * Checkout billing information form - Enhanced with saved addresses
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-billing.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 * @global WC_Checkout $checkout
 */

defined( 'ABSPATH' ) || exit;

// Get customer data if logged in
$customer_id = get_current_user_id();
$has_saved_addresses = false;
$billing_address_data = array();
$customer_name = '';

if ( $customer_id ) {
	$customer = new WC_Customer( $customer_id );
	$user_info = get_userdata( $customer_id );
	$customer_name = trim( $customer->get_first_name() . ' ' . $customer->get_last_name() );
	if ( empty( $customer_name ) ) {
		$customer_name = $user_info->display_name;
	}

	// Get billing address data
	$billing_address_data = array(
		'first_name' => get_user_meta( $customer_id, 'billing_first_name', true ),
		'last_name' => get_user_meta( $customer_id, 'billing_last_name', true ),
		'company' => get_user_meta( $customer_id, 'billing_company', true ),
		'address_1' => get_user_meta( $customer_id, 'billing_address_1', true ),
		'address_2' => get_user_meta( $customer_id, 'billing_address_2', true ),
		'city' => get_user_meta( $customer_id, 'billing_city', true ),
		'state' => get_user_meta( $customer_id, 'billing_state', true ),
		'postcode' => get_user_meta( $customer_id, 'billing_postcode', true ),
		'country' => get_user_meta( $customer_id, 'billing_country', true ),
		'email' => get_user_meta( $customer_id, 'billing_email', true ),
		'phone' => get_user_meta( $customer_id, 'billing_phone', true ),
	);

	// Check if user has a saved billing address
	$has_saved_addresses = !empty( $billing_address_data['address_1'] ) || !empty( $billing_address_data['city'] );
}
?>
<div class="woocommerce-billing-fields">
	<?php if ( wc_ship_to_billing_address_only() && WC()->cart->needs_shipping() ) : ?>

		<h3><?php esc_html_e( 'Billing &amp; Shipping', 'woocommerce' ); ?></h3>

	<?php else : ?>

		<h3><?php esc_html_e( 'Billing details', 'woocommerce' ); ?></h3>

	<?php endif; ?>

	<?php do_action( 'woocommerce_before_checkout_billing_form', $checkout ); ?>

	<?php if ( $has_saved_addresses ) : ?>
		<!-- Saved Addresses Section -->
		<div class="saved-addresses-section">
			<div class="address-list">
				<?php
				// Prepare address data for card display
				$card_data = array();
				if ( !empty( $customer_name ) ) {
					$card_data['name'] = $customer_name;
				}
				if ( !empty( $billing_address_data['company'] ) ) {
					$card_data['company'] = $billing_address_data['company'];
				}

				// Build street address
				$street_parts = array_filter( array( $billing_address_data['address_1'], $billing_address_data['address_2'] ) );
				if ( !empty( $street_parts ) ) {
					$card_data['street'] = implode( ', ', $street_parts );
				}

				// Build city/state/postcode
				$location_parts = array_filter( array( $billing_address_data['city'], $billing_address_data['state'], $billing_address_data['postcode'] ) );
				if ( !empty( $location_parts ) ) {
					$card_data['location'] = implode( ', ', $location_parts );
				}

				// Add country
				if ( !empty( $billing_address_data['country'] ) ) {
					$countries = WC()->countries->get_countries();
					$card_data['country'] = isset( $countries[ $billing_address_data['country'] ] ) ? $countries[ $billing_address_data['country'] ] : $billing_address_data['country'];
				}

				// Add contact info
				if ( !empty( $billing_address_data['phone'] ) ) {
					$card_data['phone'] = $billing_address_data['phone'];
				}
				if ( !empty( $billing_address_data['email'] ) ) {
					$card_data['email'] = $billing_address_data['email'];
				}
				?>

				<div class="address-card" data-address-id="billing" data-address-type="billing">
					<div class="address-card-header">
						<i data-feather="user" class="address-icon"></i>
						<h3 class="address-name"><?php echo esc_html( $customer_name ); ?></h3>
					</div>

					<div class="address-card-type">
						<span class="address-type-badge"><?php esc_html_e( 'Billing address', 'woocommerce' ); ?></span>
					</div>

					<div class="address-details">
						<?php if ( !empty( $card_data['company'] ) ) : ?>
						<div class="address-item">
							<i data-feather="briefcase" class="address-item-icon"></i>
							<span class="address-item-text address-company"><?php echo esc_html( $card_data['company'] ); ?></span>
						</div>
						<?php endif; ?>

						<?php if ( !empty( $card_data['street'] ) ) : ?>
						<div class="address-item">
							<i data-feather="map-pin" class="address-item-icon"></i>
							<span class="address-item-text address-street"><?php echo esc_html( $card_data['street'] ); ?></span>
						</div>
						<?php endif; ?>

						<?php if ( !empty( $card_data['location'] ) ) : ?>
						<div class="address-item">
							<i data-feather="map" class="address-item-icon"></i>
							<span class="address-item-text address-location"><?php echo esc_html( $card_data['location'] ); ?></span>
						</div>
						<?php endif; ?>

						<?php if ( !empty( $card_data['country'] ) ) : ?>
						<div class="address-item">
							<i data-feather="globe" class="address-item-icon"></i>
							<span class="address-item-text address-country"><?php echo esc_html( $card_data['country'] ); ?></span>
						</div>
						<?php endif; ?>

						<?php if ( !empty( $card_data['phone'] ) ) : ?>
						<div class="address-item">
							<i data-feather="phone" class="address-item-icon"></i>
							<span class="address-item-text address-phone"><?php echo esc_html( $card_data['phone'] ); ?></span>
						</div>
						<?php endif; ?>

						<?php if ( !empty( $card_data['email'] ) ) : ?>
						<div class="address-item">
							<i data-feather="at-sign" class="address-item-icon"></i>
							<span class="address-item-text address-email"><?php echo esc_html( $card_data['email'] ); ?></span>
						</div>
						<?php endif; ?>
					</div>
				</div>
			</div>

			<!-- Or Divider -->
			<div class="or-divider">
				<span><?php esc_html_e( 'Or enter a new address', 'woocommerce' ); ?></span>
			</div>
		</div>

		<!-- Hidden input to track selected address -->
		<input type="hidden" id="selected_address_id" name="selected_address_id" value="billing" />
	<?php endif; ?>

	<div class="woocommerce-billing-fields__field-wrapper<?php echo $has_saved_addresses ? ' hidden-form' : ''; ?>">
		<?php
		$fields = $checkout->get_checkout_fields( 'billing' );

		foreach ( $fields as $key => $field ) {
			woocommerce_form_field( $key, $field, $checkout->get_value( $key ) );
		}
		?>
	</div>

	<?php do_action( 'woocommerce_after_checkout_billing_form', $checkout ); ?>
</div>

<?php if ( ! is_user_logged_in() && $checkout->is_registration_enabled() ) : ?>
	<div class="woocommerce-account-fields">
		<?php if ( ! $checkout->is_registration_required() ) : ?>

			<p class="form-row form-row-wide create-account">
				<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
					<input class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" id="createaccount" <?php checked( ( true === $checkout->get_value( 'createaccount' ) || ( true === apply_filters( 'woocommerce_create_account_default_checked', false ) ) ), true ); ?> type="checkbox" name="createaccount" value="1" /> <span><?php esc_html_e( 'Create an account?', 'woocommerce' ); ?></span>
				</label>
			</p>

		<?php endif; ?>

		<?php do_action( 'woocommerce_before_checkout_registration_form', $checkout ); ?>

		<?php if ( $checkout->get_checkout_fields( 'account' ) ) : ?>

			<div class="create-account">
				<?php foreach ( $checkout->get_checkout_fields( 'account' ) as $key => $field ) : ?>
					<?php woocommerce_form_field( $key, $field, $checkout->get_value( $key ) ); ?>
				<?php endforeach; ?>
				<div class="clear"></div>
			</div>

		<?php endif; ?>

		<?php do_action( 'woocommerce_after_checkout_registration_form', $checkout ); ?>
	</div>
<?php endif; ?>

<script>
// Initialize Feather icons for address cards
document.addEventListener('DOMContentLoaded', function() {
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
});
</script>

<?php if ( ! is_user_logged_in() && $checkout->is_registration_enabled() ) : ?>
	<div class="woocommerce-account-fields">
		<?php if ( ! $checkout->is_registration_required() ) : ?>

			<p class="form-row form-row-wide create-account">
				<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
					<input class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" id="createaccount" <?php checked( ( true === $checkout->get_value( 'createaccount' ) || ( true === apply_filters( 'woocommerce_create_account_default_checked', false ) ) ), true ); ?> type="checkbox" name="createaccount" value="1" /> <span><?php esc_html_e( 'Create an account?', 'woocommerce' ); ?></span>
				</label>
			</p>

		<?php endif; ?>

		<?php do_action( 'woocommerce_before_checkout_registration_form', $checkout ); ?>

		<?php if ( $checkout->get_checkout_fields( 'account' ) ) : ?>

			<div class="create-account">
				<?php foreach ( $checkout->get_checkout_fields( 'account' ) as $key => $field ) : ?>
					<?php woocommerce_form_field( $key, $field, $checkout->get_value( $key ) ); ?>
				<?php endforeach; ?>
				<div class="clear"></div>
			</div>

		<?php endif; ?>

		<?php do_action( 'woocommerce_after_checkout_registration_form', $checkout ); ?>
	</div>
<?php endif; ?>
