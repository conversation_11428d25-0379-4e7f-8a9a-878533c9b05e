---
layout: examples
title: مثال الألبوم
direction: rtl
---

<header data-bs-theme="dark">
  <div class="collapse text-bg-dark" id="navbarHeader">
    <div class="container">
      <div class="row">
        <div class="col-sm-8 col-md-7 py-4">
          <h4>حول</h4>
          <p class="text-body-secondary">أضف بعض المعلومات حول الألبوم، المؤلف، أو أي سياق خلفية آخر. اجعلها بضع جمل حتى يتمكن الزوار من التقاط بعض التلميحات المفيدة. ثم اربطها ببعض مواقع التواصل الاجتماعي أو معلومات الاتصال.</p>
        </div>
        <div class="col-sm-4 offset-md-1 py-4">
          <h4>تواصل معي</h4>
          <ul class="list-unstyled">
            <li><a href="#" class="text-white">تابعني على تويتر</a></li>
            <li><a href="#" class="text-white">شاركني الإعجاب في فيسبوك</a></li>
            <li><a href="#" class="text-white">راسلني على البريد الإلكتروني</a></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="navbar navbar-dark bg-dark shadow-sm">
    <div class="container">
      <a href="#" class="navbar-brand d-flex align-items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" aria-hidden="true" class="me-2" viewBox="0 0 24 24"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/><circle cx="12" cy="13" r="4"/></svg>
        <strong>الألبوم</strong>
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarHeader" aria-controls="navbarHeader" aria-expanded="false" aria-label="تبديل التنقل">
        <span class="navbar-toggler-icon"></span>
      </button>
    </div>
  </div>
</header>

<main>

  <section class="py-5 text-center container">
    <div class="row py-lg-5">
      <div class="col-lg-6 col-md-8 mx-auto">
        <h1 class="fw-light">مثال الألبوم</h1>
        <p class="lead text-body-secondary">وصف قصير حول الألبوم أدناه (محتوياته ، ومنشؤه ، وما إلى ذلك). اجعله قصير ولطيف، ولكن ليست قصير جدًا حتى لا يتخطى الناس هذا الألبوم تمامًا.</p>
        <p>
          <a href="#" class="btn btn-primary my-2">الدعوة الرئيسية للعمل</a>
          <a href="#" class="btn btn-secondary my-2">عمل ثانوي</a>
        </p>
      </div>
    </div>
  </section>

  <div class="album py-5 bg-body-tertiary">
    <div class="container">

      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>
        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>
        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>

        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>
        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>
        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>

        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>
        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>
        <div class="col">
          <div class="card shadow-sm">
            {{< placeholder width="100%" height="225" background="#55595c" color="#eceeef" class="card-img-top" text="صورة مصغرة" >}}
            <div class="card-body">
              <p class="card-text">هذه بطاقة أوسع مع نص داعم أدناه كمقدمة طبيعية لمحتوى إضافي. هذا المحتوى أطول قليلاً.</p>
              <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">عرض</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">تعديل</button>
                </div>
                <small class="text-body-secondary">9 دقائق</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</main>

<footer class="text-body-secondary py-5">
  <div class="container">
    <p class="float-end mb-1">
      <a href="#">عد إلى الأعلى</a>
    </p>
    <p class="mb-1">مثال الألبوم هو © Bootstrap ، ولكن يرجى تنزيله وتخصيصه لنفسك!</p>
    <p class="mb-0">جديد على Bootstrap؟ <a href="/"> تفضل بزيارة الصفحة الرئيسية </a> أو اقرأ <a href="{{< docsref "/getting-started/introduction">}} "> دليل البدء </a>.</p>
  </div>
</footer>
