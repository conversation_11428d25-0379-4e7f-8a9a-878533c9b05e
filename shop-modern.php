<?php
/**
 * Template Name: Modern Shop Page
 *
 * A modern shop page template with filters, featured products, and brand sections.
 *
 * @package Teandeal
 */

get_header();
?>

<div class="container shop-container">
  <div class=" ">

    <div class="row mt-4 mb-4 bg-white top-ads">
      <div class="left-banner  col-md-4 col-lg-4">

        <div class="row">
          <div class="col-12 mb-4 banner-wrapper" style="    position: relative;">
            <img src="<?php echo get_template_directory_uri();?>/img/img6.png " alt="">

            <div class="banner-content top-left col">
              <span class="text-primary">Best Offer</span>
              <h5>Powerfull Apple watches for you</h5>
              <button type="button" class="btn btn-warning mt-4 mb-6">shop now</button>
            </div>
          </div>

          <div class="col-12 mb-4 banner-wrapper" style="    position: relative;">
            <img src="<?php echo get_template_directory_uri();?>/img/img7.png " alt="">
            <div class="banner-content top-left col">
              <span class="text-primary">Coupons</span>
              <h5>Samsung Bluetooth Headphone</h5>
            </div>
          </div>
        </div>
      </div>

      <div class="right-banned col-md-8 col-lg-8">
        <div class="col banner-wrapper" style="    position: relative;">
          <img src="<?php echo get_template_directory_uri();?>/img/img9.png " alt="">
          <div class="banner-content top-left col">
            <h2>Discover virtual reality with Xiaomi</h2>
            <h6>Discover the latest offers every week. And with huge discounts</h6>
            <button type="button" class="btn btn-warning mt-4 mb-6">shop now</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <!-- Sidebar with filters -->
    <div class="col-lg-3 shop-sidebar">
      <div class="filter-section">
        <h4 class="filter-title">Brands</h4>
        <div class="brand-filter">
          <?php
          $brands = get_terms(array(
            'taxonomy' => 'product_brand',
            'hide_empty' => true,
            'number' => 10
          ));
          
          if (!empty($brands) && !is_wp_error($brands)) {
            echo '<ul class="brand-list">';
            foreach ($brands as $brand) {
              echo '<li class="brand-item">';
              echo '<input type="checkbox" id="brand-' . esc_attr($brand->slug) . '" name="brand[]" value="' . esc_attr($brand->slug) . '">';
              echo '<label for="brand-' . esc_attr($brand->slug) . '">' . esc_html($brand->name) . '</label>';
              echo '</li>';
            }
            echo '</ul>';
          }
          ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Ratings</h4>
        <div class="rating-filter">
          <ul class="rating-list">
            <?php for ($i = 5; $i >= 1; $i--) : ?>
            <li class="rating-item">
              <input type="checkbox" id="rating-<?php echo $i; ?>" name="rating[]" value="<?php echo $i; ?>">
              <label for="rating-<?php echo $i; ?>">
                <?php 
                  for ($j = 1; $j <= 5; $j++) {
                    if ($j <= $i) {
                      echo '<i class="bi bi-star-fill"></i>';
                    } else {
                      echo '<i class="bi bi-star"></i>';
                    }
                  }
                  ?>
              </label>
            </li>
            <?php endfor; ?>
          </ul>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Price</h4>
        <div class="price-filter">
          <div class="price-slider-container">
            <div class="price-slider"></div>
            <div class="price-range">
              <span class="min-price">$0</span>
              <span class="max-price">$1000</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content area -->
    <div class="col-lg-9 shop-main-content">


      <!-- Category section -->
      <div class="category-section">
        <h2 class="section-title">Computer & office</h2>

        <!-- Products grid -->
        <div class="products-grid">
          <?php
          $args = array(
            'post_type' => 'product',
            'posts_per_page' => 9,
            'tax_query' => array(
              array(
                'taxonomy' => 'product_cat',
                'field'    => 'slug',
                'terms'    => 'computer-office',
                'operator' => 'IN',
              ),
            ),
          );
          $products = new WP_Query($args);

          if ($products->have_posts()) {
            while ($products->have_posts()) {
              $products->the_post();
              global $product;
              ?>
          <div class="product-card">
            <div class="product-image">
              <?php if ($product->is_on_sale()) : ?>
              <span class="sale-badge">Sale</span>
              <?php endif; ?>
              <a href="<?php the_permalink(); ?>">
                <?php echo woocommerce_get_product_thumbnail(); ?>
              </a>
              <div class="product-actions">
                <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-cart-plus"></i>
                </button>
                <button class="action-btn add-to-wishlist"
                  data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-heart"></i>
                </button>
                <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-eye"></i>
                </button>
              </div>
            </div>
            <div class="product-info">
              <h3 class="product-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
              </h3>
              <div class="product-rating">
                <?php echo wc_get_rating_html($product->get_average_rating()); ?>
                <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
              </div>
              <div class="product-price">
                <?php echo $product->get_price_html(); ?>
              </div>
              <div class="product-meta">
                <?php if ($product->get_stock_status() === 'instock') : ?>
                <span class="in-stock">In Stock</span>
                <?php else : ?>
                <span class="out-of-stock">Out of Stock</span>
                <?php endif; ?>
                <span class="free-shipping">Free Shipping</span>
              </div>
            </div>
          </div>
          <?php
            }
            wp_reset_postdata();
          }
          ?>
        </div>

        <div class="view-more-container">
          <a href="<?php echo get_term_link('computer-office', 'product_cat'); ?>" class="btn btn-outline-primary">View
            more</a>
        </div>
      </div>

      <!-- Best brands section -->
      <div class="best-brands-section">
        <h2 class="section-title">Best brands</h2>
        <div class="brands-grid">
          <a href="#" class="brand-item">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/brands/apple.png" alt="Apple">
          </a>
          <a href="#" class="brand-item">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/brands/oppo.png" alt="Oppo">
          </a>
          <a href="#" class="brand-item">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/brands/dell.png" alt="Dell">
          </a>
          <a href="#" class="brand-item">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/brands/asus.png" alt="Asus">
          </a>
        </div>
      </div>

      <!-- Featured products section -->
      <div class="featured-products-section">
        <div class="section-header">
          <h2 class="section-title">Featured products</h2>
          <div class="section-controls">
            <button class="control-btn prev-btn"><i class="bi bi-chevron-left"></i></button>
            <button class="control-btn next-btn"><i class="bi bi-chevron-right"></i></button>
          </div>
        </div>

        <div class="featured-products-grid">
          <?php
          $args = array(
            'post_type' => 'product',
            'posts_per_page' => 3,
            'meta_key' => '_featured',
            'meta_value' => 'yes',
          );
          $featured_products = new WP_Query($args);

          if ($featured_products->have_posts()) {
            while ($featured_products->have_posts()) {
              $featured_products->the_post();
              global $product;
              ?>
          <div class="product-card">
            <div class="product-image">
              <?php if ($product->is_on_sale()) : ?>
              <span class="sale-badge">Sale</span>
              <?php endif; ?>
              <a href="<?php the_permalink(); ?>">
                <?php echo woocommerce_get_product_thumbnail(); ?>
              </a>
              <div class="product-actions">
                <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-cart-plus"></i>
                </button>
                <button class="action-btn add-to-wishlist"
                  data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-heart"></i>
                </button>
                <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-eye"></i>
                </button>
              </div>
            </div>
            <div class="product-info">
              <h3 class="product-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
              </h3>
              <div class="product-rating">
                <?php echo wc_get_rating_html($product->get_average_rating()); ?>
                <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
              </div>
              <div class="product-price">
                <?php echo $product->get_price_html(); ?>
              </div>
              <div class="product-meta">
                <?php if ($product->get_stock_status() === 'instock') : ?>
                <span class="in-stock">In Stock</span>
                <?php else : ?>
                <span class="out-of-stock">Out of Stock</span>
                <?php endif; ?>
                <span class="free-shipping">Free Shipping</span>
              </div>
            </div>
          </div>
          <?php
            }
            wp_reset_postdata();
          }
          ?>
        </div>
      </div>
    </div>
  </div>
</div>

<?php
get_footer();
?>