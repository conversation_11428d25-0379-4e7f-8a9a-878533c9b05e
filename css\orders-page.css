/* Orders Page Styles */

/* Page Header */
.orders-page-header {
    margin-bottom: 30px;
}

.orders-page-header .breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #98a2b3;
}

.breadcrumb-item a {
    color: #98a2b3;
    text-decoration: none;
}

.breadcrumb-item.current {
    color: #1d2939;
    font-weight: 500;
}

.breadcrumb-separator {
    color: #98a2b3;
}

.orders-title {
    font-size: 32px;
    font-weight: 600;
    color: #1d2939;
    margin: 0;
}

/* Order Status Filters */
.order-status-filters {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    gap: 20px;
}

.filter-tabs {
    display: flex;
    gap: 8px;
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: transparent;
    border: 1px solid rgba(152, 162, 179, 0.2);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #98a2b3;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-tab:hover {
    background-color: rgba(234, 156, 0, 0.05);
    border-color: #ea9c00;
    color: #ea9c00;
}

.filter-tab.active {
    background-color: #ea9c00;
    border-color: #ea9c00;
    color: #fff;
}

.tab-text {
    font-weight: 500;
}

.tab-count {
    font-size: 12px;
    opacity: 0.8;
}

/* Right side filters */
.order-filters-right {
    display: flex;
    gap: 16px;
    align-items: center;
}

.order-search {
    position: relative;
}

.search-input {
    width: 280px;
    padding: 12px 16px 12px 40px;
    border: 1px solid rgba(152, 162, 179, 0.2);
    border-radius: 8px;
    font-size: 14px;
    background-color: #fff;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #ea9c00;
}

.search-input::placeholder {
    color: #98a2b3;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    color: #98a2b3;
}

.time-filter {
    position: relative;
}

.time-select {
    padding: 12px 40px 12px 16px;
    border: 1px solid rgba(152, 162, 179, 0.2);
    border-radius: 8px;
    font-size: 14px;
    background-color: #fff;
    color: #1d2939;
    cursor: pointer;
    appearance: none;
    min-width: 120px;
}

.select-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    color: #98a2b3;
    pointer-events: none;
}

/* Orders Grid */
.orders-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

/* Order Card */
.order-card {
    background: #fff;
    border: 1px solid rgba(152, 162, 179, 0.2);
    border-radius: 12px;
    padding: 20px;
    position: relative;
    transition: box-shadow 0.2s ease;
}

.order-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Order Status Badge */
.order-status-badge {
    /* position: absolute; */
    top: 16px;
    left: 16px;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.order-status-shipped {
    background-color: rgba(234, 156, 0, 0.1);
    color: #ea9c00;
}

.order-status-completed {
    background-color: rgba(18, 183, 106, 0.1);
    color: #12b76a;
}

.order-status-processing {
    background-color: rgba(234, 156, 0, 0.1);
    color: #ea9c00;
}

.order-status-cancelled {
    background-color: rgba(240, 68, 56, 0.1);
    color: #f04438;
}

.order-status-pending {
    background-color: rgba(152, 162, 179, 0.1);
    color: #98a2b3;
}

/* Order Header */
.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    /* padding-top: 40px; */
    /* Space for status badge */
}

.order-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.order-date,
.order-id {
    font-size: 14px;
    color: #98a2b3;
}

.order-copy {
    font-size: 14px;
    color: #ea9c00;
    cursor: pointer;
    font-weight: 500;
}

.order-copy:hover {
    text-decoration: underline;
}

.order-details-link {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 14px;
    color: #ea9c00;
    text-decoration: none;
    font-weight: 500;
}

.order-details-link:hover {
    text-decoration: underline;
}

.details-icon {
    width: 16px;
    height: 16px;
}

/* Product Info */
.order-product {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
}

.product-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-details {
    padding: 0px;
    flex: 1;
}

.product-name {
    font-size: 16px;
    font-weight: 600;
    color: #1d2939;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.product-description {
    font-size: 14px;
    color: #98a2b3;
    margin: 0 0 12px 0;
    line-height: 1.5;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 8px;
}

.price-label {
    font-size: 14px;
    color: #98a2b3;
}

.price-amount {
    font-size: 18px;
    font-weight: 600;
    color: #1d2939;
}

.currency {
    font-size: 14px;
    color: #98a2b3;
}

/* Order Actions */
.order-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.primary {
    background-color: #ea9c00;
    color: #fff;
}

.action-btn.primary:hover {
    background-color: #d08a00;
}

.action-btn.secondary {
    background-color: rgba(152, 162, 179, 0.1);
    color: #98a2b3;
}

.action-btn.secondary:hover {
    background-color: rgba(152, 162, 179, 0.2);
    color: #1d2939;
}

.action-btn.danger {
    background-color: rgba(240, 68, 56, 0.1);
    color: #f04438;
}

.action-btn.danger:hover {
    background-color: rgba(240, 68, 56, 0.2);
}

.action-btn.outline {
    background-color: transparent;
    border: 1px solid rgba(152, 162, 179, 0.2);
    color: #98a2b3;
}

.action-btn.outline:hover {
    border-color: #ea9c00;
    color: #ea9c00;
}

.btn-icon {
    width: 16px;
    height: 16px;
}

.additional-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    width: 100%;
}

/* Pagination */
.orders-pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.view-more-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background-color: transparent;
    border: 1px solid rgba(152, 162, 179, 0.2);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #98a2b3;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-more-btn:hover {
    border-color: #ea9c00;
    color: #ea9c00;
}

.view-more-icon {
    width: 16px;
    height: 16px;
}

/* No Orders State */
.no-orders-state {
    text-align: center;
    padding: 60px 20px;
}

.no-orders-icon {
    margin-bottom: 20px;
}

.empty-icon {
    width: 64px;
    height: 64px;
    color: #98a2b3;
}

.no-orders-title {
    font-size: 24px;
    font-weight: 600;
    color: #1d2939;
    margin: 0 0 12px 0;
}

.no-orders-text {
    font-size: 16px;
    color: #98a2b3;
    margin: 0 0 30px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.browse-products-btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    background-color: #ea9c00;
    color: #fff;
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.browse-products-btn:hover {
    background-color: #d08a00;
    color: #fff;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .order-status-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }

    .filter-tabs {
        flex-wrap: wrap;
    }

    .order-filters-right {
        justify-content: space-between;
    }

    .search-input {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .orders-title {
        font-size: 24px;
    }

    .order-status-filters {
        gap: 12px;
    }

    .filter-tabs {
        gap: 6px;
    }

    .filter-tab {
        padding: 8px 12px;
        font-size: 13px;
    }

    .order-filters-right {
        flex-direction: column;
        gap: 12px;
    }

    .search-input {
        width: 100%;
    }

    .order-card {
        padding: 16px;
    }

    .order-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .order-product {
        flex-direction: column;
        gap: 12px;
    }

    .product-image {
        width: 100%;
        height: 200px;
        align-self: center;
        max-width: 200px;
    }

    .order-actions {
        flex-direction: column;
    }

    .additional-actions {
        flex-direction: column;
        gap: 8px;
    }

    .action-btn {
        justify-content: center;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .orders-page-header {
        margin-bottom: 20px;
    }

    .orders-title {
        font-size: 20px;
    }

    .filter-tabs {
        flex-direction: column;
        gap: 8px;
    }

    .filter-tab {
        justify-content: space-between;
    }

    .order-card {
        padding: 12px;
    }

    .order-status-badge {
        position: static;
        margin-bottom: 12px;
        align-self: flex-start;
    }

    .order-header {
        padding-top: 0;
    }
}

/* Hide default WooCommerce table styles when using new design */
.woocommerce-account .woocommerce-orders-table {
    display: none;
}

/* Ensure proper spacing in account content area */
.woocommerce-MyAccount-content .orders-page-header:first-child {
    margin-top: 0;
}