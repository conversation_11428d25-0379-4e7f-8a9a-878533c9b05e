/* Updated Shop Sidebar Styles */

/* Sidebar Container */
/* .shop-sidebar {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
} */

/* Filter Section Box Design */
.brands-section,
.categories-section,
.price-filter-section,
.rating-filter-section,
.attributes-section,
.attribute-filter {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 0;
  border-bottom: none;
}

/* Scrollable Brands Container */
.brands-scroll-container {
  max-height: 280px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 5px;
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  /* scrollbar-color: #EA9C00 #f1f1f1; */
}

.brands-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.brands-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.brands-scroll-container::-webkit-scrollbar-thumb {
  background: #EA9C00;
  border-radius: 3px;
}

.brands-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #d68910;
}

/* Brand Logo Grid */
.brand-logo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.brand-logo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  height: 60px;
  text-decoration: none;
  color: #333;
}

.brand-logo-item:hover {
  border-color: #EA9C00;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.brand-logo-item.active {
  border-color: #EA9C00;
  background-color: rgba(240, 193, 75, 0.2);
  box-shadow: 0 2px 5px rgba(240, 193, 75, 0.3);
  transform: translateY(-2px);
}

.brand-logo-item img {
  max-width: 100%;
  max-height: 30px;
  object-fit: contain;
}

.brand-logo-item span {
  font-size: 12px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.brand-count {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 10px;
  color: #777;
}

/* Brands Scroll Info */
.brands-scroll-info {
  margin-top: 10px;
  text-align: center;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.brands-scroll-info small {
  font-size: 11px;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.brands-scroll-info .bi {
  font-size: 12px;
  color: #EA9C00;
}

/* Filtered brands */
.brand-logo-item.filtered-out {
  display: none !important;
}

/* Restore the display of filtered brands when expanded */
.brands-expanded .brand-logo-item.filtered-out.was-hidden {
  display: flex !important;
}

/* Reviews Filter */
.reviews-filter {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.star-rating-filter {
  list-style: none;
  padding: 0;
  margin: 0;
}

.star-rating-item {
  margin-bottom: 10px;
}

.star-rating-item a {
  display: block;
  text-decoration: none;
  color: #333;
  transition: color 0.2s ease;
}

.star-rating-item a:hover {
  color: #EA9C00;
}

.star-rating-item.active a {
  color: #EA9C00;
  font-weight: 500;
}

.stars-container {
  display: flex;
  align-items: center;
}

.stars-container i {
  color: #EA9C00;
  font-size: 14px;
}

.star-rating-count {
  color: #777;
  font-size: 14px;
  margin-left: 5px;
}

/* Price Range Filter */
.price-range-filter {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.price-slider-container {
  padding: 10px 0 20px;
}

.price-range-inputs {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 15px;
}

.price-input-group {
  padding-top: 10px;
  width: 90%;
}

.price-input-label {
  display: block;
  font-size: 12px;
  color: #1D2939;
  margin-bottom: 5px;
}

.price-input {
  color: #1D2939 !important;
  height: 48px;
  width: 100%;
  padding: 10px !important;
  border: 1px solid #1D2939 !important;
  border-radius: 12px !important;
  font-size: 14px;
}

.price-slider {
  height: 4px;
  background: #eee;
  border-radius: 2px;
  position: relative;
  margin: 20px 0;
}

.price-slider .ui-slider-range {
  height: 4px;
  background-color: #EA9C00;
  position: absolute;
}

.price-slider .ui-slider-handle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #EA9C00;
  position: absolute;
  top: -6px;
  margin-left: -8px;
  cursor: pointer;
}

.price-filter-apply-btn {
  display: block;
  width: 100%;
  padding: 8px;
  background-color: #EA9C00;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 15px;
}

.price-filter-apply-btn:hover {
  background-color: #e0b347;
}

/* Demand Filter */
.demand-filter {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.demand-options {
  list-style: none;
  padding: 0;
  margin: 0;
}

.demand-option {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  cursor: pointer;
}

.demand-option input[type="radio"] {
  margin-right: 10px;
}

.demand-option label {
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

/* Categories Filter */
.categories-filter {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* Category container */
.category-container {
  position: relative;
}

/* Shadow effect to indicate scrollable content */
.category-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to top, rgba(248, 249, 250, 1), rgba(248, 249, 250, 0));
  pointer-events: none;
  z-index: 1;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  padding-right: 5px;
}

/* Custom scrollbar for Webkit browsers (Chrome, Safari) */
.category-list::-webkit-scrollbar {
  width: 6px;
}

.category-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.category-list::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 10px;
}

.category-list::-webkit-scrollbar-thumb:hover {
  background: #EA9C00;
}

.category-item {
  margin-bottom: 10px;
  position: relative;
  padding-right: 25px;
  /* Make room for the toggle button */
}

.category-item a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
  width: 100%;
}

.toggle-subcategories {
  position: absolute;
  right: 0;
  top: 0;
  width: 18px;
  height: 18px;
  line-height: 16px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 50%;
  background-color: #f8f9fa;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;
}

.toggle-subcategories:hover {
  background-color: #EA9C00;
  color: #fff;
  border-color: #EA9C00;
}

.category-item a:hover {
  color: #EA9C00;
}

.category-item.active>a {
  color: #EA9C00;
  font-weight: 500;
}

.category-item.active>a::after {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #EA9C00;
  margin-left: 5px;
}

.subcategory-item.active>a::after {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #EA9C00;
  margin-left: 5px;
}

.category-item.related>a,
.subcategory-item.related>a {
  color: #EA9C00;
  font-style: italic;
}

/* Mixed filtering indicators */
.mixed-filtering .category-item.active>a,
.mixed-filtering .subcategory-item.active>a {
  text-decoration: underline;
  text-decoration-color: #EA9C00;
  text-decoration-thickness: 2px;
  text-underline-offset: 3px;
}

/* Active filter badges */
.filter-count-1 .shop-sidebar::before,
.filter-count-2 .shop-sidebar::before,
.filter-count-3 .shop-sidebar::before,
.filter-count-4 .shop-sidebar::before,
.filter-count-5 .shop-sidebar::before,
.filter-count-6 .shop-sidebar::before,
.filter-count-7 .shop-sidebar::before,
.filter-count-8 .shop-sidebar::before,
.filter-count-9 .shop-sidebar::before {
  content: attr(data-filter-count);
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #EA9C00;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.filter-count-1 .shop-sidebar::before {
  content: "1";
}

.filter-count-2 .shop-sidebar::before {
  content: "2";
}

.filter-count-3 .shop-sidebar::before {
  content: "3";
}

.filter-count-4 .shop-sidebar::before {
  content: "4";
}

.filter-count-5 .shop-sidebar::before {
  content: "5";
}

.filter-count-6 .shop-sidebar::before {
  content: "6";
}

.filter-count-7 .shop-sidebar::before {
  content: "7";
}

.filter-count-8 .shop-sidebar::before {
  content: "8";
}

.filter-count-9 .shop-sidebar::before {
  content: "9+";
}

/* Active attribute styles */
.attribute-item.active {
  background-color: rgba(240, 193, 75, 0.1);
  border-radius: 4px;
  padding: 3px 5px;
  margin-left: -5px;
}

/* Active rating styles */
.star-rating-item.active {
  background-color: rgba(240, 193, 75, 0.1);
  border-radius: 4px;
  padding: 3px 5px;
  margin-left: -5px;
}

/* Active price filter styles */
.mixed-filtering .price-range-filter {
  position: relative;
}

.mixed-filtering .price-range-filter::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #EA9C00;
}

/* Reset filters button */
.reset-filters-container {
  margin-top: 30px;
  text-align: center;
}

.reset-filters-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  background-color: #f8f9fa;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
}

.reset-filters-btn:hover {
  background-color: #EA9C00;
  color: #fff;
  border-color: #EA9C00;
}

.reset-filters-btn i {
  margin-right: 5px;
}

.filter-count-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #EA9C00;
  color: #fff;
  font-size: 11px;
  font-weight: bold;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-count {
  color: #777;
  font-size: 12px;
}

.subcategory-list {
  list-style: none;
  padding-left: 15px;
  margin: 5px 0 0;
}

.subcategory-item {
  margin-bottom: 5px;
}

.subcategory-item a {
  font-size: 16px;
}

.subcategory-item.active>a {
  color: #EA9C00;
  font-weight: 500;
}

/* Attribute Filters */
.attributes-section {
  background: transparent;
  border: 0px solid #e5e7eb;
  border-radius: 0px;
  padding: 0px;
  margin-bottom: 20px;
  box-shadow: none;
  display: none;
  /* Hidden by default, shown via JS when needed */
}

.attribute-filter {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: none;
  /* Hidden by default, shown via JS when needed */
}

.attribute-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.attribute-item {
  margin-bottom: 8px;
}

.attribute-item a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 14px;
  color: #333;
  text-decoration: none;
  transition: color 0.2s ease;
}

.attribute-item a:hover {
  color: #EA9C00;
}

.attribute-item.active a {
  color: #EA9C00;
  font-weight: 500;
}

.attribute-count {
  color: #777;
  font-size: 12px;
}

/* No Apply Button needed since we're using direct links */

.reset-filters-btn {
  display: block;
  width: 100%;
  padding: 10px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.reset-filters-btn:hover {
  background-color: #e9e9e9;
}

/* Responsive Styles */
@media (max-width: 991px) {
  .brand-logo-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 767px) {
  .shop-sidebar {
    display: none;
  }


  
  .mobile-filter-btn {
    display: flex;
  }
  
  /* Show filters when active */
  .filters-active .shop-sidebar {
    display: block;
  }
  .brand-logo-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 575px) {
  .brand-logo-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Category and Brand Page Specific Styles */

/* Hide categories section on category pages */
body.product-category .categories-filter {
  display: none !important;
}

/* Show categories section on brand pages with filtered content */
body.product-brand .categories-filter {
  display: block !important;
}

/* Highlight current brand on brand pages */
body.product-brand .brand-logo-item.active {
  background-color: rgba(234, 156, 0, 0.1);
  border: 2px solid #EA9C00;
  transform: scale(1.05);
}

/* Filtered state indicators */
.filtered-content-indicator {
  position: relative;
}

.filtered-content-indicator::before {
  content: '';
  position: absolute;
  top: -5px;
  right: -5px;
  width: 10px;
  height: 10px;
  background-color: #EA9C00;
  border-radius: 50%;
  border: 2px solid white;
  z-index: 10;
}

/* Category page specific styling */
body.product-category .brands-section {
  border-left: 3px solid #EA9C00;
  padding-left: 15px;
  background-color: rgba(234, 156, 0, 0.02);
}

body.product-category .brands-section .section-title::after {
  content: ' (Related to Category)';
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

/* Brand page specific styling */
body.product-brand .categories-filter {
  border-left: 3px solid #28a745;
  padding-left: 15px;
  background-color: rgba(40, 167, 69, 0.02);
}

body.product-brand .categories-filter .section-title::after {
  content: ' (Related to Brand)';
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

/* Attributes section styling for filtered pages */
body.product-category .attributes-section,
body.product-brand .attributes-section {
  border-left: 3px solid #6f42c1;
  padding-left: 15px;
  background-color: rgba(111, 66, 193, 0.02);
}

body.product-category .attributes-section .section-title::after,
body.product-brand .attributes-section .section-title::after {
  content: ' (Filtered)';
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

/* Show filtered attributes and sections */
.attributes-section.show-filtered {
  display: block !important;
}

.attribute-filter.show-filtered {
  display: block !important;
}

/* Force show attributes on category and brand pages */
body.product-category .attributes-section,
body.product-brand .attributes-section {
  display: block !important;
}

body.product-category .attribute-filter,
body.product-brand .attribute-filter {
  display: block !important;
}

/* Price filter styling for filtered pages */
body.product-category .price-range-filter,
body.product-brand .price-range-filter {
  border-left: 3px solid #dc3545;
  padding-left: 15px;
  background-color: rgba(220, 53, 69, 0.02);
}

body.product-category .price-range-filter .section-title::after,
body.product-brand .price-range-filter .section-title::after {
  content: ' (Filtered Range)';
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

/* Pagination Loading States */
#shop-products.loading {
  opacity: 0.6;
  pointer-events: none;
}

.woocommerce-pagination a {
  position: relative;
}

.woocommerce-pagination a:hover {
  background-color: #f3f4f6;
}

/* Enhanced pagination styles for filtered pages */
body.product-category .woocommerce-pagination,
body.product-brand .woocommerce-pagination {
  border-top: 2px solid #f0f0f0;
  padding-top: 30px;
  margin-top: 40px;
}

body.product-category .woocommerce-pagination::before,
body.product-brand .woocommerce-pagination::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background-color: #EA9C00;
}

/* Pagination info for filtered pages */
.pagination-info {
  text-align: center;
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.pagination-info.filtered::after {
  content: ' (Filtered Results)';
  color: #EA9C00;
  font-weight: 500;
}