<section class="pb-md-5 mb-5">
  <div class="col-lg-8 mb-5">
    <div class="masthead-followup-icon d-inline-block mb-3" style="--bg-rgb: var(--bs-warning-rgb);">
      <svg class="bi fs-1"><use xlink:href="#plugin"></use></svg>
    </div>
    <h2 class="display-5 mb-3 fw-semibold lh-sm">Powerful JavaScript plugins without&nbsp;jQuery</h2>
    <p class="lead fw-normal">
      Add toggleable hidden elements, modals and offcanvas menus, popovers and tooltips, and so much more—all without jQuery. Bootstrap's JavaScript is HTML-first, meaning most plugins are added with <code>data</code> attributes in your HTML. Need more control? Include individual plugins programmatically.
    </p>
    <p class="d-flex justify-content-start lead fw-normal mb-md-0">
      <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/javascript/" class="icon-link icon-link-hover fw-semibold">
        Learn more about Bootstrap JavaScript
        <svg class="bi"><use xlink:href="#arrow-right"></use></svg>
      </a>
    </p>
  </div>
  <div class="row gx-md-5">
    <div class="col-lg-6 mb-3">
      <h3 class="fw-semibold">Data attribute API</h3>
      <p>Why write more JavaScript when you can write HTML? Nearly all of Bootstrap's JavaScript plugins feature a first-class data API, allowing you to use JavaScript just by adding <code>data</code> attributes.</p>
      <div class="p-4 mb-3 border rounded-3">
        <div class="dropdown">
          <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
            Dropdown
          </button>
          <ul class="dropdown-menu">
            <li><button class="dropdown-item" type="button">Dropdown item</button></li>
            <li><button class="dropdown-item" type="button">Dropdown item</button></li>
            <li><button class="dropdown-item" type="button">Dropdown item</button></li>
          </ul>
        </div>
      </div>

      {{ highlight (printf `<div class="dropdown">
  <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
    Dropdown
  </button>
  <ul class="dropdown-menu">
    <li><button class="dropdown-item" type="button">Dropdown item</button></li>
    <li><button class="dropdown-item" type="button">Dropdown item</button></li>
    <li><button class="dropdown-item" type="button">Dropdown item</button></li>
  </ul>
</div>
`) "html" "" }}
      <p>Learn more about <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/javascript/#using-bootstrap-as-a-module">our JavaScript as modules</a> and <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/javascript/#programmatic-api">using the programmatic API</a>.</p>
    </div>
    <div class="col-lg-6 mb-3">
      <h3 class="fw-semibold">Comprehensive set of plugins</h3>
      <p>Bootstrap features a dozen plugins that you can drop into any project. Drop them in all at once, or choose just the ones you need.</p>
      <hr class="my-4">
      <div class="row g-3">
        {{- range $plugin := .Site.Data.plugins -}}
          {{- $href := printf "/docs/%s/%s" $.Site.Params.docs_version $plugin.link }}
          <div class="col-sm-6 mb-2">
            <a class="d-block pe-lg-4 text-decoration-none lh-sm" href="{{ $href }}">
              <h4 class="mb-0 fs-5 fw-semibold">{{ $plugin.name }}</h4>
              <small class="text-body-secondary">{{ $plugin.description }}</small>
            </a>
          </div>
        {{- end }}
      </div>
    </div>
  </div>

</section>
