---
layout: docs
title: Z-index
description: Use our low-level `z-index` utilities to quickly change the stack level of an element or component.
group: utilities
toc: true
added: "5.3"
---

## Example

Use `z-index` utilities to stack elements on top of one another. Requires a `position` value other than `static`, which can be set with custom styles or using our [position utilities]({{< docsref "/utilities/position/" >}}).

{{< callout >}}
We call these "low-level" `z-index` utilities because of their default values of `-1` through `3`, which we use for the layout of overlapping components. High-level `z-index` values are used for overlay components like modals and tooltips.
{{< /callout >}}

{{< example class="bd-example-zindex-levels position-relative" >}}
<div class="z-3 position-absolute p-5 rounded-3"><span>z-3</span></div>
<div class="z-2 position-absolute p-5 rounded-3"><span>z-2</span></div>
<div class="z-1 position-absolute p-5 rounded-3"><span>z-1</span></div>
<div class="z-0 position-absolute p-5 rounded-3"><span>z-0</span></div>
<div class="z-n1 position-absolute p-5 rounded-3"><span>z-n1</span></div>
{{< /example >}}

## Overlays

Bootstrap overlay components—dropdown, modal, offcanvas, popover, toast, and tooltip—all have their own `z-index` values to ensure a usable experience with competing "layers" of an interface.

Read about them in the [`z-index` layout page]({{< docsref "/layout/z-index" >}}).

## Component approach

On some components, we use our low-level `z-index` values to manage repeating elements that overlap one another (like buttons in a button group or items in a list group).

Learn about our [`z-index` approach]({{< docsref "/extend/approach#z-index-scales" >}}).

## CSS

### Sass maps

Customize this Sass map to change the available values and generated utilities.

{{< scss-docs name="zindex-levels-map" file="scss/_variables.scss" >}}

### Sass utilities API

Position utilities are declared in our utilities API in `scss/_utilities.scss`. [Learn how to use the utilities API.]({{< docsref "/utilities/api#using-the-api" >}})

{{< scss-docs name="utils-zindex" file="scss/_utilities.scss" >}}
