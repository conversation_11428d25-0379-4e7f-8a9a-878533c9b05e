/**
 * Optimized Cart JavaScript
 * Handles all cart functionality with proper error handling and performance optimization
 */

(function($) {
    'use strict';

    // Configuration
    const config = {
        debounceDelay: 500,
        ajaxTimeout: 30000,
        maxRetries: 3
    };

    // State management
    let isUpdating = false;
    let updateQueue = [];
    let retryCount = 0;

    /**
     * Initialize cart functionality
     */
    function initCart() {
        initSelectAllFunctionality();
        initQuantityControls();
        initItemRemoval();
        initBulkActions();
        initShippingSelection();
        initAddressManagement();
        initCouponFunctionality();
        initFormValidation();
        
        console.log('Optimized cart initialized');
    }

    /**
     * Initialize select all functionality
     */
    function initSelectAllFunctionality() {
        const $selectAll = $('#select-all');
        const $itemCheckboxes = $('.cart-item-checkbox');
        const $deleteButton = $('#delete-selected-cart-items');

        // Handle select all checkbox
        $selectAll.on('change', function() {
            const isChecked = $(this).is(':checked');
            $itemCheckboxes.prop('checked', isChecked);
            updateDeleteButtonState();
        });

        // Handle individual item checkboxes
        $(document).on('change', '.cart-item-checkbox', function() {
            updateSelectAllState();
            updateDeleteButtonState();
        });

        // Update select all state based on individual checkboxes
        function updateSelectAllState() {
            const totalCheckboxes = $itemCheckboxes.length;
            const checkedCheckboxes = $itemCheckboxes.filter(':checked').length;
            
            $selectAll.prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
            $selectAll.prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
        }

        // Update delete button state
        function updateDeleteButtonState() {
            const checkedCount = $itemCheckboxes.filter(':checked').length;
            $deleteButton.prop('disabled', checkedCount === 0);
            $deleteButton.find('.selected-count').text(`(${checkedCount})`);
        }

        // Initial state update
        updateSelectAllState();
        updateDeleteButtonState();
    }

    /**
     * Initialize quantity controls
     */
    function initQuantityControls() {
        // Debounced quantity update function
        const debouncedQuantityUpdate = debounce(updateCartQuantity, config.debounceDelay);

        // Handle quantity input changes
        $(document).on('input change', '.qty-input', function() {
            const $input = $(this);
            const newQuantity = parseInt($input.val());
            const cartItemKey = $input.data('cart-item-key');
            
            // Validate quantity
            if (isNaN(newQuantity) || newQuantity < 1) {
                $input.val(1);
                return;
            }

            // Update quantity with debounce
            debouncedQuantityUpdate(cartItemKey, newQuantity, $input);
        });

        // Handle decrease quantity button
        $(document).on('click', '.decrease-qty', function() {
            const $button = $(this);
            const $input = $button.siblings('.qty-input');
            const currentQty = parseInt($input.val());
            
            if (currentQty > 1) {
                $input.val(currentQty - 1).trigger('change');
            }
        });

        // Handle increase quantity button
        $(document).on('click', '.increase-qty', function() {
            const $button = $(this);
            const $input = $button.siblings('.qty-input');
            const currentQty = parseInt($input.val());
            const maxQty = parseInt($input.attr('max')) || 999;
            
            if (currentQty < maxQty) {
                $input.val(currentQty + 1).trigger('change');
            }
        });
    }

    /**
     * Update cart quantity via AJAX
     */
    function updateCartQuantity(cartItemKey, quantity, $input) {
        if (isUpdating) {
            updateQueue.push({ cartItemKey, quantity, $input });
            return;
        }

        isUpdating = true;
        showLoadingState();

        // Add loading state to specific item
        const $productItem = $input.closest('.cart-product-item');
        $productItem.addClass('updating');

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'update_cart_item_quantity',
                cart_item_key: cartItemKey,
                quantity: quantity,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update item totals
                    updateItemTotals(cartItemKey, response.data);
                    
                    // Update cart totals
                    updateCartTotals(response.data.cart_totals);
                    
                    // Show success message
                    showMessage(cartOptimized.success_message, 'success');
                    
                    retryCount = 0;
                } else {
                    handleAjaxError(response.data || cartOptimized.error_message);
                }
            },
            error: function(xhr, status, error) {
                handleAjaxError(error);
            },
            complete: function() {
                $productItem.removeClass('updating');
                hideLoadingState();
                isUpdating = false;
                
                // Process queue
                processUpdateQueue();
            }
        });
    }

    /**
     * Initialize item removal functionality
     */
    function initItemRemoval() {
        // Handle individual item removal
        $(document).on('click', '.remove-item-btn', function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const cartItemKey = $button.data('cart-item-key');
            const productName = $button.data('product-name');
            
            // Show confirmation dialog
            if (confirm(sprintf(cartOptimized.confirm_remove_item || 'Are you sure you want to remove "%s" from your cart?', productName))) {
                removeCartItem(cartItemKey, $button);
            }
        });
    }

    /**
     * Remove cart item via AJAX
     */
    function removeCartItem(cartItemKey, $button) {
        const $productItem = $button.closest('.cart-product-item');
        $productItem.addClass('removing');

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'remove_cart_item',
                cart_item_key: cartItemKey,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Remove item with animation
                    $productItem.fadeOut(300, function() {
                        $(this).remove();
                        
                        // Update cart totals
                        updateCartTotals(response.data.cart_totals);
                        
                        // Check if vendor section is empty
                        checkEmptyVendorSections();
                        
                        // Update select all state
                        updateSelectAllState();
                    });
                    
                    showMessage(response.data.message || 'Item removed from cart', 'success');
                } else {
                    $productItem.removeClass('removing');
                    handleAjaxError(response.data || 'Failed to remove item');
                }
            },
            error: function(xhr, status, error) {
                $productItem.removeClass('removing');
                handleAjaxError(error);
            }
        });
    }

    /**
     * Initialize bulk actions
     */
    function initBulkActions() {
        // Handle bulk delete
        $('#delete-selected-cart-items').on('click', function() {
            const $checkedItems = $('.cart-item-checkbox:checked');
            
            if ($checkedItems.length === 0) {
                return;
            }

            if (confirm(cartOptimized.confirm_remove || 'Are you sure you want to remove selected items?')) {
                const cartItemKeys = $checkedItems.map(function() {
                    return $(this).val();
                }).get();

                removeMultipleCartItems(cartItemKeys);
            }
        });
    }

    /**
     * Remove multiple cart items
     */
    function removeMultipleCartItems(cartItemKeys) {
        showLoadingState();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'remove_selected_cart_items',
                cart_item_keys: cartItemKeys,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Remove items with animation
                    cartItemKeys.forEach(function(key) {
                        $(`.cart-product-item[data-cart-item-key="${key}"]`).fadeOut(300, function() {
                            $(this).remove();
                        });
                    });

                    // Update cart totals
                    updateCartTotals(response.data.cart_totals);
                    
                    // Reset checkboxes
                    $('#select-all').prop('checked', false);
                    
                    showMessage(response.data.message || 'Selected items removed', 'success');
                } else {
                    handleAjaxError(response.data || 'Failed to remove items');
                }
            },
            error: function(xhr, status, error) {
                handleAjaxError(error);
            },
            complete: function() {
                hideLoadingState();
            }
        });
    }

    /**
     * Initialize shipping selection
     */
    function initShippingSelection() {
        // Handle shipping method selection button click
        $(document).on('click', '.shipping-selection-btn', function() {
            const vendorId = $(this).data('vendor');
            $('#shippingModal').data('vendor-id', vendorId);
            showShippingModal(vendorId);
        });

        // Handle confirm shipping method button
        $(document).on('click', '#confirm-shipping-method', function() {
            confirmShippingMethod();
        });

        // Handle modal close - reset state
        $('#shippingModal').on('hidden.bs.modal', function() {
            $(this).removeData('vendor-id');
            $('#shipping-methods-list').empty();
            $('#confirm-shipping-method').prop('disabled', true);
        });
    }

    /**
     * Show shipping method selection modal
     */
    function showShippingModal(vendorId) {
        const $modal = $('#shippingModal');
        const $vendorName = $('#vendor-name');
        const $vendorInitial = $('#vendor-initial');
        const $vendorLocation = $('#vendor-location');
        const $methodsList = $('#shipping-methods-list');
        const $confirmBtn = $('#confirm-shipping-method');

        // Get vendor information
        const $vendorSection = $(`.vendor-cart-section[data-vendor="${vendorId}"]`);
        const vendorName = $vendorSection.find('.vendor-name').text() || 'Unknown Vendor';
        const vendorLocation = $vendorSection.find('.vendor-location').text() || 'Location not specified';

        // Update modal content
        $vendorName.text(vendorName);
        $vendorInitial.text(vendorName.charAt(0).toUpperCase());
        $vendorLocation.text(vendorLocation);

        // Show modal
        $modal.modal('show');

        // Load shipping methods
        loadShippingMethods(vendorId);
    }

    /**
     * Load shipping methods for vendor
     */
    function loadShippingMethods(vendorId) {
        const $loading = $('#shipping-loading');
        const $content = $('#shipping-content');
        const $methodsList = $('#shipping-methods-list');

        $loading.show();
        $content.hide();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'get_vendor_shipping_methods',
                vendor_id: vendorId,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    renderShippingMethods(response.data.methods);
                    $loading.hide();
                    $content.show();
                } else {
                    showShippingError(response.data || 'Failed to load shipping methods');
                }
            },
            error: function(xhr, status, error) {
                showShippingError('Network error occurred');
            }
        });
    }

    /**
     * Render shipping methods in modal
     */
    function renderShippingMethods(methods) {
        const $methodsList = $('#shipping-methods-list');
        const $confirmBtn = $('#confirm-shipping-method');

        if (!methods || methods.length === 0) {
            $methodsList.html(`
                <div class="alert alert-warning">
                    <i data-feather="alert-triangle" class="feather-sm me-2"></i>
                    ${cartOptimized.no_shipping_methods || 'No shipping methods available for this vendor.'}
                </div>
            `);
            return;
        }

        let methodsHtml = '';
        methods.forEach(function(method, index) {
            const isSelected = method.selected || index === 0;
            methodsHtml += `
                <div class="shipping-method-option ${isSelected ? 'selected' : ''}" data-method-id="${method.id}">
                    <div class="method-radio">
                        <input type="radio" name="shipping_method" value="${method.id}"
                               id="method_${method.id}" ${isSelected ? 'checked' : ''}>
                        <label for="method_${method.id}"></label>
                    </div>
                    <div class="method-info">
                        <div class="method-header">
                            <h6 class="method-name">${method.name}</h6>
                            <span class="method-cost">${method.cost}</span>
                        </div>
                        <div class="method-details">
                            <div class="delivery-time">
                                <i data-feather="clock" class="feather-xs me-1"></i>
                                ${method.delivery_time || 'Standard delivery'}
                            </div>
                            ${method.description ? `<div class="method-description">${method.description}</div>` : ''}
                        </div>
                    </div>
                    <div class="method-icon">
                        <i data-feather="${method.icon || 'truck'}" class="feather-lg"></i>
                    </div>
                </div>
            `;
        });

        $methodsList.html(methodsHtml);

        // Enable confirm button if a method is selected
        $confirmBtn.prop('disabled', false);

        // Handle method selection
        $methodsList.on('click', '.shipping-method-option', function() {
            const $option = $(this);
            const $radio = $option.find('input[type="radio"]');

            // Update selection
            $('.shipping-method-option').removeClass('selected');
            $option.addClass('selected');
            $radio.prop('checked', true);

            // Update delivery estimate
            updateDeliveryEstimate($option.data('method-id'), methods);
        });

        // Initialize delivery estimate for selected method
        const selectedMethod = methods.find(m => m.selected) || methods[0];
        if (selectedMethod) {
            updateDeliveryEstimate(selectedMethod.id, methods);
        }
    }

    /**
     * Update delivery estimate
     */
    function updateDeliveryEstimate(methodId, methods) {
        const method = methods.find(m => m.id === methodId);
        const $estimateText = $('#delivery-estimate-text');

        if (method && method.estimate) {
            $estimateText.html(`
                <strong>Estimated Delivery:</strong> ${method.estimate}
                ${method.tracking ? '<br><small>Tracking available</small>' : ''}
            `);
        } else {
            $estimateText.text('Delivery estimate not available');
        }
    }

    /**
     * Show shipping error
     */
    function showShippingError(message) {
        const $loading = $('#shipping-loading');
        const $content = $('#shipping-content');
        const $methodsList = $('#shipping-methods-list');

        $loading.hide();
        $content.show();

        $methodsList.html(`
            <div class="alert alert-danger">
                <i data-feather="alert-circle" class="feather-sm me-2"></i>
                ${message}
            </div>
        `);
    }

    /**
     * Confirm shipping method selection
     */
    function confirmShippingMethod() {
        const selectedMethod = $('input[name="shipping_method"]:checked').val();
        const vendorId = $('#shippingModal').data('vendor-id');

        if (!selectedMethod) {
            showMessage('Please select a shipping method', 'error');
            return;
        }

        showLoadingState();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'update_vendor_shipping_method',
                vendor_id: vendorId,
                shipping_method: selectedMethod,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update shipping display in cart
                    updateVendorShippingDisplay(vendorId, response.data);

                    // Update cart totals
                    updateCartTotals(response.data.cart_totals);

                    // Close modal
                    $('#shippingModal').modal('hide');

                    showMessage('Shipping method updated successfully', 'success');
                } else {
                    showMessage(response.data || 'Failed to update shipping method', 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('Network error occurred', 'error');
            },
            complete: function() {
                hideLoadingState();
            }
        });
    }

    /**
     * Update vendor shipping display in cart
     */
    function updateVendorShippingDisplay(vendorId, data) {
        const $vendorSection = $(`.vendor-cart-section[data-vendor="${vendorId}"]`);
        const $shippingBtn = $vendorSection.find('.shipping-selection-btn');
        const $shippingText = $shippingBtn.find('.shipping-text');
        const $shippingCost = $shippingBtn.find('.shipping-cost');

        if (data.method_name) {
            $shippingText.text(data.method_name);
        }

        if (data.method_cost) {
            $shippingCost.text(data.method_cost);
        }

        // Add visual feedback
        $shippingBtn.addClass('updated');
        setTimeout(function() {
            $shippingBtn.removeClass('updated');
        }, 2000);
    }

    /**
     * Initialize address management
     */
    function initAddressManagement() {
        // Handle address form toggle
        $('#toggle-address-form').on('click', function() {
            $('#address-update-form').slideToggle();
        });

        // Handle address form submission
        $('#cart-address-form').on('submit', function(e) {
            e.preventDefault();
            updateCartAddress($(this));
        });
    }

    /**
     * Update cart address
     */
    function updateCartAddress($form) {
        const formData = $form.serialize();
        
        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            data: formData + '&action=update_cart_address',
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    $('#address-update-form').slideUp();
                    // Refresh shipping calculations
                    location.reload();
                } else {
                    showMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showMessage(cartOptimized.error_message, 'error');
            }
        });
    }

    /**
     * Initialize coupon functionality
     */
    function initCouponFunctionality() {
        $('#apply_coupon').on('click', function() {
            const couponCode = $('#coupon_code').val().trim();
            
            if (!couponCode) {
                showMessage('Please enter a coupon code', 'error');
                return;
            }

            applyCoupon(couponCode);
        });

        // Handle enter key in coupon input
        $('#coupon_code').on('keypress', function(e) {
            if (e.which === 13) {
                $('#apply_coupon').click();
            }
        });
    }

    /**
     * Apply coupon
     */
    function applyCoupon(couponCode) {
        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            data: {
                action: 'apply_coupon',
                coupon_code: couponCode,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage('Coupon applied successfully!', 'success');
                    location.reload();
                } else {
                    showMessage(response.data || 'Invalid coupon code', 'error');
                }
            },
            error: function() {
                showMessage('Error applying coupon', 'error');
            }
        });
    }

    /**
     * Initialize form validation
     */
    function initFormValidation() {
        // Validate quantity inputs
        $(document).on('blur', '.qty-input', function() {
            const $input = $(this);
            const value = parseInt($input.val());
            const min = parseInt($input.attr('min')) || 1;
            const max = parseInt($input.attr('max')) || 999;

            if (isNaN(value) || value < min) {
                $input.val(min);
            } else if (value > max) {
                $input.val(max);
            }
        });
    }

    /**
     * Update item totals in the UI
     */
    function updateItemTotals(cartItemKey, data) {
        const $item = $(`.cart-product-item[data-cart-item-key="${cartItemKey}"]`);
        
        if (data.item_total) {
            $item.find('.total-amount').html(data.item_total);
        }
        
        if (data.item_price) {
            $item.find('.price-amount').html(data.item_price);
        }
    }

    /**
     * Update cart totals in the UI
     */
    function updateCartTotals(totals) {
        if (totals) {
            // Update sidebar totals
            if (totals.subtotal) {
                $('.cart-subtotal .amount').html(totals.subtotal);
            }
            if (totals.total) {
                $('.cart-total .amount').html(totals.total);
            }
            if (totals.shipping) {
                $('.shipping-cost').html(totals.shipping);
            }
        }
    }

    /**
     * Check for empty vendor sections
     */
    function checkEmptyVendorSections() {
        $('.vendor-cart-section').each(function() {
            const $section = $(this);
            const itemCount = $section.find('.cart-product-item').length;
            
            if (itemCount === 0) {
                $section.fadeOut(300, function() {
                    $(this).remove();
                });
            }
        });
    }

    /**
     * Show loading state
     */
    function showLoadingState() {
        $('#cart-loading').fadeIn(200);
        $('body').addClass('cart-updating');
    }

    /**
     * Hide loading state
     */
    function hideLoadingState() {
        $('#cart-loading').fadeOut(200);
        $('body').removeClass('cart-updating');
    }

    /**
     * Show message to user
     */
    function showMessage(message, type = 'info') {
        const $messages = $('#cart-messages');
        const $alert = $messages.find('.alert');
        
        $alert.removeClass('alert-success alert-error alert-warning alert-info')
              .addClass(`alert-${type}`);
        $alert.find('.message-text').text(message);
        
        $messages.fadeIn(300);
        
        // Auto hide after 5 seconds
        setTimeout(function() {
            $messages.fadeOut(300);
        }, 5000);
    }

    /**
     * Handle AJAX errors
     */
    function handleAjaxError(error) {
        console.error('Cart AJAX error:', error);
        
        if (retryCount < config.maxRetries) {
            retryCount++;
            showMessage(`Error occurred. Retrying... (${retryCount}/${config.maxRetries})`, 'warning');
        } else {
            showMessage(error || cartOptimized.error_message, 'error');
            retryCount = 0;
        }
    }

    /**
     * Process update queue
     */
    function processUpdateQueue() {
        if (updateQueue.length > 0) {
            const nextUpdate = updateQueue.shift();
            updateCartQuantity(nextUpdate.cartItemKey, nextUpdate.quantity, nextUpdate.$input);
        }
    }

    /**
     * Debounce utility function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Simple sprintf implementation
     */
    function sprintf(str, ...args) {
        return str.replace(/%s/g, () => args.shift());
    }

    /**
     * Handle close message button
     */
    $(document).on('click', '.cart-messages .btn-close', function() {
        $('#cart-messages').fadeOut(300);
    });

    // Initialize when document is ready
    $(document).ready(function() {
        initCart();
    });

    // Expose functions globally for external use
    window.cartOptimized = window.cartOptimized || {};
    window.cartOptimized.functions = {
        showMessage: showMessage,
        showLoadingState: showLoadingState,
        hideLoadingState: hideLoadingState,
        updateCartQuantity: updateCartQuantity,
        removeCartItem: removeCartItem
    };

})(jQuery);
