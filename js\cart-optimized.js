/**
 * Optimized Cart JavaScript
 * Handles all cart functionality with proper error handling and performance optimization
 */

(function($) {
    'use strict';

    // Configuration
    const config = {
        debounceDelay: 500,
        ajaxTimeout: 30000,
        maxRetries: 3,
        cacheExpiry: 300000, // 5 minutes
        performanceMonitoring: true
    };

    // State management
    let isUpdating = false;
    let updateQueue = [];
    let retryCount = 0;

    // Performance monitoring
    let performanceMetrics = {
        ajaxRequests: 0,
        totalResponseTime: 0,
        errors: 0,
        cacheHits: 0
    };

    // Request caching
    const requestCache = new Map();

    // Performance optimization flags
    let isLowEndDevice = false;
    let prefersReducedMotion = false;

    /**
     * Initialize cart functionality
     */
    function initCart() {
        // Detect device capabilities
        detectDeviceCapabilities();

        // Initialize performance monitoring
        if (config.performanceMonitoring) {
            initPerformanceMonitoring();
        }

        // Initialize core functionality
        initSelectAllFunctionality();
        initQuantityControls();
        initItemRemoval();
        initBulkActions();
        initShippingSelection();
        initAddressManagement();
        initCouponFunctionality();
        initFormValidation();

        // Initialize performance optimizations
        initRequestOptimization();
        initDOMOptimization();

        console.log('Optimized cart initialized with performance monitoring');
    }

    /**
     * Detect device capabilities for optimization
     */
    function detectDeviceCapabilities() {
        // Check for low-end device indicators
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection) {
            isLowEndDevice = connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';
        }

        // Check for reduced motion preference
        prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        // Adjust config for low-end devices
        if (isLowEndDevice) {
            config.debounceDelay = 1000; // Increase debounce delay
            config.ajaxTimeout = 60000; // Increase timeout
        }

        // Disable animations for reduced motion
        if (prefersReducedMotion) {
            document.body.classList.add('reduce-motion');
        }
    }

    /**
     * Initialize performance monitoring
     */
    function initPerformanceMonitoring() {
        // Monitor AJAX performance
        $(document).ajaxSend(function(event, xhr, settings) {
            xhr.startTime = performance.now();
            performanceMetrics.ajaxRequests++;
        });

        $(document).ajaxComplete(function(event, xhr, settings) {
            if (xhr.startTime) {
                const responseTime = performance.now() - xhr.startTime;
                performanceMetrics.totalResponseTime += responseTime;

                // Log slow requests
                if (responseTime > 3000) {
                    console.warn('Slow AJAX request:', settings.url, responseTime + 'ms');
                }
            }
        });

        $(document).ajaxError(function(event, xhr, settings, error) {
            performanceMetrics.errors++;
            console.error('AJAX error:', settings.url, error);
        });

        // Report performance metrics periodically
        setInterval(reportPerformanceMetrics, 60000); // Every minute
    }

    /**
     * Initialize request optimization
     */
    function initRequestOptimization() {
        // Implement request deduplication
        const originalAjax = $.ajax;
        $.ajax = function(options) {
            const cacheKey = JSON.stringify({
                url: options.url,
                data: options.data,
                type: options.type
            });

            // Check cache for GET requests
            if (options.type === 'GET' || options.cache !== false) {
                const cached = getCachedRequest(cacheKey);
                if (cached) {
                    performanceMetrics.cacheHits++;
                    return $.Deferred().resolve(cached).promise();
                }
            }

            // Make request and cache result
            const xhr = originalAjax.call(this, options);

            if (options.type === 'GET' || options.cache !== false) {
                xhr.done(function(data) {
                    setCachedRequest(cacheKey, data);
                });
            }

            return xhr;
        };
    }

    /**
     * Initialize DOM optimization
     */
    function initDOMOptimization() {
        // Use passive event listeners where possible
        const passiveEvents = ['scroll', 'touchstart', 'touchmove', 'wheel'];
        passiveEvents.forEach(function(eventType) {
            document.addEventListener(eventType, function() {}, { passive: true });
        });

        // Optimize image loading
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            });

            // Observe all lazy images
            document.querySelectorAll('img[data-src]').forEach(function(img) {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Initialize select all functionality
     */
    function initSelectAllFunctionality() {
        const $selectAll = $('#select-all');
        const $itemCheckboxes = $('.cart-item-checkbox');
        const $deleteButton = $('#delete-selected-cart-items');

        // Handle select all checkbox
        $selectAll.on('change', function() {
            const isChecked = $(this).is(':checked');
            $itemCheckboxes.prop('checked', isChecked);
            updateDeleteButtonState();
        });

        // Handle individual item checkboxes
        $(document).on('change', '.cart-item-checkbox', function() {
            updateSelectAllState();
            updateDeleteButtonState();
        });

        // Update select all state based on individual checkboxes
        function updateSelectAllState() {
            const totalCheckboxes = $itemCheckboxes.length;
            const checkedCheckboxes = $itemCheckboxes.filter(':checked').length;
            
            $selectAll.prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
            $selectAll.prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
        }

        // Update delete button state
        function updateDeleteButtonState() {
            const checkedCount = $itemCheckboxes.filter(':checked').length;
            $deleteButton.prop('disabled', checkedCount === 0);
            $deleteButton.find('.selected-count').text(`(${checkedCount})`);
        }

        // Initial state update
        updateSelectAllState();
        updateDeleteButtonState();
    }

    /**
     * Initialize quantity controls
     */
    function initQuantityControls() {
        // Debounced quantity update function
        const debouncedQuantityUpdate = debounce(updateCartQuantity, config.debounceDelay);

        // Handle quantity input changes
        $(document).on('input change', '.qty-input', function() {
            const $input = $(this);
            const newQuantity = parseInt($input.val());
            const cartItemKey = $input.data('cart-item-key');
            
            // Validate quantity
            if (isNaN(newQuantity) || newQuantity < 1) {
                $input.val(1);
                return;
            }

            // Update quantity with debounce
            debouncedQuantityUpdate(cartItemKey, newQuantity, $input);
        });

        // Handle decrease quantity button
        $(document).on('click', '.decrease-qty', function() {
            const $button = $(this);
            const $input = $button.siblings('.qty-input');
            const currentQty = parseInt($input.val());
            
            if (currentQty > 1) {
                $input.val(currentQty - 1).trigger('change');
            }
        });

        // Handle increase quantity button
        $(document).on('click', '.increase-qty', function() {
            const $button = $(this);
            const $input = $button.siblings('.qty-input');
            const currentQty = parseInt($input.val());
            const maxQty = parseInt($input.attr('max')) || 999;
            
            if (currentQty < maxQty) {
                $input.val(currentQty + 1).trigger('change');
            }
        });
    }

    /**
     * Update cart quantity via AJAX
     */
    function updateCartQuantity(cartItemKey, quantity, $input) {
        if (isUpdating) {
            updateQueue.push({ cartItemKey, quantity, $input });
            return;
        }

        isUpdating = true;
        showLoadingState();

        // Add loading state to specific item
        const $productItem = $input.closest('.cart-product-item');
        $productItem.addClass('updating');

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'update_cart_item_quantity',
                cart_item_key: cartItemKey,
                quantity: quantity,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update item totals
                    updateItemTotals(cartItemKey, response.data);
                    
                    // Update cart totals
                    updateCartTotals(response.data.cart_totals);
                    
                    // Show success message
                    showMessage(cartOptimized.success_message, 'success');
                    
                    retryCount = 0;
                } else {
                    handleAjaxError(response.data || cartOptimized.error_message);
                }
            },
            error: function(xhr, status, error) {
                handleAjaxError(error);
            },
            complete: function() {
                $productItem.removeClass('updating');
                hideLoadingState();
                isUpdating = false;
                
                // Process queue
                processUpdateQueue();
            }
        });
    }

    /**
     * Initialize item removal functionality
     */
    function initItemRemoval() {
        // Handle individual item removal
        $(document).on('click', '.remove-item-btn', function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const cartItemKey = $button.data('cart-item-key');
            const productName = $button.data('product-name');
            
            // Show confirmation dialog
            if (confirm(sprintf(cartOptimized.confirm_remove_item || 'Are you sure you want to remove "%s" from your cart?', productName))) {
                removeCartItem(cartItemKey, $button);
            }
        });
    }

    /**
     * Remove cart item via AJAX
     */
    function removeCartItem(cartItemKey, $button) {
        const $productItem = $button.closest('.cart-product-item');
        $productItem.addClass('removing');

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'remove_cart_item',
                cart_item_key: cartItemKey,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Remove item with animation
                    $productItem.fadeOut(300, function() {
                        $(this).remove();
                        
                        // Update cart totals
                        updateCartTotals(response.data.cart_totals);
                        
                        // Check if vendor section is empty
                        checkEmptyVendorSections();
                        
                        // Update select all state
                        updateSelectAllState();
                    });
                    
                    showMessage(response.data.message || 'Item removed from cart', 'success');
                } else {
                    $productItem.removeClass('removing');
                    handleAjaxError(response.data || 'Failed to remove item');
                }
            },
            error: function(xhr, status, error) {
                $productItem.removeClass('removing');
                handleAjaxError(error);
            }
        });
    }

    /**
     * Initialize bulk actions
     */
    function initBulkActions() {
        // Handle bulk delete
        $('#delete-selected-cart-items').on('click', function() {
            const $checkedItems = $('.cart-item-checkbox:checked');
            
            if ($checkedItems.length === 0) {
                return;
            }

            if (confirm(cartOptimized.confirm_remove || 'Are you sure you want to remove selected items?')) {
                const cartItemKeys = $checkedItems.map(function() {
                    return $(this).val();
                }).get();

                removeMultipleCartItems(cartItemKeys);
            }
        });
    }

    /**
     * Remove multiple cart items
     */
    function removeMultipleCartItems(cartItemKeys) {
        showLoadingState();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'remove_selected_cart_items',
                cart_item_keys: cartItemKeys,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Remove items with animation
                    cartItemKeys.forEach(function(key) {
                        $(`.cart-product-item[data-cart-item-key="${key}"]`).fadeOut(300, function() {
                            $(this).remove();
                        });
                    });

                    // Update cart totals
                    updateCartTotals(response.data.cart_totals);
                    
                    // Reset checkboxes
                    $('#select-all').prop('checked', false);
                    
                    showMessage(response.data.message || 'Selected items removed', 'success');
                } else {
                    handleAjaxError(response.data || 'Failed to remove items');
                }
            },
            error: function(xhr, status, error) {
                handleAjaxError(error);
            },
            complete: function() {
                hideLoadingState();
            }
        });
    }

    /**
     * Initialize shipping selection
     */
    function initShippingSelection() {
        // Handle shipping method selection button click
        $(document).on('click', '.shipping-selection-btn', function() {
            const vendorId = $(this).data('vendor');
            $('#shippingModal').data('vendor-id', vendorId);
            showShippingModal(vendorId);
        });

        // Handle confirm shipping method button
        $(document).on('click', '#confirm-shipping-method', function() {
            confirmShippingMethod();
        });

        // Handle modal close - reset state
        $('#shippingModal').on('hidden.bs.modal', function() {
            $(this).removeData('vendor-id');
            $('#shipping-methods-list').empty();
            $('#confirm-shipping-method').prop('disabled', true);
        });
    }

    /**
     * Show shipping method selection modal
     */
    function showShippingModal(vendorId) {
        const $modal = $('#shippingModal');
        const $vendorName = $('#vendor-name');
        const $vendorInitial = $('#vendor-initial');
        const $vendorLocation = $('#vendor-location');
        const $methodsList = $('#shipping-methods-list');
        const $confirmBtn = $('#confirm-shipping-method');

        // Get vendor information
        const $vendorSection = $(`.vendor-cart-section[data-vendor="${vendorId}"]`);
        const vendorName = $vendorSection.find('.vendor-name').text() || 'Unknown Vendor';
        const vendorLocation = $vendorSection.find('.vendor-location').text() || 'Location not specified';

        // Update modal content
        $vendorName.text(vendorName);
        $vendorInitial.text(vendorName.charAt(0).toUpperCase());
        $vendorLocation.text(vendorLocation);

        // Show modal
        $modal.modal('show');

        // Load shipping methods
        loadShippingMethods(vendorId);
    }

    /**
     * Load shipping methods for vendor
     */
    function loadShippingMethods(vendorId) {
        const $loading = $('#shipping-loading');
        const $content = $('#shipping-content');
        const $methodsList = $('#shipping-methods-list');

        $loading.show();
        $content.hide();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'get_vendor_shipping_methods',
                vendor_id: vendorId,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    renderShippingMethods(response.data.methods);
                    $loading.hide();
                    $content.show();
                } else {
                    showShippingError(response.data || 'Failed to load shipping methods');
                }
            },
            error: function(xhr, status, error) {
                showShippingError('Network error occurred');
            }
        });
    }

    /**
     * Render shipping methods in modal
     */
    function renderShippingMethods(methods) {
        const $methodsList = $('#shipping-methods-list');
        const $confirmBtn = $('#confirm-shipping-method');

        if (!methods || methods.length === 0) {
            $methodsList.html(`
                <div class="alert alert-warning">
                    <i data-feather="alert-triangle" class="feather-sm me-2"></i>
                    ${cartOptimized.no_shipping_methods || 'No shipping methods available for this vendor.'}
                </div>
            `);
            return;
        }

        let methodsHtml = '';
        methods.forEach(function(method, index) {
            const isSelected = method.selected || index === 0;
            methodsHtml += `
                <div class="shipping-method-option ${isSelected ? 'selected' : ''}" data-method-id="${method.id}">
                    <div class="method-radio">
                        <input type="radio" name="shipping_method" value="${method.id}"
                               id="method_${method.id}" ${isSelected ? 'checked' : ''}>
                        <label for="method_${method.id}"></label>
                    </div>
                    <div class="method-info">
                        <div class="method-header">
                            <h6 class="method-name">${method.name}</h6>
                            <span class="method-cost">${method.cost}</span>
                        </div>
                        <div class="method-details">
                            <div class="delivery-time">
                                <i data-feather="clock" class="feather-xs me-1"></i>
                                ${method.delivery_time || 'Standard delivery'}
                            </div>
                            ${method.description ? `<div class="method-description">${method.description}</div>` : ''}
                        </div>
                    </div>
                    <div class="method-icon">
                        <i data-feather="${method.icon || 'truck'}" class="feather-lg"></i>
                    </div>
                </div>
            `;
        });

        $methodsList.html(methodsHtml);

        // Enable confirm button if a method is selected
        $confirmBtn.prop('disabled', false);

        // Handle method selection
        $methodsList.on('click', '.shipping-method-option', function() {
            const $option = $(this);
            const $radio = $option.find('input[type="radio"]');

            // Update selection
            $('.shipping-method-option').removeClass('selected');
            $option.addClass('selected');
            $radio.prop('checked', true);

            // Update delivery estimate
            updateDeliveryEstimate($option.data('method-id'), methods);
        });

        // Initialize delivery estimate for selected method
        const selectedMethod = methods.find(m => m.selected) || methods[0];
        if (selectedMethod) {
            updateDeliveryEstimate(selectedMethod.id, methods);
        }
    }

    /**
     * Update delivery estimate
     */
    function updateDeliveryEstimate(methodId, methods) {
        const method = methods.find(m => m.id === methodId);
        const $estimateText = $('#delivery-estimate-text');

        if (method && method.estimate) {
            $estimateText.html(`
                <strong>Estimated Delivery:</strong> ${method.estimate}
                ${method.tracking ? '<br><small>Tracking available</small>' : ''}
            `);
        } else {
            $estimateText.text('Delivery estimate not available');
        }
    }

    /**
     * Show shipping error
     */
    function showShippingError(message) {
        const $loading = $('#shipping-loading');
        const $content = $('#shipping-content');
        const $methodsList = $('#shipping-methods-list');

        $loading.hide();
        $content.show();

        $methodsList.html(`
            <div class="alert alert-danger">
                <i data-feather="alert-circle" class="feather-sm me-2"></i>
                ${message}
            </div>
        `);
    }

    /**
     * Confirm shipping method selection
     */
    function confirmShippingMethod() {
        const selectedMethod = $('input[name="shipping_method"]:checked').val();
        const vendorId = $('#shippingModal').data('vendor-id');

        if (!selectedMethod) {
            showMessage('Please select a shipping method', 'error');
            return;
        }

        showLoadingState();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'update_vendor_shipping_method',
                vendor_id: vendorId,
                shipping_method: selectedMethod,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update shipping display in cart
                    updateVendorShippingDisplay(vendorId, response.data);

                    // Update cart totals
                    updateCartTotals(response.data.cart_totals);

                    // Close modal
                    $('#shippingModal').modal('hide');

                    showMessage('Shipping method updated successfully', 'success');
                } else {
                    showMessage(response.data || 'Failed to update shipping method', 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('Network error occurred', 'error');
            },
            complete: function() {
                hideLoadingState();
            }
        });
    }

    /**
     * Update vendor shipping display in cart
     */
    function updateVendorShippingDisplay(vendorId, data) {
        const $vendorSection = $(`.vendor-cart-section[data-vendor="${vendorId}"]`);
        const $shippingBtn = $vendorSection.find('.shipping-selection-btn');
        const $shippingText = $shippingBtn.find('.shipping-text');
        const $shippingCost = $shippingBtn.find('.shipping-cost');

        if (data.method_name) {
            $shippingText.text(data.method_name);
        }

        if (data.method_cost) {
            $shippingCost.text(data.method_cost);
        }

        // Add visual feedback
        $shippingBtn.addClass('updated');
        setTimeout(function() {
            $shippingBtn.removeClass('updated');
        }, 2000);
    }

    /**
     * Initialize address management
     */
    function initAddressManagement() {
        // Handle saved address selection
        $('#select-saved-address').on('click', function() {
            showSavedAddressModal();
        });

        // Handle address form toggle
        $('#toggle-address-form').on('click', function() {
            $('#address-update-form').slideToggle();
        });

        // Handle address form submission
        $('#cart-address-form').on('submit', function(e) {
            e.preventDefault();
            updateCartAddress($(this));
        });

        // Handle confirm address selection
        $('#confirm-address-selection').on('click', function() {
            confirmAddressSelection();
        });

        // Handle add new address button
        $('#add-new-address-btn').on('click', function() {
            $('#savedAddressModal').modal('hide');
            $('#toggle-address-form').click();
        });

        // Handle modal close - reset state
        $('#savedAddressModal').on('hidden.bs.modal', function() {
            $('#saved-addresses-list').empty();
            $('#confirm-address-selection').prop('disabled', true);
        });
    }

    /**
     * Show saved address selection modal
     */
    function showSavedAddressModal() {
        const $modal = $('#savedAddressModal');

        // Show modal
        $modal.modal('show');

        // Load saved addresses
        loadSavedAddresses();
    }

    /**
     * Load saved addresses for user
     */
    function loadSavedAddresses() {
        const $loading = $('#address-loading');
        const $content = $('#address-content');
        const $addressesList = $('#saved-addresses-list');

        $loading.show();
        $content.hide();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'get_saved_addresses',
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    renderSavedAddresses(response.data.addresses);
                    $loading.hide();
                    $content.show();
                } else {
                    showAddressError(response.data || 'Failed to load saved addresses');
                }
            },
            error: function(xhr, status, error) {
                showAddressError('Network error occurred');
            }
        });
    }

    /**
     * Render saved addresses in modal
     */
    function renderSavedAddresses(addresses) {
        const $addressesList = $('#saved-addresses-list');
        const $confirmBtn = $('#confirm-address-selection');

        if (!addresses || addresses.length === 0) {
            $addressesList.html(`
                <div class="alert alert-info">
                    <i data-feather="info" class="feather-sm me-2"></i>
                    ${cartOptimized.no_saved_addresses || 'No saved addresses found. Add a new address to get started.'}
                </div>
            `);
            return;
        }

        let addressesHtml = '';
        addresses.forEach(function(address, index) {
            const isSelected = address.is_default || index === 0;
            addressesHtml += `
                <div class="saved-address-option ${isSelected ? 'selected' : ''}" data-address-id="${address.id}">
                    <div class="address-radio">
                        <input type="radio" name="saved_address" value="${address.id}"
                               id="address_${address.id}" ${isSelected ? 'checked' : ''}>
                        <label for="address_${address.id}"></label>
                    </div>
                    <div class="address-info">
                        <div class="address-header">
                            <h6 class="address-title">${address.title || 'Address'}</h6>
                            ${address.is_default ? '<span class="default-badge">Default</span>' : ''}
                        </div>
                        <div class="address-details">
                            <div class="address-text">${address.formatted_address}</div>
                            ${address.phone ? `<div class="address-phone"><i data-feather="phone" class="feather-xs me-1"></i>${address.phone}</div>` : ''}
                        </div>
                    </div>
                    <div class="address-actions">
                        <button type="button" class="btn btn-sm btn-outline-secondary edit-address"
                                data-address-id="${address.id}" title="Edit Address">
                            <i data-feather="edit-2" class="feather-xs"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        $addressesList.html(addressesHtml);

        // Enable confirm button if an address is selected
        $confirmBtn.prop('disabled', false);

        // Handle address selection
        $addressesList.on('click', '.saved-address-option', function() {
            const $option = $(this);
            const $radio = $option.find('input[type="radio"]');

            // Update selection
            $('.saved-address-option').removeClass('selected');
            $option.addClass('selected');
            $radio.prop('checked', true);
        });

        // Handle edit address button
        $addressesList.on('click', '.edit-address', function(e) {
            e.stopPropagation();
            const addressId = $(this).data('address-id');
            editSavedAddress(addressId);
        });
    }

    /**
     * Show address error
     */
    function showAddressError(message) {
        const $loading = $('#address-loading');
        const $content = $('#address-content');
        const $addressesList = $('#saved-addresses-list');

        $loading.hide();
        $content.show();

        $addressesList.html(`
            <div class="alert alert-danger">
                <i data-feather="alert-circle" class="feather-sm me-2"></i>
                ${message}
            </div>
        `);
    }

    /**
     * Confirm address selection
     */
    function confirmAddressSelection() {
        const selectedAddress = $('input[name="saved_address"]:checked').val();

        if (!selectedAddress) {
            showMessage('Please select an address', 'error');
            return;
        }

        showLoadingState();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'set_cart_address',
                address_id: selectedAddress,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Close modal
                    $('#savedAddressModal').modal('hide');

                    // Reload page to show updated address
                    location.reload();
                } else {
                    showMessage(response.data || 'Failed to set address', 'error');
                }
            },
            error: function(xhr, status, error) {
                showMessage('Network error occurred', 'error');
            },
            complete: function() {
                hideLoadingState();
            }
        });
    }

    /**
     * Edit saved address
     */
    function editSavedAddress(addressId) {
        // Close modal and redirect to account page
        $('#savedAddressModal').modal('hide');
        window.location.href = cartOptimized.account_url + '#addresses';
    }

    /**
     * Update cart address
     */
    function updateCartAddress($form) {
        const formData = $form.serialize();
        
        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            data: formData + '&action=update_cart_address',
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    $('#address-update-form').slideUp();
                    // Refresh shipping calculations
                    location.reload();
                } else {
                    showMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showMessage(cartOptimized.error_message, 'error');
            }
        });
    }

    /**
     * Initialize coupon functionality
     */
    function initCouponFunctionality() {
        // Apply coupon button
        $('#apply_coupon').on('click', function() {
            const couponCode = $('#coupon_code').val().trim();

            if (!couponCode) {
                showMessage('Please enter a coupon code', 'error');
                return;
            }

            applyCoupon(couponCode);
        });

        // Handle enter key in coupon input
        $('#coupon_code').on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('#apply_coupon').click();
            }
        });

        // Remove coupon functionality
        $(document).on('click', '.remove-coupon', function() {
            const couponCode = $(this).data('coupon');
            removeCoupon(couponCode);
        });

        // Coupon input validation
        $('#coupon_code').on('input', function() {
            const $input = $(this);
            const $button = $('#apply_coupon');
            const value = $input.val().trim();

            if (value.length > 0) {
                $button.prop('disabled', false);
                $input.removeClass('error');
            } else {
                $button.prop('disabled', true);
            }
        });

        // Initialize button state
        const initialValue = $('#coupon_code').val().trim();
        $('#apply_coupon').prop('disabled', initialValue.length === 0);
    }

    /**
     * Apply coupon
     */
    function applyCoupon(couponCode) {
        const $input = $('#coupon_code');
        const $button = $('#apply_coupon');
        const originalText = $button.html();

        // Show loading state
        $button.html('<i class="fas fa-spinner fa-spin"></i> Applying...').prop('disabled', true);
        $input.prop('disabled', true);

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'apply_coupon',
                coupon_code: couponCode,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Clear input
                    $input.val('');

                    // Update cart totals
                    updateCartTotals(response.data.cart_totals);

                    // Show applied coupon
                    displayAppliedCoupon(response.data.coupon);

                    showMessage(response.data.message || 'Coupon applied successfully!', 'success');
                } else {
                    $input.addClass('error');
                    showMessage(response.data || 'Invalid coupon code', 'error');
                }
            },
            error: function() {
                $input.addClass('error');
                showMessage('Error applying coupon', 'error');
            },
            complete: function() {
                $button.html(originalText).prop('disabled', false);
                $input.prop('disabled', false);
            }
        });
    }

    /**
     * Remove coupon
     */
    function removeCoupon(couponCode) {
        showLoadingState();

        $.ajax({
            url: cartOptimized.ajax_url,
            type: 'POST',
            timeout: config.ajaxTimeout,
            data: {
                action: 'remove_coupon',
                coupon_code: couponCode,
                nonce: cartOptimized.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Remove coupon display
                    $(`.applied-coupon[data-coupon="${couponCode}"]`).fadeOut(300, function() {
                        $(this).remove();
                    });

                    // Update cart totals
                    updateCartTotals(response.data.cart_totals);

                    showMessage(response.data.message || 'Coupon removed successfully', 'success');
                } else {
                    showMessage(response.data || 'Failed to remove coupon', 'error');
                }
            },
            error: function() {
                showMessage('Error removing coupon', 'error');
            },
            complete: function() {
                hideLoadingState();
            }
        });
    }

    /**
     * Display applied coupon
     */
    function displayAppliedCoupon(couponData) {
        const $appliedCoupons = $('.applied-coupons');

        if ($appliedCoupons.length === 0) {
            // Create applied coupons container if it doesn't exist
            $('.cart-coupon').append('<div class="applied-coupons mt-3"></div>');
        }

        const couponHtml = `
            <div class="applied-coupon" data-coupon="${couponData.code}">
                <div class="coupon-info">
                    <span class="coupon-code">${couponData.code}</span>
                    <span class="coupon-discount">-${couponData.discount}</span>
                </div>
                <button type="button" class="remove-coupon" data-coupon="${couponData.code}" title="Remove coupon">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        $('.applied-coupons').append(couponHtml);
    }

    /**
     * Initialize form validation
     */
    function initFormValidation() {
        // Validate quantity inputs
        $(document).on('blur', '.qty-input', function() {
            const $input = $(this);
            const value = parseInt($input.val());
            const min = parseInt($input.attr('min')) || 1;
            const max = parseInt($input.attr('max')) || 999;

            if (isNaN(value) || value < min) {
                $input.val(min);
            } else if (value > max) {
                $input.val(max);
            }
        });
    }

    /**
     * Update item totals in the UI
     */
    function updateItemTotals(cartItemKey, data) {
        const $item = $(`.cart-product-item[data-cart-item-key="${cartItemKey}"]`);
        
        if (data.item_total) {
            $item.find('.total-amount').html(data.item_total);
        }
        
        if (data.item_price) {
            $item.find('.price-amount').html(data.item_price);
        }
    }

    /**
     * Update cart totals in the UI
     */
    function updateCartTotals(totals) {
        if (totals) {
            // Update sidebar totals with animation
            if (totals.subtotal) {
                animateValueChange('.cart-subtotal .amount', totals.subtotal);
            }
            if (totals.shipping) {
                animateValueChange('.shipping-total .amount', totals.shipping);
            }
            if (totals.tax) {
                animateValueChange('.tax-total .amount', totals.tax);
            }
            if (totals.discount) {
                if (totals.discount !== '0') {
                    $('.discount-row').show();
                    animateValueChange('.discount-total .amount', totals.discount);
                } else {
                    $('.discount-row').hide();
                }
            }
            if (totals.total) {
                animateValueChange('.cart-total .amount', totals.total);
            }

            // Update vendor-specific totals
            if (totals.vendor_totals) {
                updateVendorTotals(totals.vendor_totals);
            }

            // Update item count
            if (totals.item_count !== undefined) {
                $('.cart-item-count').text(totals.item_count);
            }
        }
    }

    /**
     * Animate value changes for better UX
     */
    function animateValueChange(selector, newValue) {
        const $element = $(selector);

        if ($element.length) {
            $element.addClass('updating');

            setTimeout(function() {
                $element.html(newValue);
                $element.removeClass('updating').addClass('updated');

                setTimeout(function() {
                    $element.removeClass('updated');
                }, 1000);
            }, 200);
        }
    }

    /**
     * Update vendor-specific totals
     */
    function updateVendorTotals(vendorTotals) {
        Object.keys(vendorTotals).forEach(function(vendorId) {
            const vendorData = vendorTotals[vendorId];
            const $vendorSection = $(`.vendor-cart-section[data-vendor="${vendorId}"]`);

            if ($vendorSection.length && vendorData) {
                // Update vendor subtotal
                if (vendorData.subtotal) {
                    $vendorSection.find('.vendor-subtotal').html(vendorData.subtotal);
                }

                // Update vendor shipping cost
                if (vendorData.shipping) {
                    $vendorSection.find('.shipping-cost').html(vendorData.shipping);
                }

                // Update vendor total
                if (vendorData.total) {
                    $vendorSection.find('.vendor-total').html(vendorData.total);
                }
            }
        });
    }

    /**
     * Check for empty vendor sections
     */
    function checkEmptyVendorSections() {
        $('.vendor-cart-section').each(function() {
            const $section = $(this);
            const itemCount = $section.find('.cart-product-item').length;
            
            if (itemCount === 0) {
                $section.fadeOut(300, function() {
                    $(this).remove();
                });
            }
        });
    }

    /**
     * Show loading state
     */
    function showLoadingState() {
        $('#cart-loading').fadeIn(200);
        $('body').addClass('cart-updating');
    }

    /**
     * Hide loading state
     */
    function hideLoadingState() {
        $('#cart-loading').fadeOut(200);
        $('body').removeClass('cart-updating');
    }

    /**
     * Show message to user
     */
    function showMessage(message, type = 'info') {
        const $messages = $('#cart-messages');
        const $alert = $messages.find('.alert');
        
        $alert.removeClass('alert-success alert-error alert-warning alert-info')
              .addClass(`alert-${type}`);
        $alert.find('.message-text').text(message);
        
        $messages.fadeIn(300);
        
        // Auto hide after 5 seconds
        setTimeout(function() {
            $messages.fadeOut(300);
        }, 5000);
    }

    /**
     * Handle AJAX errors
     */
    function handleAjaxError(error) {
        console.error('Cart AJAX error:', error);
        
        if (retryCount < config.maxRetries) {
            retryCount++;
            showMessage(`Error occurred. Retrying... (${retryCount}/${config.maxRetries})`, 'warning');
        } else {
            showMessage(error || cartOptimized.error_message, 'error');
            retryCount = 0;
        }
    }

    /**
     * Process update queue
     */
    function processUpdateQueue() {
        if (updateQueue.length > 0) {
            const nextUpdate = updateQueue.shift();
            updateCartQuantity(nextUpdate.cartItemKey, nextUpdate.quantity, nextUpdate.$input);
        }
    }

    /**
     * Debounce utility function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Simple sprintf implementation
     */
    function sprintf(str, ...args) {
        return str.replace(/%s/g, () => args.shift());
    }

    /**
     * Get cached request
     */
    function getCachedRequest(key) {
        const cached = requestCache.get(key);
        if (cached && (Date.now() - cached.timestamp) < config.cacheExpiry) {
            return cached.data;
        }
        return null;
    }

    /**
     * Set cached request
     */
    function setCachedRequest(key, data) {
        requestCache.set(key, {
            data: data,
            timestamp: Date.now()
        });

        // Limit cache size
        if (requestCache.size > 50) {
            const firstKey = requestCache.keys().next().value;
            requestCache.delete(firstKey);
        }
    }

    /**
     * Report performance metrics
     */
    function reportPerformanceMetrics() {
        if (performanceMetrics.ajaxRequests > 0) {
            const avgResponseTime = performanceMetrics.totalResponseTime / performanceMetrics.ajaxRequests;
            const errorRate = (performanceMetrics.errors / performanceMetrics.ajaxRequests) * 100;
            const cacheHitRate = (performanceMetrics.cacheHits / performanceMetrics.ajaxRequests) * 100;

            console.log('Cart Performance Metrics:', {
                totalRequests: performanceMetrics.ajaxRequests,
                avgResponseTime: Math.round(avgResponseTime) + 'ms',
                errorRate: Math.round(errorRate) + '%',
                cacheHitRate: Math.round(cacheHitRate) + '%'
            });

            // Send metrics to server if needed
            if (cartOptimized.performance_endpoint) {
                $.post(cartOptimized.performance_endpoint, {
                    metrics: performanceMetrics,
                    nonce: cartOptimized.nonce
                });
            }
        }
    }

    /**
     * Optimize animations based on device capabilities
     */
    function optimizeAnimation($element, animation) {
        if (prefersReducedMotion || isLowEndDevice) {
            // Skip animations for better performance
            return $element;
        }

        return $element[animation]();
    }

    /**
     * Throttle function for performance
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Request animation frame polyfill
     */
    const requestAnimFrame = (function() {
        return window.requestAnimationFrame ||
               window.webkitRequestAnimationFrame ||
               window.mozRequestAnimationFrame ||
               function(callback) {
                   window.setTimeout(callback, 1000 / 60);
               };
    })();

    /**
     * Batch DOM updates for better performance
     */
    function batchDOMUpdates(updates) {
        requestAnimFrame(function() {
            updates.forEach(function(update) {
                update();
            });
        });
    }

    /**
     * Memory cleanup
     */
    function cleanupMemory() {
        // Clear old cache entries
        const now = Date.now();
        for (let [key, value] of requestCache.entries()) {
            if ((now - value.timestamp) > config.cacheExpiry) {
                requestCache.delete(key);
            }
        }

        // Clear update queue if too large
        if (updateQueue.length > 10) {
            updateQueue = updateQueue.slice(-5);
        }
    }

    // Run memory cleanup periodically
    setInterval(cleanupMemory, 300000); // Every 5 minutes

    /**
     * Handle close message button
     */
    $(document).on('click', '.cart-messages .btn-close', function() {
        $('#cart-messages').fadeOut(300);
    });

    // Initialize when document is ready
    $(document).ready(function() {
        initCart();
    });

    // Expose functions globally for external use
    window.cartOptimized = window.cartOptimized || {};
    window.cartOptimized.functions = {
        showMessage: showMessage,
        showLoadingState: showLoadingState,
        hideLoadingState: hideLoadingState,
        updateCartQuantity: updateCartQuantity,
        removeCartItem: removeCartItem
    };

})(jQuery);
