<?php
/**
 * Address Card Functions
 * 
 * Functions to generate address cards with the modern design
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate an address card HTML
 * 
 * @param array $address_data Address information
 * @param array $options Display options
 * @return string HTML for the address card
 */
function tendeal_generate_address_card($address_data, $options = array()) {
    // Default options
    $defaults = array(
        'show_actions' => false,
        'show_default_badge' => true,
        'compact' => false,
        'card_class' => '',
        'edit_url' => '',
        'delete_action' => '',
        'set_default_action' => ''
    );
    
    $options = wp_parse_args($options, $defaults);
    
    // Build CSS classes
    $card_classes = array('address-card');
    if ($options['compact']) {
        $card_classes[] = 'compact';
    }
    if ($options['show_actions']) {
        $card_classes[] = 'address-card-with-actions';
    }
    if (!empty($options['card_class'])) {
        $card_classes[] = $options['card_class'];
    }
    
    $html = '<div class="' . esc_attr(implode(' ', $card_classes)) . '" data-address-id="' . esc_attr($address_data['id'] ?? '') . '">';
    
    // Default badge
    if ($options['show_default_badge'] && !empty($address_data['is_default'])) {
        $html .= '<div class="address-default-badge">';
        $html .= '<i data-feather="check-circle" class="icon"></i>';
        $html .= esc_html__('Default', 'tendeal');
        $html .= '</div>';
    }
    
    // Action buttons
    if ($options['show_actions']) {
        $html .= '<div class="address-card-actions">';
        
        if (!empty($options['edit_url'])) {
            $html .= '<a href="' . esc_url($options['edit_url']) . '" class="address-action-btn" title="' . esc_attr__('Edit Address', 'tendeal') . '">';
            $html .= '<i data-feather="edit-2"></i>';
            $html .= '</a>';
        }
        
        if (!empty($address_data['is_default']) && !empty($options['set_default_action'])) {
            $html .= '<button type="button" class="address-action-btn set-default" data-action="' . esc_attr($options['set_default_action']) . '" title="' . esc_attr__('Set as Default', 'tendeal') . '">';
            $html .= '<i data-feather="star"></i>';
            $html .= '</button>';
        }
        
        if (!empty($options['delete_action'])) {
            $html .= '<button type="button" class="address-action-btn delete" data-action="' . esc_attr($options['delete_action']) . '" title="' . esc_attr__('Delete Address', 'tendeal') . '">';
            $html .= '<i data-feather="trash-2"></i>';
            $html .= '</button>';
        }
        
        $html .= '</div>';
    }
    
    // Header with name
    $html .= '<div class="address-card-header">';
    $html .= '<i data-feather="user" class="address-icon"></i>';
    $html .= '<h3 class="address-name">' . esc_html($address_data['name'] ?? ($address_data['first_name'] . ' ' . $address_data['last_name'])) . '</h3>';
    $html .= '</div>';
    
    // Address details
    $html .= '<div class="address-details">';
    
    // Street address
    if (!empty($address_data['street']) || (!empty($address_data['address_1']))) {
        $street = $address_data['street'] ?? $address_data['address_1'];
        if (!empty($address_data['address_2'])) {
            $street .= ', ' . $address_data['address_2'];
        }
        if (!empty($address_data['city'])) {
            $street .= ', ' . $address_data['city'];
        }
        if (!empty($address_data['state'])) {
            $street .= ', ' . $address_data['state'];
        }
        
        $html .= '<div class="address-item">';
        $html .= '<i data-feather="map-pin" class="address-item-icon"></i>';
        $html .= '<span class="address-item-text address-street">' . esc_html($street) . '</span>';
        $html .= '</div>';
    }
    
    // Postal code
    if (!empty($address_data['postal_code']) || !empty($address_data['postcode'])) {
        $postal_code = $address_data['postal_code'] ?? $address_data['postcode'];
        $html .= '<div class="address-item">';
        $html .= '<i data-feather="mail" class="address-item-icon"></i>';
        $html .= '<span class="address-item-text">Zip Code ' . esc_html($postal_code) . '</span>';
        $html .= '</div>';
    }
    
    // Country
    if (!empty($address_data['country'])) {
        $country_name = $address_data['country'];
        
        // Convert country code to name if needed
        if (strlen($country_name) === 2 && function_exists('WC')) {
            $countries = WC()->countries->get_countries();
            $country_name = $countries[$country_name] ?? $country_name;
        }
        
        $html .= '<div class="address-item">';
        $html .= '<i data-feather="flag" class="address-item-icon"></i>';
        $html .= '<span class="address-item-text address-country">' . esc_html($country_name) . '</span>';
        $html .= '</div>';
    }
    
    // Phone
    if (!empty($address_data['phone'])) {
        $html .= '<div class="address-item">';
        $html .= '<i data-feather="phone" class="address-item-icon"></i>';
        $html .= '<span class="address-item-text address-phone">' . esc_html($address_data['phone']) . '</span>';
        $html .= '</div>';
    }
    
    $html .= '</div>'; // End address-details
    $html .= '</div>'; // End address-card
    
    return $html;
}

/**
 * Display multiple address cards in a grid
 * 
 * @param array $addresses Array of address data
 * @param array $options Display options
 */
function tendeal_display_address_cards($addresses, $options = array()) {
    if (empty($addresses)) {
        echo '<p class="no-addresses">' . esc_html__('No addresses found.', 'tendeal') . '</p>';
        return;
    }
    
    echo '<div class="address-list">';
    
    foreach ($addresses as $address) {
        echo tendeal_generate_address_card($address, $options);
    }
    
    echo '</div>';
}

/**
 * Generate address form HTML
 * 
 * @param array $address_data Existing address data (for editing)
 * @param array $options Form options
 * @return string HTML for the address form
 */
function tendeal_generate_address_form($address_data = array(), $options = array()) {
    $defaults = array(
        'form_title' => __('Add New Address', 'tendeal'),
        'form_action' => '',
        'form_method' => 'post',
        'show_country_dropdown' => true,
        'required_fields' => array('name', 'street', 'city', 'country'),
        'submit_text' => __('Save Address', 'tendeal'),
        'cancel_url' => ''
    );
    
    $options = wp_parse_args($options, $defaults);
    
    // If editing, change title
    if (!empty($address_data['id'])) {
        $options['form_title'] = __('Edit Address', 'tendeal');
        $options['submit_text'] = __('Update Address', 'tendeal');
    }
    
    $html = '<div class="address-form-container">';
    
    // Form header
    $html .= '<div class="address-form-header">';
    $html .= '<i data-feather="' . (empty($address_data['id']) ? 'plus' : 'edit-2') . '" class="address-icon"></i>';
    $html .= '<h3 class="address-form-title">' . esc_html($options['form_title']) . '</h3>';
    $html .= '</div>';
    
    // Form
    $html .= '<form method="' . esc_attr($options['form_method']) . '"';
    if (!empty($options['form_action'])) {
        $html .= ' action="' . esc_url($options['form_action']) . '"';
    }
    $html .= '>';
    
    // Hidden field for address ID (if editing)
    if (!empty($address_data['id'])) {
        $html .= '<input type="hidden" name="address_id" value="' . esc_attr($address_data['id']) . '">';
    }
    
    // Name and Phone row
    $html .= '<div class="address-form-row">';
    
    $html .= '<div class="address-form-group">';
    $html .= '<label class="address-form-label">' . esc_html__('Full Name', 'tendeal');
    if (in_array('name', $options['required_fields'])) $html .= ' *';
    $html .= '</label>';
    $html .= '<input type="text" name="name" class="address-form-input" value="' . esc_attr($address_data['name'] ?? '') . '" placeholder="' . esc_attr__('Enter full name', 'tendeal') . '"';
    if (in_array('name', $options['required_fields'])) $html .= ' required';
    $html .= '>';
    $html .= '</div>';
    
    $html .= '<div class="address-form-group">';
    $html .= '<label class="address-form-label">' . esc_html__('Phone Number', 'tendeal');
    if (in_array('phone', $options['required_fields'])) $html .= ' *';
    $html .= '</label>';
    $html .= '<input type="tel" name="phone" class="address-form-input" value="' . esc_attr($address_data['phone'] ?? '') . '" placeholder="' . esc_attr__('Enter phone number', 'tendeal') . '"';
    if (in_array('phone', $options['required_fields'])) $html .= ' required';
    $html .= '>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    // Street address
    $html .= '<div class="address-form-row full-width">';
    $html .= '<div class="address-form-group">';
    $html .= '<label class="address-form-label">' . esc_html__('Street Address', 'tendeal');
    if (in_array('street', $options['required_fields'])) $html .= ' *';
    $html .= '</label>';
    $html .= '<input type="text" name="street" class="address-form-input" value="' . esc_attr($address_data['street'] ?? '') . '" placeholder="' . esc_attr__('Enter street address', 'tendeal') . '"';
    if (in_array('street', $options['required_fields'])) $html .= ' required';
    $html .= '>';
    $html .= '</div>';
    $html .= '</div>';
    
    // City and Postal Code row
    $html .= '<div class="address-form-row">';
    
    $html .= '<div class="address-form-group">';
    $html .= '<label class="address-form-label">' . esc_html__('City', 'tendeal');
    if (in_array('city', $options['required_fields'])) $html .= ' *';
    $html .= '</label>';
    $html .= '<input type="text" name="city" class="address-form-input" value="' . esc_attr($address_data['city'] ?? '') . '" placeholder="' . esc_attr__('Enter city', 'tendeal') . '"';
    if (in_array('city', $options['required_fields'])) $html .= ' required';
    $html .= '>';
    $html .= '</div>';
    
    $html .= '<div class="address-form-group">';
    $html .= '<label class="address-form-label">' . esc_html__('Postal Code', 'tendeal');
    if (in_array('postal_code', $options['required_fields'])) $html .= ' *';
    $html .= '</label>';
    $html .= '<input type="text" name="postal_code" class="address-form-input" value="' . esc_attr($address_data['postal_code'] ?? '') . '" placeholder="' . esc_attr__('Enter postal code', 'tendeal') . '"';
    if (in_array('postal_code', $options['required_fields'])) $html .= ' required';
    $html .= '>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    // Country
    if ($options['show_country_dropdown']) {
        $html .= '<div class="address-form-row full-width">';
        $html .= '<div class="address-form-group">';
        $html .= '<label class="address-form-label">' . esc_html__('Country', 'tendeal');
        if (in_array('country', $options['required_fields'])) $html .= ' *';
        $html .= '</label>';
        $html .= '<select name="country" class="address-form-input"';
        if (in_array('country', $options['required_fields'])) $html .= ' required';
        $html .= '>';
        $html .= '<option value="">' . esc_html__('Select Country', 'tendeal') . '</option>';
        
        // Add common countries
        $countries = array(
            'SA' => 'Saudi Arabia',
            'US' => 'United States',
            'GB' => 'United Kingdom',
            'AE' => 'United Arab Emirates',
            'CA' => 'Canada',
            'AU' => 'Australia'
        );
        
        foreach ($countries as $code => $name) {
            $selected = ($address_data['country'] ?? '') === $code ? ' selected' : '';
            $html .= '<option value="' . esc_attr($code) . '"' . $selected . '>' . esc_html($name) . '</option>';
        }
        
        $html .= '</select>';
        $html .= '</div>';
        $html .= '</div>';
    }
    
    // Form actions
    $html .= '<div class="address-form-actions">';
    
    if (!empty($options['cancel_url'])) {
        $html .= '<a href="' . esc_url($options['cancel_url']) . '" class="address-btn address-btn-secondary">';
        $html .= '<i data-feather="x"></i>';
        $html .= esc_html__('Cancel', 'tendeal');
        $html .= '</a>';
    }
    
    $html .= '<button type="submit" class="address-btn address-btn-primary">';
    $html .= '<i data-feather="save"></i>';
    $html .= esc_html($options['submit_text']);
    $html .= '</button>';
    
    $html .= '</div>';
    
    $html .= '</form>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Enqueue address card styles
 */
function tendeal_enqueue_address_card_styles() {
    wp_enqueue_style(
        'tendeal-address-card',
        get_template_directory_uri() . '/css/address-card.css',
        array(),
        '1.0.0'
    );
}

/**
 * Example usage function
 */
function tendeal_address_card_example() {
    // Example address data
    $addresses = array(
        array(
            'id' => 1,
            'name' => 'Achref Maher',
            'street' => 'King Khalid Street, Dammam, Eastern Province',
            'postal_code' => '125456',
            'country' => 'SA',
            'phone' => '+966 03 833 3444',
            'is_default' => true
        ),
        array(
            'id' => 2,
            'name' => 'John Smith',
            'street' => '123 Main Street, New York, NY',
            'postal_code' => '10001',
            'country' => 'US',
            'phone' => '****** 123 4567',
            'is_default' => false
        )
    );
    
    $options = array(
        'show_actions' => true,
        'edit_url' => '#edit',
        'delete_action' => 'delete_address',
        'set_default_action' => 'set_default'
    );
    
    tendeal_enqueue_address_card_styles();
    tendeal_display_address_cards($addresses, $options);
}
