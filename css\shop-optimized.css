/**
 * Optimized Shop Styles for Large Product Catalogs
 * Focused on performance and user experience
 */

/* Shop Container */
.optimized-shop {
    --primary-color: #007cba;
    --secondary-color: #f8f9fa;
    --border-color: #dee2e6;
    --text-color: #333;
    --muted-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}

.shop-container {
    padding: 2rem 0;
    min-height: 60vh;
}

/* Sidebar Filters */
.shop-sidebar {
    background: var(--secondary-color);
    border-radius: 8px;
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.filters-container {
    max-height: 80vh;
    overflow-y: auto;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.filters-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.clear-filters {
    color: var(--danger-color);
    text-decoration: none;
    font-size: 0.875rem;
    padding: 0;
    border: none;
    background: none;
}

.clear-filters:hover {
    text-decoration: underline;
}

/* Filter Groups */
.filter-group {
    margin-bottom: 2rem;
}

.filter-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.filter-options {
    max-height: 200px;
    overflow-y: auto;
}

.filter-option {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-option:hover {
    background-color: rgba(0, 124, 186, 0.1);
    border-radius: 4px;
    padding: 0.25rem;
}

.filter-option input[type="checkbox"],
.filter-option input[type="radio"] {
    margin-right: 0.75rem;
    transform: scale(1.1);
}

.filter-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    font-size: 0.9rem;
    color: var(--text-color);
}

.filter-count {
    color: var(--muted-color);
    font-size: 0.8rem;
    font-weight: 500;
}

/* Price Filter */
.price-filter {
    margin-top: 1rem;
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.price-inputs input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
}

.price-inputs span {
    color: var(--muted-color);
    font-weight: 500;
}

.price-range-display {
    font-size: 0.8rem;
    color: var(--muted-color);
    text-align: center;
}

/* Rating Filter */
.stars {
    display: inline-flex;
    gap: 2px;
}

.star {
    font-size: 0.875rem;
    color: #ddd;
}

.star.filled {
    color: var(--warning-color);
}

/* Filter Actions */
.filter-actions {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Shop Main Content */
.shop-main {
    padding-left: 2rem;
}

/* Shop Header */
.shop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.results-count {
    margin: 0;
    color: var(--muted-color);
    font-size: 0.9rem;
}

.shop-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* View Mode Toggle */
.view-mode-toggle {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.view-mode-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: white;
    color: var(--muted-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-mode-btn:hover,
.view-mode-btn.active {
    background: var(--primary-color);
    color: white;
}

/* Sort Dropdown */
#shop-orderby {
    min-width: 200px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    font-size: 0.875rem;
}

/* Loading Overlay */
.shop-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 8px;
}

.loading-spinner {
    text-align: center;
}

.loading-spinner p {
    margin-top: 1rem;
    color: var(--muted-color);
}

/* Products Container */
.products-container {
    position: relative;
    min-height: 400px;
}

.products-container[data-view="grid"] .products {
    display: flex;
    flex-wrap: wrap;
    margin: -0.75rem;
}

.products-container[data-view="grid"] .products>div {
    padding: 0.75rem;
}

.products-container[data-view="list"] .products {
    display: block;
}

.products-container[data-view="list"] .products>div {
    width: 100% !important;
    margin-bottom: 1rem;
}

/* No Products Found */
.no-products-found {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--secondary-color);
    border-radius: 8px;
}

.no-products-found h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.no-products-found p {
    color: var(--muted-color);
    margin-bottom: 2rem;
}

/* Pagination */
.woocommerce-pagination {
    margin-top: 3rem;
    text-align: center;
}

.woocommerce-pagination ul {
    display: inline-flex;
    list-style: none;
    padding: 0;
    margin: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.woocommerce-pagination li {
    margin: 0;
}

.woocommerce-pagination a,
.woocommerce-pagination span {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    border-right: 1px solid var(--border-color);
    background: white;
    transition: all 0.2s ease;
}

.woocommerce-pagination li:last-child a,
.woocommerce-pagination li:last-child span {
    border-right: none;
}

.woocommerce-pagination a:hover {
    background: var(--primary-color);
    color: white;
}

.woocommerce-pagination .current {
    background: var(--primary-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .shop-main {
        padding-left: 0;
        margin-top: 2rem;
    }

    .shop-sidebar {
        position: static;
        margin-bottom: 2rem;
    }

    .shop-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .shop-controls {
        justify-content: space-between;
    }
}

@media (max-width: 767.98px) {
    .shop-container {
        padding: 1rem 0;
    }

    .shop-sidebar {
        padding: 1rem;
    }

    .filters-container {
        max-height: none;
    }

    .shop-header {
        padding: 0.75rem;
    }

    .shop-controls {
        flex-direction: column;
        gap: 0.75rem;
    }

    #shop-orderby {
        min-width: auto;
        width: 100%;
    }

    .products-container[data-view="grid"] .products>div {
        width: 50% !important;
    }
}

@media (max-width: 575.98px) {
    .products-container[data-view="grid"] .products>div {
        width: 100% !important;
    }

    .view-mode-toggle {
        display: none;
    }
}

/* Performance Optimizations */
.products img {
    loading: lazy;
    transition: transform 0.2s ease;
}

.products img:hover {
    transform: scale(1.05);
}

/* Reduce animations on mobile for better performance */
@media (max-width: 767.98px) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .products img:hover {
        transform: none;
    }
}

/* Focus states for accessibility */
.filter-option:focus-within,
.view-mode-btn:focus,
#shop-orderby:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .shop-sidebar {
        border: 2px solid var(--text-color);
    }

    .filter-option:hover {
        background-color: var(--text-color);
        color: white;
    }
}

/* Optimized Product Card Styles */
.product-card.optimized-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card.optimized-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Product Image Container */
.product-image-container {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
    background: var(--secondary-color);
}

.product-image-link {
    display: block;
    width: 100%;
    height: 100%;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
}

.product-image.hover-image {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

.product-image-container:hover .product-image.hover-image {
    opacity: 1;
}

.product-image-container:hover .product-image.main-image {
    opacity: 0;
}

/* Lazy loading placeholder */
.product-image.lazy {
    background: var(--secondary-color);
}

/* Product Badges */
.product-badges {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sale-badge {
    background: var(--danger-color);
    color: white;
}

.stock-badge.out-of-stock {
    background: var(--muted-color);
    color: white;
}

.stock-badge.backorder {
    background: var(--warning-color);
    color: var(--text-color);
}

/* Product Actions */
.product-actions {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.product-actions .btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
}

.product-actions .btn:hover {
    background: white;
    transform: scale(1.1);
}

/* Product Info */
.product-info {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.product-title-link {
    color: var(--text-color);
    text-decoration: none;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-title-link:hover {
    color: var(--primary-color);
}

/* Product Rating */
.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.stars-container {
    display: flex;
    gap: 1px;
}

.star {
    font-size: 0.875rem;
    color: #ddd;
}

.star.filled {
    color: var(--warning-color);
}

.star.half-filled {
    background: linear-gradient(90deg, var(--warning-color) 50%, #ddd 50%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.rating-count {
    font-size: 0.8rem;
    color: var(--muted-color);
}

.product-rating.no-rating .star {
    color: #e9ecef;
}

/* Product Price */
.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.product-price del {
    color: var(--muted-color);
    font-weight: 400;
    margin-right: 0.5rem;
}

.product-price ins {
    text-decoration: none;
    color: var(--danger-color);
}

/* Add to Cart Button */
.product-add-to-cart {
    margin-top: auto;
}

.add-to-cart-btn {
    position: relative;
    overflow: hidden;
}

.add-to-cart-btn:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.add-to-cart-btn:hover:before {
    width: 300px;
    height: 300px;
}

.add-to-cart-btn i {
    margin-right: 0.5rem;
}

/* List View Styles */
.products-container[data-view="list"] .product-card.optimized-card {
    flex-direction: row;
    height: auto;
}

.products-container[data-view="list"] .product-image-container {
    width: 200px;
    flex-shrink: 0;
    aspect-ratio: 1;
}

.products-container[data-view="list"] .product-info {
    padding: 1.5rem;
}

.products-container[data-view="list"] .product-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
}

.products-container[data-view="list"] .product-actions {
    position: static;
    flex-direction: row;
    opacity: 1;
    transform: none;
    margin-top: 1rem;
}

/* Mobile Optimizations */
@media (max-width: 767.98px) {
    .product-card.optimized-card:hover {
        transform: none;
    }

    .product-actions {
        opacity: 1;
        transform: translateX(0);
    }

    .product-actions .btn {
        width: 32px;
        height: 32px;
    }

    .product-info {
        padding: 0.75rem;
    }

    .product-title {
        font-size: 0.9rem;
    }

    .product-price {
        font-size: 1rem;
    }

    .products-container[data-view="list"] .product-card.optimized-card {
        flex-direction: column;
    }

    .products-container[data-view="list"] .product-image-container {
        width: 100%;
    }
}