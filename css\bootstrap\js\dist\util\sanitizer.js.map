{"version": 3, "file": "sanitizer.js", "sources": ["../../src/util/sanitizer.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n// js-docs-end allow-list\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\n// eslint-disable-next-line unicorn/better-regex\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n"], "names": ["ARIA_ATTRIBUTE_PATTERN", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "dd", "div", "dl", "dt", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "uriAttributes", "Set", "SAFE_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "toLowerCase", "includes", "has", "Boolean", "test", "nodeValue", "filter", "attributeRegex", "RegExp", "some", "regex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFunction", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "elements", "concat", "body", "querySelectorAll", "element", "elementName", "Object", "keys", "remove", "attributeList", "attributes", "allowedAttributes", "removeAttribute", "innerHTML"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMA,sBAAsB,GAAG,gBAAgB,CAAA;AAExC,QAAMC,gBAAgB,GAAG;EAC9B;EACA,EAAA,GAAG,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAED,sBAAsB,CAAC;IACnEE,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EACrCC,EAAAA,IAAI,EAAE,EAAE;EACRC,EAAAA,CAAC,EAAE,EAAE;EACLC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,GAAG,EAAE,EAAE;EACPC,EAAAA,IAAI,EAAE,EAAE;EACRC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,GAAG,EAAE,EAAE;EACPC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,CAAC,EAAE,EAAE;EACLC,EAAAA,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC;EACzDC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,EAAE,EAAE,EAAE;EACNC,EAAAA,CAAC,EAAE,EAAE;EACLC,EAAAA,GAAG,EAAE,EAAE;EACPC,EAAAA,CAAC,EAAE,EAAE;EACLC,EAAAA,KAAK,EAAE,EAAE;EACTC,EAAAA,IAAI,EAAE,EAAE;EACRC,EAAAA,GAAG,EAAE,EAAE;EACPC,EAAAA,GAAG,EAAE,EAAE;EACPC,EAAAA,MAAM,EAAE,EAAE;EACVC,EAAAA,CAAC,EAAE,EAAE;EACLC,EAAAA,EAAE,EAAE,EAAA;EACN,EAAC;EACD;;EAEA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAC5B,YAAY,EACZ,MAAM,EACN,MAAM,EACN,UAAU,EACV,UAAU,EACV,QAAQ,EACR,KAAK,EACL,YAAY,CACb,CAAC,CAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,gBAAgB,GAAG,yDAAyD,CAAA;EAElF,MAAMC,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,oBAAoB,KAAK;IAC5D,MAAMC,aAAa,GAAGF,SAAS,CAACG,QAAQ,CAACC,WAAW,EAAE,CAAA;EAEtD,EAAA,IAAIH,oBAAoB,CAACI,QAAQ,CAACH,aAAa,CAAC,EAAE;EAChD,IAAA,IAAIN,aAAa,CAACU,GAAG,CAACJ,aAAa,CAAC,EAAE;QACpC,OAAOK,OAAO,CAACT,gBAAgB,CAACU,IAAI,CAACR,SAAS,CAACS,SAAS,CAAC,CAAC,CAAA;EAC5D,KAAA;EAEA,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;IACA,OAAOR,oBAAoB,CAACS,MAAM,CAACC,cAAc,IAAIA,cAAc,YAAYC,MAAM,CAAC,CACnFC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACN,IAAI,CAACN,aAAa,CAAC,CAAC,CAAA;EAC7C,CAAC,CAAA;EAEM,SAASa,YAAYA,CAACC,UAAU,EAAEC,SAAS,EAAEC,gBAAgB,EAAE;EACpE,EAAA,IAAI,CAACF,UAAU,CAACG,MAAM,EAAE;EACtB,IAAA,OAAOH,UAAU,CAAA;EACnB,GAAA;EAEA,EAAA,IAAIE,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;MAC9D,OAAOA,gBAAgB,CAACF,UAAU,CAAC,CAAA;EACrC,GAAA;EAEA,EAAA,MAAMI,SAAS,GAAG,IAAIC,MAAM,CAACC,SAAS,EAAE,CAAA;IACxC,MAAMC,eAAe,GAAGH,SAAS,CAACI,eAAe,CAACR,UAAU,EAAE,WAAW,CAAC,CAAA;EAC1E,EAAA,MAAMS,QAAQ,GAAG,EAAE,CAACC,MAAM,CAAC,GAAGH,eAAe,CAACI,IAAI,CAACC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAA;EAEzE,EAAA,KAAK,MAAMC,OAAO,IAAIJ,QAAQ,EAAE;MAC9B,MAAMK,WAAW,GAAGD,OAAO,CAAC1B,QAAQ,CAACC,WAAW,EAAE,CAAA;EAElD,IAAA,IAAI,CAAC2B,MAAM,CAACC,IAAI,CAACf,SAAS,CAAC,CAACZ,QAAQ,CAACyB,WAAW,CAAC,EAAE;QACjDD,OAAO,CAACI,MAAM,EAAE,CAAA;EAChB,MAAA,SAAA;EACF,KAAA;MAEA,MAAMC,aAAa,GAAG,EAAE,CAACR,MAAM,CAAC,GAAGG,OAAO,CAACM,UAAU,CAAC,CAAA;EACtD,IAAA,MAAMC,iBAAiB,GAAG,EAAE,CAACV,MAAM,CAACT,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,EAAEA,SAAS,CAACa,WAAW,CAAC,IAAI,EAAE,CAAC,CAAA;EAEvF,IAAA,KAAK,MAAM9B,SAAS,IAAIkC,aAAa,EAAE;EACrC,MAAA,IAAI,CAACnC,gBAAgB,CAACC,SAAS,EAAEoC,iBAAiB,CAAC,EAAE;EACnDP,QAAAA,OAAO,CAACQ,eAAe,CAACrC,SAAS,CAACG,QAAQ,CAAC,CAAA;EAC7C,OAAA;EACF,KAAA;EACF,GAAA;EAEA,EAAA,OAAOoB,eAAe,CAACI,IAAI,CAACW,SAAS,CAAA;EACvC;;;;;;;;;;;"}