<?php
/**
 * The Template for displaying product archives, including the main shop page which is a post type archive
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/archive-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 8.6.0
 */

defined('ABSPATH') || exit;

// Simply include the dynamic filters template
// This will handle all product archive pages (shop, category, tag, brand)
include_once(get_template_directory() . '/shop-dynamic-filters.php');

// Stop execution to prevent the rest of this file from running
return;
?>

<div class="container shop-container">
  <div class="row">
    <!-- Sidebar with filters -->
    <div class="col-lg-3 shop-sidebar">
      <?php if ($current_category) : ?>
      <div class="current-category-info">
        <h3 class="category-title"><?php echo esc_html($current_category->name); ?></h3>
        <?php if ($current_category->description) : ?>
        <div class="category-description">
          <?php echo wp_kses_post($current_category->description); ?>
        </div>
        <?php endif; ?>
      </div>
      <?php endif; ?>

      <div class="filter-section">
        <h4 class="filter-title">Categories</h4>
        <div class="category-filter">
          <?php
          // If we're on a category page, show child categories
          // Otherwise show top-level categories
          if ($current_category) {
            $args = array(
              'taxonomy' => 'product_cat',
              'hide_empty' => true,
              'parent' => $current_category->term_id
            );
          } else {
            $args = array(
              'taxonomy' => 'product_cat',
              'hide_empty' => true,
              'parent' => 0,
              'exclude' => array(get_option('default_product_cat')) // Exclude "Uncategorized"
            );
          }

          $product_categories = get_terms($args);

          if (!empty($product_categories) && !is_wp_error($product_categories)) {
            echo '<ul class="category-list">';
            foreach ($product_categories as $category) {
              $is_current = $current_category && $current_category->term_id === $category->term_id;
              echo '<li class="category-item' . ($is_current ? ' current' : '') . '">';
              echo '<a href="' . esc_url(get_term_link($category)) . '">' . esc_html($category->name) . ' (' . $category->count . ')</a>';

              // Get subcategories
              $subcategories = get_terms(array(
                'taxonomy' => 'product_cat',
                'hide_empty' => true,
                'parent' => $category->term_id
              ));

              if (!empty($subcategories) && !is_wp_error($subcategories)) {
                echo '<ul class="subcategory-list">';
                foreach ($subcategories as $subcategory) {
                  $is_current_sub = $current_category && $current_category->term_id === $subcategory->term_id;
                  echo '<li class="subcategory-item' . ($is_current_sub ? ' current' : '') . '">';
                  echo '<a href="' . esc_url(get_term_link($subcategory)) . '">' . esc_html($subcategory->name) . ' (' . $subcategory->count . ')</a>';
                  echo '</li>';
                }
                echo '</ul>';
              }

              echo '</li>';
            }
            echo '</ul>';

            // If we're on a subcategory, show a link to go back to parent
            if ($current_category && $current_category->parent) {
              $parent_category = get_term($current_category->parent, 'product_cat');
              echo '<div class="back-to-parent">';
              echo '<a href="' . esc_url(get_term_link($parent_category)) . '"><i class="bi bi-arrow-left"></i> Back to ' . esc_html($parent_category->name) . '</a>';
              echo '</div>';
            }
          } else {
            echo '<p>No product categories found.</p>';
          }
          ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Price Range</h4>
        <div class="price-filter">
          <?php echo do_shortcode('[woocommerce_price_filter]'); ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Brands</h4>
        <div class="brand-filter">
          <?php
          // Get products in the current category to filter brands
          $category_products = array();
          if ($current_category) {
            $args = array(
              'post_type' => 'product',
              'posts_per_page' => -1,
              'fields' => 'ids',
              'tax_query' => array(
                array(
                  'taxonomy' => 'product_cat',
                  'field' => 'term_id',
                  'terms' => $current_category->term_id,
                ),
              ),
            );
            $category_products = get_posts($args);
          }

          // Check if product_brand taxonomy exists (WooCommerce Brands plugin)
          if (taxonomy_exists('product_brand')) {
            $brand_args = array(
              'taxonomy' => 'product_brand',
              'hide_empty' => true
            );

            // If we're on a category page, only show brands that have products in this category
            if ($current_category && !empty($category_products)) {
              $brand_args['object_ids'] = $category_products;
            }

            $brands = get_terms($brand_args);

            if (!empty($brands) && !is_wp_error($brands)) {
              echo '<ul class="brand-list">';
              foreach ($brands as $brand) {
                echo '<li class="brand-item">';
                echo '<input type="checkbox" id="brand-' . esc_attr($brand->slug) . '" name="brand[]" value="' . esc_attr($brand->slug) . '" class="filter-checkbox brand-checkbox">';
                echo '<label for="brand-' . esc_attr($brand->slug) . '">' . esc_html($brand->name) . ' (' . $brand->count . ')</label>';
                echo '</li>';
              }
              echo '</ul>';
            } else {
              echo '<p>No brands found for this category.</p>';
            }
          } else {
            // If product_brand taxonomy doesn't exist, try to use product attributes
            $attribute_taxonomies = wc_get_attribute_taxonomies();
            $brand_attribute = false;

            // Look for a brand-like attribute
            foreach ($attribute_taxonomies as $tax) {
              if (strpos(strtolower($tax->attribute_name), 'brand') !== false) {
                $brand_attribute = $tax->attribute_name;
                break;
              }
            }

            if ($brand_attribute) {
              $taxonomy = 'pa_' . $brand_attribute;
              $brand_args = array(
                'taxonomy' => $taxonomy,
                'hide_empty' => true
              );

              // If we're on a category page, only show brands that have products in this category
              if ($current_category && !empty($category_products)) {
                $brand_args['object_ids'] = $category_products;
              }

              $brands = get_terms($brand_args);

              if (!empty($brands) && !is_wp_error($brands)) {
                echo '<ul class="brand-list">';
                foreach ($brands as $brand) {
                  echo '<li class="brand-item">';
                  echo '<input type="checkbox" id="brand-' . esc_attr($brand->slug) . '" name="brand[]" value="' . esc_attr($brand->slug) . '" class="filter-checkbox brand-checkbox" data-taxonomy="' . esc_attr($taxonomy) . '">';
                  echo '<label for="brand-' . esc_attr($brand->slug) . '">' . esc_html($brand->name) . ' (' . $brand->count . ')</label>';
                  echo '</li>';
                }
                echo '</ul>';
              } else {
                echo '<p>No brands found for this category.</p>';
              }
            } else {
              echo '<p>Brand taxonomy not available.</p>';
            }
          }
          ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Color</h4>
        <div class="color-filter">
          <?php
          // Check if there's a color attribute
          $attribute_taxonomies = wc_get_attribute_taxonomies();
          $color_attribute = false;

          // Look for a color attribute
          foreach ($attribute_taxonomies as $tax) {
            if (strpos(strtolower($tax->attribute_name), 'color') !== false ||
                strpos(strtolower($tax->attribute_name), 'colour') !== false) {
              $color_attribute = $tax->attribute_name;
              break;
            }
          }

          if ($color_attribute) {
            $taxonomy = 'pa_' . $color_attribute;
            $color_args = array(
              'taxonomy' => $taxonomy,
              'hide_empty' => true
            );

            // If we're on a category page, only show colors that have products in this category
            if ($current_category && !empty($category_products)) {
              $color_args['object_ids'] = $category_products;
            }

            $colors = get_terms($color_args);

            if (!empty($colors) && !is_wp_error($colors)) {
              echo '<ul class="color-list">';
              foreach ($colors as $color) {
                // Try to get color code from term meta or use a default
                $color_code = get_term_meta($color->term_id, 'color', true);
                if (!$color_code) {
                  // Try to guess color code from name
                  $color_map = array(
                    'red' => '#ff0000',
                    'blue' => '#0000ff',
                    'green' => '#00ff00',
                    'yellow' => '#ffff00',
                    'black' => '#000000',
                    'white' => '#ffffff',
                    'orange' => '#ffa500',
                    'purple' => '#800080',
                    'pink' => '#ffc0cb',
                    'brown' => '#a52a2a',
                    'gray' => '#808080',
                    'grey' => '#808080',
                  );

                  $color_name = strtolower($color->name);
                  $color_code = isset($color_map[$color_name]) ? $color_map[$color_name] : '#cccccc';
                }

                echo '<li class="color-item">';
                echo '<input type="checkbox" id="color-' . esc_attr($color->slug) . '" name="color[]" value="' . esc_attr($color->slug) . '" class="filter-checkbox color-checkbox" data-taxonomy="' . esc_attr($taxonomy) . '">';
                echo '<label for="color-' . esc_attr($color->slug) . '">';
                echo '<span class="color-swatch" style="background-color: ' . esc_attr($color_code) . '"></span>';
                echo esc_html($color->name) . ' (' . $color->count . ')';
                echo '</label>';
                echo '</li>';
              }
              echo '</ul>';
            } else {
              echo '<p>No colors found for this category.</p>';
            }
          } else {
            echo '<p>Color attribute not available.</p>';
          }
          ?>
        </div>
      </div>

      <?php if (is_active_sidebar('shop-sidebar')) : ?>
      <div class="filter-section">
        <?php dynamic_sidebar('shop-sidebar'); ?>
      </div>
      <?php endif; ?>

      <div class="filter-actions">
        <button id="apply-filters" class="btn btn-primary">Apply Filters</button>
        <button id="reset-filters" class="btn btn-outline-secondary">Reset</button>
      </div>
    </div>

    <!-- Main content area -->
    <div class="col-lg-9 shop-main-content ">
      <div class="shop-header">
        <?php if (woocommerce_product_loop()) : ?>
        <div class="woocommerce-products-header">
          <?php if (apply_filters('woocommerce_show_page_title', true)) : ?>
          <h1 class="woocommerce-products-header__title page-title"><?php woocommerce_page_title(); ?></h1>
          <?php endif; ?>

          <?php
            /**
             * Hook: woocommerce_archive_description.
             *
             * @hooked woocommerce_taxonomy_archive_description - 10
             * @hooked woocommerce_product_archive_description - 10
             */
            do_action('woocommerce_archive_description');
            ?>
        </div>

        <div class="shop-controls">
          <div class="product-count">
            <?php
              $total = wc_get_loop_prop('total');
              $per_page = wc_get_loop_prop('per_page');
              $current = wc_get_loop_prop('current_page');
              $first = (($current - 1) * $per_page) + 1;
              $last = min($total, $current * $per_page);

              if ($total <= $per_page || -1 === $per_page) {
                /* translators: %d: total results */
                printf(_n('Showing %d result', 'Showing all %d results', $total, 'woocommerce'), $total);
              } else {
                /* translators: 1: first result 2: last result 3: total results */
                printf(_nx('Showing %1$d&ndash;%2$d of %3$d result', 'Showing %1$d&ndash;%2$d of %3$d results', $total, 'with first and last result', 'woocommerce'), $first, $last, $total);
              }
              ?>
          </div>

          <div class="shop-sorting">
            <?php
              /**
               * Hook: woocommerce_before_shop_loop.
               *
               * @hooked woocommerce_output_all_notices - 10
               * @hooked woocommerce_result_count - 20
               * @hooked woocommerce_catalog_ordering - 30
               */
              do_action('woocommerce_before_shop_loop');
              ?>
          </div>

          <div class="shop-view-mode">
            <button class="view-mode-btn grid-view active" data-view="grid"><i
                class="bi bi-grid-3x3-gap-fill"></i></button>
            <button class="view-mode-btn list-view" data-view="list"><i class="bi bi-list"></i></button>
          </div>
        </div>
        <?php endif; ?>
      </div>

      <div id="shop-products" class="products-grid">
        <?php
        if (woocommerce_product_loop()) {
          woocommerce_product_loop_start();

          if (wc_get_loop_prop('total')) {
            while (have_posts()) {
              the_post();

              /**
               * Hook: woocommerce_shop_loop.
               */
              do_action('woocommerce_shop_loop');

              // Get the product
              global $product;

              // Make sure we have a valid product object
              if (!is_a($product, 'WC_Product')) {
                continue;
              }

              // Get product categories
              $categories = get_the_terms($product->get_id(), 'product_cat');
              $category_classes = '';
              $category_names = array();

              if (!empty($categories) && !is_wp_error($categories)) {
                foreach ($categories as $category) {
                  $category_classes .= ' category-' . $category->slug;
                  $category_names[] = $category->name;
                }
              }

              // Get product brands
              $brands = array();
              $brand_classes = '';
              $brand_names = array();

              if (taxonomy_exists('product_brand')) {
                $brands = get_the_terms($product->get_id(), 'product_brand');

                if (!empty($brands) && !is_wp_error($brands)) {
                  foreach ($brands as $brand) {
                    $brand_classes .= ' brand-' . $brand->slug;
                    $brand_names[] = $brand->name;
                  }
                }
              }

              // Get product colors
              $colors = array();
              $color_classes = '';
              $color_names = array();

              if (isset($color_attribute) && $color_attribute) {
                $colors = get_the_terms($product->get_id(), 'pa_' . $color_attribute);

                if (!empty($colors) && !is_wp_error($colors)) {
                  foreach ($colors as $color) {
                    $color_classes .= ' color-' . $color->slug;
                    $color_names[] = $color->name;
                  }
                }
              }

              // Get product rating
              $rating = $product->get_average_rating();
              $rating_class = 'rating-' . floor($rating);
              ?>
        <li
          class="product-card<?php echo esc_attr($category_classes . $brand_classes . $color_classes . ' ' . $rating_class); ?>"
          data-price="<?php echo esc_attr($product->get_price()); ?>"
          data-categories="<?php echo esc_attr(implode(',', $category_names)); ?>"
          data-brands="<?php echo esc_attr(implode(',', $brand_names)); ?>"
          data-colors="<?php echo esc_attr(implode(',', $color_names)); ?>"
          data-rating="<?php echo esc_attr(floor($rating)); ?>">
          <div class="product-image">
            <?php if ($product->is_on_sale()) : ?>
            <span class="sale-badge">Sale</span>
            <?php endif; ?>
            <a href="<?php the_permalink(); ?>">
              <?php
                    if (has_post_thumbnail()) {
                      echo woocommerce_get_product_thumbnail();
                    } else {
                      echo '<img src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                    }
                    ?>
            </a>
            <div class="product-actions">
              <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-cart-plus"></i>
              </button>
              <button class="action-btn add-to-wishlist" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-heart"></i>
              </button>
              <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                <i class="bi bi-eye"></i>
              </button>
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-title">
              <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
            </h3>
            <div class="product-rating">
              <?php echo wc_get_rating_html($product->get_average_rating()); ?>
              <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
            </div>
            <div class="product-price">
              <?php echo $product->get_price_html(); ?>
            </div>
            <div class="product-meta">
              <?php if ($product->get_stock_status() === 'instock') : ?>
              <span class="in-stock">In Stock</span>
              <?php else : ?>
              <span class="out-of-stock">Out of Stock</span>
              <?php endif; ?>
              <?php if (!empty($category_names)) : ?>
              <span class="product-categories">Category: <?php echo esc_html($category_names[0]); ?></span>
              <?php endif; ?>
            </div>
            <div class="product-description">
              <?php echo wp_trim_words($product->get_short_description(), 20, '...'); ?>
            </div>
            <div class="list-view-actions">
              <a href="<?php echo esc_url($product->add_to_cart_url()); ?>" class="btn btn-primary add-to-cart-btn">Add
                to Cart</a>
              <a href="<?php the_permalink(); ?>" class="btn btn-outline-secondary view-details-btn">View Details</a>
            </div>
          </div>
        </li>
        <?php
            }
          }

          woocommerce_product_loop_end();

          /**
           * Hook: woocommerce_after_shop_loop.
           *
           * @hooked woocommerce_pagination - 10
           */
          do_action('woocommerce_after_shop_loop');
        } else {
          /**
           * Hook: woocommerce_no_products_found.
           *
           * @hooked wc_no_products_found - 10
           */
          do_action('woocommerce_no_products_found');
        }
        ?>
      </div>
    </div>
  </div>
</div>

<?php
/**
 * Hook: woocommerce_after_main_content.
 *
 * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
 */
do_action('woocommerce_after_main_content');

// We're not using the default sidebar
// do_action('woocommerce_sidebar');

get_footer('shop');
?>