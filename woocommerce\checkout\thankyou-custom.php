<?php
/**
 * Thankyou page
 *
 * This template overrides the default WooCommerce thankyou page
 * with a custom design.
 *
 * @package TenDeal
 */

defined('ABSPATH') || exit;
?>

<div class="checkout-container">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="checkout-form">
          <div class="woocommerce-order">
            <?php if ($order) : ?>
            <?php do_action('woocommerce_before_thankyou', $order->get_id()); ?>

            <?php if ($order->has_status('failed')) : ?>
            <div class="alert alert-danger">
              <p>
                <?php esc_html_e('Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please attempt your purchase again.', 'woocommerce'); ?>
              </p>
              <p>
                <a href="<?php echo esc_url($order->get_checkout_payment_url()); ?>"
                  class="btn btn-primary"><?php esc_html_e('Pay', 'woocommerce'); ?></a>
                <?php if (is_user_logged_in()) : ?>
                <a href="<?php echo esc_url(wc_get_page_permalink('myaccount')); ?>"
                  class="btn btn-secondary"><?php esc_html_e('My account', 'woocommerce'); ?></a>
                <?php endif; ?>
              </p>
            </div>
            <?php else : ?>
            <div class="success-message">
              <div class="success-icon">
                <i data-feather="check"></i>
              </div>
              <h2 class="success-title"><?php esc_html_e('Thank you for your order!', 'woocommerce'); ?></h2>
              <p><?php esc_html_e('Your order has been received and is now being processed.', 'woocommerce'); ?></p>
              <!-- <?php wc_get_template('checkout/order-received-custom.php', array('order' => $order)); ?> -->
            </div>

            <div class="order-details mt-5">
              <h3><?php esc_html_e('Order Details', 'woocommerce'); ?></h3>

              <div class="order-info">
                <div class="row">
                  <div class="col-md-6 col-lg-6 mb-3">
                    <div class="order-info-item">
                      <span class="order-info-label"><?php esc_html_e('Order Number:', 'woocommerce'); ?></span>
                      <span class="order-info-value"><?php echo $order->get_order_number(); ?></span>
                    </div>
                  </div>

                  <div class="col-md-6 col-lg-6 mb-3">
                    <div class="order-info-item">
                      <span class="order-info-label"><?php esc_html_e('Date:', 'woocommerce'); ?></span>
                      <span
                        class="order-info-value"><?php echo wc_format_datetime($order->get_date_created()); ?></span>
                    </div>
                  </div>

                  <?php if (is_user_logged_in() && $order->get_user_id() === get_current_user_id() && $order->get_billing_email()) : ?>
                  <div class="col-md-6 col-lg-6 mb-3">
                    <div class="order-info-item">
                      <span class="order-info-label"><?php esc_html_e('Email:', 'woocommerce'); ?></span>
                      <span class="order-info-value"><?php echo $order->get_billing_email(); ?></span>
                    </div>
                  </div>
                  <?php endif; ?>

                  <div class="col-md-6 col-lg-6 mb-3">
                    <div class="order-info-item">
                      <span class="order-info-label"><?php esc_html_e('Total:', 'woocommerce'); ?></span>
                      <span class="order-info-value"><?php echo $order->get_formatted_order_total(); ?></span>
                    </div>
                  </div>

                  <?php if ($order->get_payment_method_title()) : ?>
                  <div class="col-md-6 col-lg-6 mb-3">
                    <div class="order-info-item">
                      <span class="order-info-label"><?php esc_html_e('Payment Method:', 'woocommerce'); ?></span>
                      <span
                        class="order-info-value"><?php echo wp_kses_post($order->get_payment_method_title()); ?></span>
                    </div>
                  </div>
                  <?php endif; ?>
                </div>
              </div>
            </div>

            <?php if ($order->has_status('processing') && $order->needs_shipping_address()) : ?>
            <div class="shipping-details mt-4">
              <h3><?php esc_html_e('Shipping Details', 'woocommerce'); ?></h3>

              <div class="row">
                <div class="col-md-6">
                  <div class="address-card">
                    <div class="address-card-header">
                      <span class="address-card-title"><?php esc_html_e('Shipping Address', 'woocommerce'); ?></span>
                    </div>
                    <div class="address-card-body">
                      <address>
                        <?php echo wp_kses_post($order->get_formatted_shipping_address(esc_html__('N/A', 'woocommerce'))); ?>
                      </address>
                    </div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="address-card">
                    <div class="address-card-header">
                      <span class="address-card-title"><?php esc_html_e('Billing Address', 'woocommerce'); ?></span>
                    </div>
                    <div class="address-card-body">
                      <address>
                        <?php echo wp_kses_post($order->get_formatted_billing_address(esc_html__('N/A', 'woocommerce'))); ?>
                      </address>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <?php endif; ?>

            <div class="order-items mt-4">
              <h3><?php esc_html_e('Order Items', 'woocommerce'); ?></h3>

              <table class="table">
                <thead>
                  <tr>
                    <th><?php esc_html_e('Product', 'woocommerce'); ?></th>
                    <th><?php esc_html_e('Quantity', 'woocommerce'); ?></th>
                    <th><?php esc_html_e('Price', 'woocommerce'); ?></th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($order->get_items() as $item_id => $item) :
                                                $product = $item->get_product();
                                                // Skip any product-specific operations if the product doesn't exist
                                                ?>

                  <tr>
                    <td> <?php echo wp_kses_post($item->get_name()); ?> </td>
                    <td><?php echo esc_html($item->get_quantity()); ?></td>
                    <td><?php echo wp_kses_post($order->get_formatted_line_subtotal($item)); ?></td>
                  </tr>
                  <?php endforeach; ?>
                </tbody>
                <tfoot>
                  <tr>
                    <th colspan="2"><?php esc_html_e('Subtotal:', 'woocommerce'); ?></th>
                    <td><?php echo wp_kses_post($order->get_subtotal_to_display()); ?></td>
                  </tr>

                  <?php if ($order->get_shipping_method()) : ?>
                  <tr>
                    <th colspan="2"><?php esc_html_e('Shipping:', 'woocommerce'); ?></th>
                    <td><?php echo wp_kses_post($order->get_shipping_to_display()); ?></td>
                  </tr>
                  <?php endif; ?>

                  <?php foreach ($order->get_tax_totals() as $code => $tax) : ?>
                  <tr>
                    <th colspan="2"><?php echo esc_html($tax->label); ?>:</th>
                    <td><?php echo wp_kses_post($tax->formatted_amount); ?></td>
                  </tr>
                  <?php endforeach; ?>

                  <tr>
                    <th colspan="2"><?php esc_html_e('Total:', 'woocommerce'); ?></th>
                    <td><?php echo wp_kses_post($order->get_formatted_order_total()); ?></td>
                  </tr>
                </tfoot>
              </table>
            </div>

            <div class="order-actions mt-4 text-center">
              <?php if (is_user_logged_in()) : ?>
              <a href="<?php echo esc_url(wc_get_page_permalink('myaccount')); ?>"
                class="btn btn-primary"><?php esc_html_e('View My Account', 'woocommerce'); ?></a>
              <?php endif; ?>
              <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>"
                class="btn btn-secondary"><?php esc_html_e('Continue Shopping', 'woocommerce'); ?></a>
            </div>
            <?php endif; ?>

            <!-- <?php do_action('woocommerce_thankyou_' . $order->get_payment_method(), $order->get_id()); ?> -->
            <!-- <?php do_action('woocommerce_thankyou', $order->get_id()); ?> -->
            <?php else : ?>
            <!-- <div class="success-message">
              <div class="success-icon">
                <i data-feather="check"></i>
              </div>
              <h2 class="success-title"><?php esc_html_e('Thank you for your order!', 'woocommerce'); ?></h2>
              <?php wc_get_template('checkout/order-received-custom.php', array('order' => false)); ?>

              <div class="order-actions mt-4 text-center">
                <?php if (is_user_logged_in()) : ?>
                <a href="<?php echo esc_url(wc_get_page_permalink('myaccount')); ?>"
                  class="btn btn-primary"><?php esc_html_e('View My Account', 'woocommerce'); ?></a>
                <?php endif; ?>
                <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>"
                  class="btn btn-secondary"><?php esc_html_e('Continue Shopping', 'woocommerce'); ?></a>
              </div>
            </div> -->
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Initialize Feather icons
document.addEventListener('DOMContentLoaded', function() {
  if (typeof feather !== 'undefined') {
    feather.replace();
  }
});
</script>