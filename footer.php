<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package tendeal
 */

?>

<footer id="colophon" class="site-footer">
  <div class=" text-white pt-5 " style="background-color: #1b1f26;">
    <div class="container footer-bloc pb-4 pt-5">
      <div class="row">

        <div class="col-3 pb-3">
          <div class="col align-content-center pb-4">
            <img src="<?php echo get_template_directory_uri(); ?>/img/tendeal-white.png" class="logo" alt="Footer logo">
          </div>
          <div class="about  justify-content-center" style="color: #667085;">
            <span><?php echo esc_html(get_theme_mod('footer_about_text', 'Tendeal always provides quality products and services via its international e-commerce platform which increases the happiness of global customers.')); ?></span>
          </div>

          <div>
            <?php echo do_shortcode('[language-switcher]'); ?>
          </div>
        </div>

        <div class="col-2">
          <h5>Important links</h5>
          <div class="footer-bloc__list ">
            <?php
            if (has_nav_menu('footer-important')) {
              wp_nav_menu(array(
                'theme_location' => 'footer-important',
                'container' => false,
                'menu_class' => '',
                'fallback_cb' => false,
                'items_wrap' => '<ul>%3$s</ul>',
                'depth' => 1,
              ));
            } else {
              // Fallback if no menu is assigned
              echo '<ul>
                <li><a href="' . esc_url(home_url('/')) . '">Home</a></li>
                <li><a href="' . esc_url(home_url('/about-us/')) . '">About Us</a></li>
                <li><a href="' . esc_url(home_url('/contact-us/')) . '">Contact Us</a></li>
                <li><a href="' . esc_url(home_url('/my-account/')) . '">My Account</a></li>
              </ul>';
            }
            ?>
          </div>
        </div>

        <div class="col-2">
          <h5>Others</h5>
          <div class="footer-bloc__list ">
            <?php
            if (has_nav_menu('footer-others')) {
              wp_nav_menu(array(
                'theme_location' => 'footer-others',
                'container' => false,
                'menu_class' => '',
                'fallback_cb' => false,
                'items_wrap' => '<ul>%3$s</ul>',
                'depth' => 1,
              ));
            } else {
              // Fallback if no menu is assigned
              echo '<ul>
                <li><a href="' . esc_url(home_url('/privacy-policy/')) . '">Privacy Policy</a></li>
                <li><a href="' . esc_url(home_url('/terms-and-conditions/')) . '">Terms and Conditions</a></li>
                <li><a href="' . esc_url(home_url('/faq/')) . '">FAQs</a></li>

              </ul>';
            }
            ?>
          </div>
        </div>

        <div class="col-2">
          <h5>Contact information</h5>
          <div class="footer-bloc__list ">
            <ul>
              <li>
                <i data-feather="map-pin" class="feather-sm"></i>
                <?php echo esc_html(get_theme_mod('footer_address', 'Lusail Marina Tower 50, Floor No. 5, Doha, Qatar')); ?>
              </li>
              <li>
                <i data-feather="phone" class="feather-sm"></i>
                <?php echo esc_html(get_theme_mod('footer_phone', '+97451040008')); ?>
              </li>
              <li>
                <i data-feather="mail" class="feather-sm"></i>
                <?php echo esc_html(get_theme_mod('footer_email', '<EMAIL>')); ?>
              </li>
            </ul>
          </div>
        </div>

        <div class="col-3 form-subscribe">
          <form id="footer-newsletter-form" method="post">
            <div class="mb-3 text-gray">
              <label for="footer-email-input" class="form-label">Email address</label>
              <input type="email" class="form-control" id="footer-email-input" name="email" aria-describedby="emailHelp"
                required>
            </div>
            <div class="mb-3 text-gray">
              <button type="submit" id="footer-subscribe-btn" class="btn btn-primary">Subscribe</button>
            </div>
            <div id="newsletter-response" class="mt-2"></div>
          </form>

        


          <!-- Social Media Icons -->
          <div class="footer-social-media mt-4 d-flex">
            <h5 >Contact Us</h5>
            <div class="social-media-links d-flex">
              <a href="<?php echo esc_url(get_theme_mod('social_facebook', '#')); ?>" class="social-link " target="_blank" rel="noopener" aria-label="Facebook">
                <i data-feather="facebook" class="feather-md"></i>
              </a>
              <a href="<?php echo esc_url(get_theme_mod('social_twitter', '#')); ?>" class="social-link " target="_blank" rel="noopener" aria-label="Twitter">
                <i data-feather="twitter" class="feather-md"></i>
              </a>
              <a href="<?php echo esc_url(get_theme_mod('social_instagram', '#')); ?>" class="social-link " target="_blank" rel="noopener" aria-label="Instagram">
                <i data-feather="instagram" class="feather-md"></i>
              </a>
              <a href="<?php echo esc_url(get_theme_mod('social_linkedin', '#')); ?>" class="social-link " target="_blank" rel="noopener" aria-label="LinkedIn">
                <i data-feather="message-circle" class="feather-sm me-2"></i>
              </a>
        </div>
      </div>
    </div>

    <div class="container pt-4 d-flex justify-content-center" style="border-top: 1px solid var(--bs-gray);">
      <span><?php echo tendeal_get_copyright_text(); ?></span>
    </div>
  </div>
</footer><!-- #colophon -->
</div><!-- #page -->

<?php wp_footer(); ?>

</body>

</html>