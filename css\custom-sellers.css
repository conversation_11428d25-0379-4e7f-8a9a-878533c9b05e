/* ROG Gaming Sellers Page Styles */

.custom-sellers-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
  font-family: 'Roboto', sans-serif;
  color: #333;
}

/* Header Section */
.custom-sellers-header {
  margin-bottom: 40px;
  text-align: center;
}

.custom-breadcrumb {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.custom-breadcrumb a {
  color: #f90;
  text-decoration: none;
  transition: color 0.3s;
}

.custom-breadcrumb a:hover {
  color: #e58300;
}

.custom-breadcrumb .separator {
  margin: 0 8px;
  color: #999;
}

.custom-breadcrumb .current {
  color: #333;
  font-weight: 500;
}

.custom-sellers-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 15px;
  color: #333;
  position: relative;
  display: inline-block;
}

.custom-sellers-title:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #f90;
}

.custom-sellers-description {
  font-size: 18px;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

/* Filters Section */
.custom-sellers-filters {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
}

.custom-search-box {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.custom-search-box input {
  width: 100%;
  padding: 12px 50px 12px 20px;
  border: 1px solid #ddd;
  border-radius: 30px;
  font-size: 16px;
  transition: all 0.3s;
  outline: none;
}

.custom-search-box input:focus {
  border-color: #f90;
  box-shadow: 0 0 0 2px rgba(255, 153, 0, 0.2);
}

.custom-search-box button {
  position: absolute;
  right: 5px;
  top: 5px;
  width: 40px;
  height: 40px;
  border: none;
  background-color: #f90;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s;
}

.custom-search-box button:hover {
  background-color: #e58300;
}

/* Sellers Grid */
.custom-sellers-grid {
  margin-bottom: 40px;
}

.wcfmmp-store-search-form {
  display: none;
}

/* Override WCFM Styles */
.wcfmmp-store-wrap {
  margin: 0 !important;
  padding: 0 !important;
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
  gap: 30px !important;
  list-style: none !important;
}

.wcfmmp-store-wrap li {
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  width: 100% !important;
  background-color: #fff;
  border-radius: 10px !important;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.wcfmmp-store-wrap li:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}



.wcfmmp-store-wrap .wcfmmp-store-wrap-content {
  display: flex !important;
  flex-direction: column !important;
  height: 100%;
}

/* Store Banner */
.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-banner {
  height: 150px !important;
  overflow: hidden;
  position: relative;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-banner img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

/* Store Avatar */
.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-avatar {
  width: 80px !important;
  height: 80px !important;
  border-radius: 50% !important;
  border: 4px solid #fff !important;
  margin: -40px auto 0 !important;
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-color: #fff;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-avatar img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

/* Store Info */
.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-data {
  padding: 20px !important;
  text-align: center !important;
  flex-grow: 1;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-data .store-name {
  font-size: 18px !important;
  font-weight: 700 !important;
  margin-bottom: 10px !important;
  color: #333 !important;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-data .store-name a {
  color: #333 !important;
  text-decoration: none !important;
  transition: color 0.3s !important;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-data .store-name a:hover {
  color: #f90 !important;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-data .store-rating {
  margin-bottom: 15px !important;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-data .store-rating .star-rating {
  float: none !important;
  display: inline-block !important;
  color: #f90 !important;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-data .store-desc {
  font-size: 14px !important;
  color: #666 !important;
  margin-bottom: 15px !important;
  line-height: 1.5 !important;
  max-height: 63px !important;
  overflow: hidden !important;
}

/* Store Footer */
.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-footer {
  padding: 15px 20px !important;
  border-top: 1px solid #eee !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  background-color: #f9f9f9 !important;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-footer .store-products-count {
  font-size: 14px !important;
  color: #666 !important;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-footer .store-visit {
  background-color: #f90 !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 8px 15px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
  text-decoration: none !important;
  display: inline-block !important;
}

.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-footer .store-visit:hover {
  background-color: #e58300 !important;
}

/* Hide Unnecessary Elements */
.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-phone,
.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-address,
.wcfmmp-store-wrap .wcfmmp-store-wrap-content .store-enquiry {
  display: none !important;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .wcfmmp-store-wrap {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
  }
}

@media (max-width: 768px) {
  .custom-sellers-title {
    font-size: 28px;
  }

  .custom-sellers-description {
    font-size: 16px;
  }

  .wcfmmp-store-wrap {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
    gap: 20px !important;
  }
}

@media (max-width: 576px) {
  .custom-sellers-container {
    padding: 20px 15px;
  }

  .wcfmmp-store-wrap {
    grid-template-columns: 1fr !important;
  }
}