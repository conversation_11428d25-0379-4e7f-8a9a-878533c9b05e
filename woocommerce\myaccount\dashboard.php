<?php
/**
 * My Account Dashboard
 *
 * Shows the custom dashboard for the account page.
 *
 * This template overrides the WooCommerce default dashboard.php template.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 4.4.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

$current_user = wp_get_current_user();

// Get order counts
$customer_orders = wc_get_orders( array(
    'customer' => get_current_user_id(),
    'limit'    => -1,
) );

// Initialize counters
$shipped_count = 0;
$confirmed_count = 0;
$shipping_count = 0;
$returns_count = 0;
$canceled_count = 0;

// Count orders by status
foreach ( $customer_orders as $order ) {
    $status = $order->get_status();

    if ( $status === 'completed' ) {
        $shipped_count++;
    } elseif ( $status === 'processing' ) {
        $confirmed_count++;
    } elseif ( $status === 'on-hold' ) {
        $shipping_count++;
    } elseif ( $status === 'refunded' ) {
        $returns_count++;
    } elseif ( $status === 'cancelled' ) {
        $canceled_count++;
    }
}

// Get cart count
$cart_count = WC()->cart ? WC()->cart->get_cart_contents_count() : 0;

// Get wishlist count (if YITH Wishlist is active)
$wishlist_count = 0;
if ( function_exists( 'YITH_WCWL' ) ) {
    // Use the new recommended function for YITH WooCommerce Wishlist 4.0.0+
    if ( class_exists( 'YITH_WCWL_Wishlist_Factory' ) && method_exists( 'YITH_WCWL_Wishlist_Factory', 'get_current_wishlist' ) ) {
        $wishlist = YITH_WCWL_Wishlist_Factory::get_current_wishlist();
        if ( $wishlist ) {
            $wishlist_count = $wishlist->count_items();
        }
    } elseif ( class_exists( 'YITH_WCWL_Wishlist' ) && method_exists( 'YITH_WCWL_Wishlist', 'count_items' ) ) {
        $wishlist_count = YITH_WCWL_Wishlist::count_items();
    } elseif ( class_exists( 'YITH_WCWL_Wishlists' ) && method_exists( 'YITH_WCWL_Wishlists', 'count_items_in_wishlist' ) ) {
        // Use the recommended replacement function
        $wishlist_count = YITH_WCWL_Wishlists::count_items_in_wishlist();
    } else {
        // Fallback to the deprecated function with a suppressed warning
        $wishlist_count = @YITH_WCWL()->count_products();
    }
}

// Get ordered products count
$ordered_count = 0;
foreach ( $customer_orders as $order ) {
    if ( $order->get_status() !== 'cancelled' && $order->get_status() !== 'refunded' ) {
        $ordered_count += count( $order->get_items() );
    }
}

$allowed_html = array(
	'a' => array(
		'href' => array(),
	),
);
?>

<div class="account-dashboard">
  <div class="dashboard-header">
    <h2><?php esc_html_e( 'Overview', 'tendeal' ); ?></h2>
  </div>

  <!-- Status Cards -->
  <div class="status-cards">
    <div class="status-card">
      <div class="status-icon shipped">
        <i data-feather="package" class="feather-md"></i>
      </div>
      <div class="status-content">
        <h3><?php esc_html_e( 'Shipped', 'tendeal' ); ?></h3>
        <div class="status-count"><?php echo esc_html( $shipped_count ); ?></div>
      </div>
    </div>

    <div class="status-card">
      <div class="status-icon confirmed">
        <i data-feather="check-circle" class="feather-md"></i>
      </div>
      <div class="status-content">
        <h3><?php esc_html_e( 'Confirmed', 'tendeal' ); ?></h3>
        <div class="status-count"><?php echo esc_html( $confirmed_count ); ?></div>
      </div>
    </div>

    <div class="status-card">
      <div class="status-icon shipping">
        <i data-feather="truck" class="feather-md"></i>
      </div>
      <div class="status-content">
        <h3><?php esc_html_e( 'Shipping', 'tendeal' ); ?></h3>
        <div class="status-count"><?php echo esc_html( $shipping_count ); ?></div>
      </div>
    </div>

    <div class="status-card">
      <div class="status-icon returns">
        <i data-feather="rotate-ccw" class="feather-md"></i>
      </div>
      <div class="status-content">
        <h3><?php esc_html_e( 'Returns', 'tendeal' ); ?></h3>
        <div class="status-count"><?php echo esc_html( $returns_count ); ?></div>
      </div>
    </div>

    <div class="status-card">
      <div class="status-icon canceled">
        <i data-feather="x-circle" class="feather-md"></i>
      </div>
      <div class="status-content">
        <h3><?php esc_html_e( 'Canceled', 'tendeal' ); ?></h3>
        <div class="status-count"><?php echo esc_html( $canceled_count ); ?></div>
      </div>
    </div>
  </div>

  <!-- Wallet and Points -->
  <!-- <div class="account-stats">
    <div class="wallet-card">
      <div class="wallet-header">
        <i data-feather="credit-card" class="feather-md"></i>
        <span><?php esc_html_e( 'Wallet', 'tendeal' ); ?></span>
      </div>
      <div class="wallet-amount">
        <span class="amount">200</span>
        <span class="currency">USD</span>
      </div>
    </div>

    <div class="points-card">
      <div class="points-header">
        <i data-feather="award" class="feather-md"></i>
        <span><?php esc_html_e( 'Total points', 'tendeal' ); ?></span>
      </div>
      <div class="points-amount">
        <span class="amount">5000</span>
        <span class="label">Points</span>
      </div>
      <div class="points-info">
        <span>5 pts = 1 USD</span>
      </div>
    </div>
  </div> -->

  <!-- Products Summary -->
  <div class="products-summary">
    <div class="product-summary-card">
      <div class="product-summary-icon cart">
        <i data-feather="shopping-cart" class="feather-md"></i>
      </div>
      <div class="product-summary-count"><?php echo esc_html( $cart_count ); ?></div>
      <div class="product-summary-label"><?php esc_html_e( 'Products', 'tendeal' ); ?></div>
      <div class="product-summary-sublabel"><?php esc_html_e( 'in cart', 'tendeal' ); ?></div>
    </div>

    <div class="product-summary-card">
      <div class="product-summary-icon wishlist">
        <i data-feather="heart" class="feather-md"></i>
      </div>
      <div class="product-summary-count"><?php echo esc_html( $wishlist_count ); ?></div>
      <div class="product-summary-label"><?php esc_html_e( 'Products', 'tendeal' ); ?></div>
      <div class="product-summary-sublabel"><?php esc_html_e( 'in wishlist', 'tendeal' ); ?></div>
    </div>

    <div class="product-summary-card">
      <div class="product-summary-icon ordered">
        <i data-feather="package" class="feather-md"></i>
      </div>
      <div class="product-summary-count"><?php echo esc_html( $ordered_count ); ?></div>
      <div class="product-summary-label"><?php esc_html_e( 'Products', 'tendeal' ); ?></div>
      <div class="product-summary-sublabel"><?php esc_html_e( 'Ordered', 'tendeal' ); ?></div>
    </div>
  </div>

  <!-- Wallet and Points Details -->
  <!-- <div class="account-details">
    <div class="wallet-details">
      <div class="wallet-details-header">
        <i data-feather="credit-card" class="feather-md"></i>
        <span><?php esc_html_e( 'Wallet', 'tendeal' ); ?></span>
      </div>
      <div class="wallet-details-amount">
        <span class="amount">200</span>
        <span class="currency">USD</span>
      </div>
    </div>

    <div class="points-details">
      <div class="points-details-header">
        <i data-feather="award" class="feather-md"></i>
        <span><?php esc_html_e( 'Points', 'tendeal' ); ?></span>
      </div>
      <div class="points-details-amount">
        <span class="amount">5000</span>
        <span class="label">Pts</span>
      </div>
    </div>
  </div> -->

  <!-- Wishlist Section -->
  <div class="wishlist-section">
    <div class="section-header">
      <h3><?php esc_html_e( 'Wishlist', 'tendeal' ); ?></h3>
      <a href="<?php echo esc_url( wc_get_endpoint_url( 'wishlist' ) ); ?>"
        class="view-all"><?php esc_html_e( 'All wishlist', 'tendeal' ); ?> →</a>
    </div>

    <div class="wishlist-products">
      <?php
            // Display wishlist items if YITH Wishlist is active
            if ( function_exists( 'YITH_WCWL' ) ) {
                $wishlist_items = array();

                // Get wishlist items using the appropriate method based on YITH Wishlist version
                if ( class_exists( 'YITH_WCWL_Wishlist_Factory' ) && method_exists( 'YITH_WCWL_Wishlist_Factory', 'get_current_wishlist' ) ) {
                    $wishlist = YITH_WCWL_Wishlist_Factory::get_current_wishlist();
                    if ( $wishlist ) {
                        $wishlist_items = $wishlist->get_items();
                    }
                } else {
                    // Fallback to the old method with suppressed warning
                    $wishlist_items = @YITH_WCWL()->get_products();
                }

                $count = 0;

                if ( ! empty( $wishlist_items ) ) {
                    foreach ( $wishlist_items as $item ) {
                        if ( $count >= 4 ) break; // Show only 4 items

                        // Handle both new and old item format
                        $product_id = isset($item['prod_id']) ? $item['prod_id'] : $item->get_product_id();
                        $product = wc_get_product( $product_id );
                        if ( ! $product ) continue;

                        // Get date added (compatible with both formats)
                        $date_added = isset($item['dateadded']) ? $item['dateadded'] : $item->get_date_added();

                        ?>
      <div class="wishlist-product">
        <div class="product-image">
          <?php echo $product->get_image(); ?>
        </div>
        <div class="product-info">
          <h4><?php echo esc_html( $product->get_name() ); ?></h4>
          <div class="product-price"><?php echo $product->get_price_html(); ?></div>
          <div class="product-meta">
            <span
              class="product-date"><?php echo esc_html( date_i18n( get_option( 'date_format' ), strtotime( $date_added ) ) ); ?></span>
          </div>
        </div>
        <div class="product-actions">
          <a href="<?php echo esc_url( $product->add_to_cart_url() ); ?>"
            class="button add-to-cart"><?php esc_html_e( 'Add to cart', 'tendeal' ); ?></a>
        </div>
      </div>
      <?php
                        $count++;
                    }
                } else {
                    echo '<p>' . esc_html__( 'No products in your wishlist yet.', 'tendeal' ) . '</p>';
                }
            } else {
                echo '<p>' . esc_html__( 'Wishlist feature is not available.', 'tendeal' ) . '</p>';
            }
            ?>
    </div>
  </div>

  <!-- Orders Section -->
  <div class="orders-section">
    <div class="section-header">
      <h3><?php esc_html_e( 'Orders', 'tendeal' ); ?></h3>
      <a href="<?php echo esc_url( wc_get_endpoint_url( 'orders' ) ); ?>"
        class="view-all"><?php esc_html_e( 'All orders', 'tendeal' ); ?> →</a>
    </div>

    <div class="recent-orders">
      <?php
            $recent_orders = wc_get_orders( array(
                'customer' => get_current_user_id(),
                'limit'    => 4,
                'orderby'  => 'date',
                'order'    => 'DESC',
            ) );

            if ( ! empty( $recent_orders ) ) {
                foreach ( $recent_orders as $order ) {
                    $items = $order->get_items();
                    if ( empty( $items ) ) continue;

                    // Get the first item
                    $item = reset( $items );
                    $product_id = $item->get_product_id();
                    $product = wc_get_product( $product_id );

                    if ( ! $product ) continue;

                    ?>
      <div class="order-item">
        <div class="product-image">
          <?php echo $product->get_image(); ?>
        </div>
        <div class="order-info">
          <h4><?php echo esc_html( $product->get_name() ); ?></h4>
          <div class="order-meta">
            <span
              class="order-number"><?php echo esc_html__( 'Order ID: ', 'tendeal' ) . $order->get_order_number(); ?></span>
            <span
              class="order-date"><?php echo esc_html( $order->get_date_created()->date_i18n( get_option( 'date_format' ) ) ); ?></span>
          </div>
          <div class="order-status <?php echo esc_attr( $order->get_status() ); ?>">
            <?php echo esc_html( wc_get_order_status_name( $order->get_status() ) ); ?>
          </div>
        </div>
        <div class="order-actions">
          <a href="<?php echo esc_url( $order->get_view_order_url() ); ?>"
            class="button view-order"><?php esc_html_e( 'View order', 'tendeal' ); ?></a>
        </div>
      </div>
      <?php
                }
            } else {
                echo '<p>' . esc_html__( 'No orders have been placed yet.', 'tendeal' ) . '</p>';
            }
            ?>
    </div>
  </div>

  <!-- Tenders Section -->
  <div class="tenders-section">
    <div class="section-header">
      <h3><?php esc_html_e( 'Tenders', 'tendeal' ); ?></h3>
      <a href="#" class="view-all"><?php esc_html_e( 'View all', 'tendeal' ); ?> →</a>
    </div>

    <div class="tenders-list">
      <!-- Example Tender Items -->
      <div class="tender-item">
        <div class="tender-icon">
          <i data-feather="settings" class="feather-md"></i>
        </div>
        <div class="tender-info">
          <h4><?php esc_html_e( 'Oil well drilling project', 'tendeal' ); ?></h4>
          <div class="tender-meta">
            <span class="tender-category"><?php esc_html_e( 'Energy and oil services', 'tendeal' ); ?></span>
          </div>
          <div class="tender-dates">
            <span class="tender-date"><?php esc_html_e( '2023-02-01', 'tendeal' ); ?></span>
            <span class="tender-date"><?php esc_html_e( '2023-05-30', 'tendeal' ); ?></span>
          </div>
        </div>
        <div class="tender-stats">
          <div class="tender-stat">
            <span class="stat-label"><?php esc_html_e( 'Industrial', 'tendeal' ); ?></span>
            <span class="stat-value"><?php esc_html_e( 'Actual value', 'tendeal' ); ?></span>
          </div>
          <div class="tender-stat">
            <span class="stat-label"><?php esc_html_e( '# of participants', 'tendeal' ); ?></span>
            <span class="stat-value">56</span>
          </div>
        </div>
        <div class="tender-actions">
          <a href="#" class="button view-more"><?php esc_html_e( 'View more', 'tendeal' ); ?></a>
        </div>
      </div>

      <div class="tender-item">
        <div class="tender-icon">
          <i data-feather="settings" class="feather-md"></i>
        </div>
        <div class="tender-info">
          <h4><?php esc_html_e( 'Oil well drilling project', 'tendeal' ); ?></h4>
          <div class="tender-meta">
            <span class="tender-category"><?php esc_html_e( 'Energy and oil services', 'tendeal' ); ?></span>
          </div>
          <div class="tender-dates">
            <span class="tender-date"><?php esc_html_e( '2023-02-01', 'tendeal' ); ?></span>
            <span class="tender-date"><?php esc_html_e( '2023-05-30', 'tendeal' ); ?></span>
          </div>
        </div>
        <div class="tender-stats">
          <div class="tender-stat">
            <span class="stat-label"><?php esc_html_e( 'Industrial', 'tendeal' ); ?></span>
            <span class="stat-value"><?php esc_html_e( 'Actual value', 'tendeal' ); ?></span>
          </div>
          <div class="tender-stat">
            <span class="stat-label"><?php esc_html_e( '# of participants', 'tendeal' ); ?></span>
            <span class="stat-value">56</span>
          </div>
        </div>
        <div class="tender-actions">
          <a href="#" class="button view-more"><?php esc_html_e( 'View more', 'tendeal' ); ?></a>
        </div>
      </div>

      <div class="tender-item">
        <div class="tender-icon">
          <i data-feather="shopping-bag" class="feather-md"></i>
        </div>
        <div class="tender-info">
          <h4><?php esc_html_e( 'Renting shops', 'tendeal' ); ?></h4>
          <div class="tender-meta">
            <span class="tender-category"><?php esc_html_e( 'Real estate and leasing services', 'tendeal' ); ?></span>
          </div>
          <div class="tender-dates">
            <span class="tender-date"><?php esc_html_e( '2023-03-01', 'tendeal' ); ?></span>
            <span class="tender-date"><?php esc_html_e( '2023-06-30', 'tendeal' ); ?></span>
          </div>
        </div>
        <div class="tender-stats">
          <div class="tender-stat">
            <span class="stat-label"><?php esc_html_e( 'Industrial', 'tendeal' ); ?></span>
            <span class="stat-value"><?php esc_html_e( 'Actual value', 'tendeal' ); ?></span>
          </div>
          <div class="tender-stat">
            <span class="stat-label"><?php esc_html_e( '# of participants', 'tendeal' ); ?></span>
            <span class="stat-value">56</span>
          </div>
        </div>
        <div class="tender-actions">
          <a href="#" class="button view-more"><?php esc_html_e( 'View more', 'tendeal' ); ?></a>
        </div>
      </div>

      <div class="tender-item">
        <div class="tender-icon">
          <i data-feather="shopping-bag" class="feather-md"></i>
        </div>
        <div class="tender-info">
          <h4><?php esc_html_e( 'Renting shops', 'tendeal' ); ?></h4>
          <div class="tender-meta">
            <span class="tender-category"><?php esc_html_e( 'Real estate and leasing services', 'tendeal' ); ?></span>
          </div>
          <div class="tender-dates">
            <span class="tender-date"><?php esc_html_e( '2023-03-01', 'tendeal' ); ?></span>
            <span class="tender-date"><?php esc_html_e( '2023-06-30', 'tendeal' ); ?></span>
          </div>
        </div>
        <div class="tender-stats">
          <div class="tender-stat">
            <span class="stat-label"><?php esc_html_e( 'Industrial', 'tendeal' ); ?></span>
            <span class="stat-value"><?php esc_html_e( 'Actual value', 'tendeal' ); ?></span>
          </div>
          <div class="tender-stat">
            <span class="stat-label"><?php esc_html_e( '# of participants', 'tendeal' ); ?></span>
            <span class="stat-value">56</span>
          </div>
        </div>
        <div class="tender-actions">
          <a href="#" class="button view-more"><?php esc_html_e( 'View more', 'tendeal' ); ?></a>
        </div>
      </div>
    </div>
  </div>
</div>

<?php
	/**
	 * My Account dashboard.
	 *
	 * @since 2.6.0
	 */
	do_action( 'woocommerce_account_dashboard' );

	/**
	 * Deprecated woocommerce_before_my_account action.
	 *
	 * @deprecated 2.6.0
	 */
	do_action( 'woocommerce_before_my_account' );

	/**
	 * Deprecated woocommerce_after_my_account action.
	 *
	 * @deprecated 2.6.0
	 */
	do_action( 'woocommerce_after_my_account' );

/* Omit closing PHP tag at the end of PHP files to avoid "headers already sent" issues. */