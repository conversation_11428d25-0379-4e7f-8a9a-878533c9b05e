/* Shop Page with Brands Styles */

:root {
  --primary-color: #ea9c00;
  --secondary-color: #282f39;
  --success-color: #12b76a;
  --warning-color: #f79009;
  --danger-color: #f04438;
  --light-color: #98a2b3;
  --dark-color: #1d2939;
  --gray-color: #98a2b3;
  --body-bg: #f4f7fd;
  --border-color: rgba(152, 162, 179, 0.25);
}

/* Shop Container */
.shop-container {
  padding: 20px 0;
  background-color: var(--body-bg);
}

/* Sidebar Styles */
.shop-sidebar {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-section {
  margin-bottom: 25px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 15px;
}

/* Brand Filter */
.brand-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.brand-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.brand-item input[type="checkbox"] {
  margin-right: 10px;
}

.brand-item label {
  font-size: 14px;
  color: var(--secondary-color);
  cursor: pointer;
}

/* Rating Filter */
.rating-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.rating-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.rating-item input[type="checkbox"] {
  margin-right: 10px;
}

.rating-item label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.rating-item .bi-star-fill {
  color: var(--primary-color);
  font-size: 14px;
}

.rating-item .bi-star {
  color: var(--light-color);
  font-size: 14px;
}

/* Price Filter */
.price-filter .price_slider_wrapper {
  padding: 10px 0;
}

.price-filter .price_slider {
  height: 4px;
  background-color: var(--light-color) !important;
  margin: 15px 0;
  border-radius: 2px;
}

.price-filter .ui-slider-range {
  background-color: var(--primary-color) !important;
}

.price-filter .ui-slider-handle {
  background-color: #fff !important;
  border: 2px solid var(--primary-color) !important;
  border-radius: 50% !important;
  top: -0.5em !important;
  width: 1em !important;
  height: 1em !important;
  cursor: pointer !important;
}

.price-filter .price_slider_amount {
  display: flex;
  flex-direction: column-reverse;
}

.price-filter .price_label {
  margin-bottom: 10px;
  font-size: 14px;
  color: var(--secondary-color);
}

.price-filter button {
  background-color: var(--primary-color) !important;
  color: #fff !important;
  border: none !important;
  padding: 8px 15px !important;
  border-radius: 5px !important;
  cursor: pointer !important;
  font-size: 14px !important;
  transition: background-color 0.3s ease !important;
}

.price-filter button:hover {
  background-color: #d08a00 !important;
}

/* Main Content Area */
.shop-main-content {
  padding-left: 20px;
}

/* Featured Banners */
.featured-banners {
  margin-bottom: 30px;
}

.banner-item {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  height: 200px;
  margin-bottom: 20px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.banner-content {
  position: absolute;
  top: 20px;
  left: 20px;
  max-width: 60%;
  z-index: 2;
}

.banner-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 10px;
}

.banner-content p {
  font-size: 14px;
  color: var(--secondary-color);
  margin-bottom: 15px;
}

.banner-image {
  position: absolute;
  right: 0;
  bottom: 0;
  max-height: 100%;
  max-width: 50%;
  object-fit: contain;
}

/* Section Styles */
.category-section,
.best-brands-section,
.gaming-offers-section,
.featured-products-section {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0;
}

/* Products Grid */
.products-grid,
.gaming-offers-grid,
.featured-products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

/* Product Card */
.product-card {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-image {
  position: relative;
  padding: 10px;
  text-align: center;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
}

.product-image img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

.sale-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--secondary-color);
  color: #fff;
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 15px;
  z-index: 2;
}

.product-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
  opacity: 1;
}

.action-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #fff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.action-btn:hover {
  background-color: var(--primary-color);
  color: #fff;
}

.product-info {
  padding: 15px;
}

.product-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-title a {
  color: var(--dark-color);
  text-decoration: none;
}

.product-title a:hover {
  color: var(--primary-color);
}

.product-rating {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.star-rating {
  color: var(--primary-color);
  font-size: 14px;
  margin-right: 5px;
}

.rating-count {
  font-size: 12px;
  color: var(--light-color);
}

.product-price {
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--dark-color);
}

.product-price del {
  color: var(--light-color);
  font-weight: 400;
  font-size: 12px;
  margin-right: 5px;
}

.product-price ins {
  text-decoration: none;
  color: var(--dark-color);
}

.product-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.in-stock {
  color: var(--success-color);
}

.out-of-stock {
  color: var(--danger-color);
}

.free-shipping {
  color: var(--primary-color);
}

.view-more-container {
  text-align: center;
  margin-top: 20px;
}

/* Best Brands Section */
.brands-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.brands-grid .brand-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.brands-grid .brand-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.brands-grid .brand-item img {
  max-width: 100%;
  max-height: 50px;
  object-fit: contain;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .shop-main-content {
    padding-left: 0;
    margin-top: 20px;
  }
  
  .products-grid,
  .gaming-offers-grid,
  .featured-products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .brands-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .products-grid,
  .gaming-offers-grid,
  .featured-products-grid {
    grid-template-columns: 1fr;
  }
  
  .banner-content {
    max-width: 100%;
  }
  
  .banner-image {
    opacity: 0.3;
    max-width: 100%;
  }
}

/* Fix for WooCommerce star rating */
.woocommerce .star-rating {
  float: none;
  display: inline-block;
}

.woocommerce .star-rating::before {
  color: var(--light-color);
}

.woocommerce .star-rating span::before {
  color: var(--primary-color);
}

/* Button Styles */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #d08a00;
  border-color: #d08a00;
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
}

/* Notification Styles */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
}

.notification {
  padding: 15px 20px;
  margin-bottom: 10px;
  border-radius: 5px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification-success {
  background-color: var(--success-color);
  color: #fff;
}

.notification-error {
  background-color: var(--danger-color);
  color: #fff;
}

.notification-info {
  background-color: var(--primary-color);
  color: #fff;
}
