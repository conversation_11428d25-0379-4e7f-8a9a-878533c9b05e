/* Share Modal Styles */
.share-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99999;
    display: flex;
    align-items: center;
    justify-content: center;
    /* opacity: 0; */
    transition: opacity 0.3s ease;
    backdrop-filter: blur(2px);
}

.share-modal.show {
    opacity: 1;
}

.share-modal-content {
    background: white;
    border-radius: 12px;
    padding: 0;
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.share-modal.show .share-modal-content {
    transform: scale(1);
}

.share-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
}

.share-modal-header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
}

.share-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.share-modal-close:hover {
    background-color: #f5f5f5;
    color: #333;
}

.share-options {
    padding: 20px 24px;
    display: grid;
    gap: 12px;
}

.share-option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    border: 1px solid #e5e5e5;
    background: #fff;
}

.share-option:hover {
    background-color: #f8f9fa;
    border-color: #ea9c00;
    color: #ea9c00;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(234, 156, 0, 0.1);
}

.share-option i {
    margin-right: 12px;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.share-option span {
    font-weight: 500;
    font-size: 0.95rem;
}

/* Platform-specific colors */
.share-option.facebook:hover {
    background-color: #1877f2;
    border-color: #1877f2;
    color: white;
}

.share-option.twitter:hover {
    background-color: #1da1f2;
    border-color: #1da1f2;
    color: white;
}

.share-option.whatsapp:hover {
    background-color: #25d366;
    border-color: #25d366;
    color: white;
}

.share-option.email:hover {
    background-color: #ea4335;
    border-color: #ea4335;
    color: white;
}

.share-option.copy-link:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .share-modal-content {
        width: 95%;
        margin: 20px;
    }

    .share-modal-header {
        padding: 16px 20px;
    }

    .share-modal-header h4 {
        font-size: 1.1rem;
    }

    .share-options {
        padding: 16px 20px;
        gap: 10px;
    }

    .share-option {
        padding: 14px 16px;
    }

    .share-option span {
        font-size: 0.9rem;
    }
}

/* Animation for fade in/out */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }

    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* Success state for copy link */
.share-option.copy-link.copied {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.share-option.copy-link.copied:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

/* Improved share button styling */
.share-button {
    position: relative;
}

.share-button:hover {
    color: #ea9c00;
}

.share-button:active {
    transform: scale(0.95);
}

/* Loading state for share button */
.share-button.loading {
    opacity: 0.7;
    pointer-events: none;
}

.share-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #ea9c00;
    border-radius: 50%;
    border-top-color: transparent;
    /* No spinning animation */
}