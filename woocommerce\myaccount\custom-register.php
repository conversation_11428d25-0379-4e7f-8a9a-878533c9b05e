<?php
/**
 * Custom Registration Form
 *
 * This template overrides the default WooCommerce registration form.
 *
 * @package tendeal
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Ensure CSS is loaded
wp_enqueue_style('tendeal-account-pages', get_stylesheet_directory_uri() . '/css/account-pages.css');

get_header();

do_action('woocommerce_before_customer_login_form');
?>

<div class="account-page-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="<?php echo esc_url(home_url('/')); ?>">Home</a></li>
      <li class="breadcrumb-item active" aria-current="page">Create new account</li>
    </ol>
  </nav>

  <h1 class="account-page-title">Create new account</h1>

  <form method="post" class="account-form woocommerce-form woocommerce-form-register register"
    <?php do_action('woocommerce_register_form_tag'); ?>>
    <?php do_action('woocommerce_register_form_start'); ?>

    <div class="form-group">
      <label for="reg_full_name"><?php esc_html_e('Full name', 'woocommerce'); ?></label>
      <input type="text" class="form-control" name="full_name" id="reg_full_name" autocomplete="name"
        value="<?php echo (!empty($_POST['full_name'])) ? esc_attr(wp_unslash($_POST['full_name'])) : ''; ?>"
        placeholder="Full name" />
    </div>

    <div class="form-group">
      <label for="reg_email"><?php esc_html_e('Email address', 'woocommerce'); ?></label>
      <input type="email" class="form-control" name="email" id="reg_email" autocomplete="email"
        value="<?php echo (!empty($_POST['email'])) ? esc_attr(wp_unslash($_POST['email'])) : ''; ?>"
        placeholder="Email address" />
    </div>

    <div class="form-group">
      <label for="reg_password"><?php esc_html_e('Password', 'woocommerce'); ?></label>
      <div class="password-field-container">
        <input type="password" class="form-control" name="password" id="reg_password" autocomplete="new-password"
          placeholder="Password" />
        <button type="button" class="password-toggle" aria-label="Toggle password visibility">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye"
            viewBox="0 0 16 16">
            <path
              d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z" />
            <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z" />
          </svg>
        </button>
      </div>
    </div>

    <div class="form-group">
      <label for="reg_confirm_password"><?php esc_html_e('Confirm password', 'woocommerce'); ?></label>
      <div class="password-field-container">
        <input type="password" class="form-control" name="confirm_password" id="reg_confirm_password"
          autocomplete="new-password" placeholder="Confirm password" />
        <button type="button" class="password-toggle" aria-label="Toggle password visibility">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye"
            viewBox="0 0 16 16">
            <path
              d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z" />
            <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z" />
          </svg>
        </button>
      </div>
    </div>

    <div class="form-check terms-check">
      <input class="form-check-input" name="terms" type="checkbox" id="terms" required>
      <label class="form-check-label" for="terms">
        <?php esc_html_e('I agree to the', 'woocommerce'); ?>
        <a
          href="<?php echo esc_url(get_privacy_policy_url()); ?>"><?php esc_html_e('terms and conditions', 'woocommerce'); ?></a>
        <?php esc_html_e('and have read the', 'woocommerce'); ?>
        <a
          href="<?php echo esc_url(get_privacy_policy_url()); ?>"><?php esc_html_e('privacy policy', 'woocommerce'); ?></a>.
      </label>
    </div>

    <?php do_action('woocommerce_register_form'); ?>

    <p>
      <?php wp_nonce_field('woocommerce-register', 'woocommerce-register-nonce'); ?>
      <button type="submit" class="btn-submit" name="register"
        value="<?php esc_attr_e('Register', 'woocommerce'); ?>"><?php esc_html_e('Create a new account', 'woocommerce'); ?></button>
    </p>

    <div class="account-switch">
      <?php esc_html_e('Already have an account?', 'woocommerce'); ?>
      <a href="<?php echo esc_url(wc_get_page_permalink('myaccount')); ?>">
        <?php esc_html_e('Log in', 'woocommerce'); ?>
      </a>
    </div>

    <?php do_action('woocommerce_register_form_end'); ?>
  </form>
</div>

<script>
jQuery(document).ready(function($) {
  // Password visibility toggle
  $('.password-toggle').on('click', function() {
    const passwordField = $(this).siblings('input');
    const icon = $(this).find('svg');

    if (passwordField.attr('type') === 'password') {
      passwordField.attr('type', 'text');
      icon.html(
        '<path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"/><path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"/><path d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12-.708.708z"/>'
      );
    } else {
      passwordField.attr('type', 'password');
      icon.html(
        '<path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"/><path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>'
      );
    }
  });

  // Form validation
  $('form.register').on('submit', function(e) {
    const password = $('#reg_password').val();
    const confirmPassword = $('#reg_confirm_password').val();

    if (password !== confirmPassword) {
      e.preventDefault();
      alert('Passwords do not match.');
      return false;
    }

    if (!$('#terms').is(':checked')) {
      e.preventDefault();
      alert('You must agree to the terms and conditions.');
      return false;
    }

    return true;
  });
});
</script>

<?php
do_action('woocommerce_after_customer_login_form');
get_footer();
?>