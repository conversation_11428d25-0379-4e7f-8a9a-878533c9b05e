<?php
/**
 * Template Name: Shop with Updated Product Cards
 *
 * Shop page template using the new modern product card design
 */

get_header();

// Get current page and filters
$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
$products_per_page = 12;

// Build query args
$args = array(
    'post_type' => 'product',
    'posts_per_page' => $products_per_page,
    'paged' => $paged,
    'post_status' => 'publish',
    'meta_query' => array(
        array(
            'key' => '_visibility',
            'value' => array('catalog', 'visible'),
            'compare' => 'IN'
        )
    )
);

// Handle search
if (isset($_GET['s']) && !empty($_GET['s'])) {
    $args['s'] = sanitize_text_field($_GET['s']);
}

// Handle category filter
if (isset($_GET['product_cat']) && !empty($_GET['product_cat'])) {
    $args['tax_query'][] = array(
        'taxonomy' => 'product_cat',
        'field' => 'slug',
        'terms' => sanitize_text_field($_GET['product_cat'])
    );
}

// Handle price filter
if (isset($_GET['min_price']) && isset($_GET['max_price'])) {
    $args['meta_query'][] = array(
        'key' => '_price',
        'value' => array(floatval($_GET['min_price']), floatval($_GET['max_price'])),
        'compare' => 'BETWEEN',
        'type' => 'NUMERIC'
    );
}

// Handle rating filter
if (isset($_GET['rating']) && !empty($_GET['rating'])) {
    $args['meta_query'][] = array(
        'key' => '_wc_average_rating',
        'value' => intval($_GET['rating']),
        'compare' => '>='
    );
}

// Handle sorting
$orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'menu_order';
switch ($orderby) {
    case 'price':
        $args['meta_key'] = '_price';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'ASC';
        break;
    case 'price-desc':
        $args['meta_key'] = '_price';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'DESC';
        break;
    case 'popularity':
        $args['meta_key'] = 'total_sales';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'DESC';
        break;
    case 'rating':
        $args['meta_key'] = '_wc_average_rating';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'DESC';
        break;
    case 'date':
        $args['orderby'] = 'date';
        $args['order'] = 'DESC';
        break;
    default:
        $args['orderby'] = 'menu_order title';
        $args['order'] = 'ASC';
}

// Execute query
$products_query = new WP_Query($args);
?>

<main id="primary" class="site-main">
    <div class="container">
        <!-- Page Header -->
        <div class="shop-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="shop-title">Shop</h1>
                    <p class="shop-description">Discover our amazing products</p>
                </div>
                <div class="col-md-6">
                    <!-- Search and Sort -->
                    <div class="shop-controls">
                        <form method="GET" class="shop-search-form">
                            <div class="input-group">
                                <input type="text" name="s" class="form-control" placeholder="Search products..." value="<?php echo esc_attr(isset($_GET['s']) ? $_GET['s'] : ''); ?>">
                                <button type="submit" class="btn btn-primary">
                                    <i data-feather="search"></i>
                                </button>
                            </div>
                        </form>

                        <select name="orderby" class="form-select" onchange="this.form.submit()">
                            <option value="menu_order" <?php selected($orderby, 'menu_order'); ?>>Default sorting</option>
                            <option value="popularity" <?php selected($orderby, 'popularity'); ?>>Sort by popularity</option>
                            <option value="rating" <?php selected($orderby, 'rating'); ?>>Sort by average rating</option>
                            <option value="date" <?php selected($orderby, 'date'); ?>>Sort by latest</option>
                            <option value="price" <?php selected($orderby, 'price'); ?>>Sort by price: low to high</option>
                            <option value="price-desc" <?php selected($orderby, 'price-desc'); ?>>Sort by price: high to low</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="shop-content">
            <?php if ($products_query->have_posts()) : ?>

                <!-- Results Info -->
                <div class="shop-results-info">
                    <p>Showing <?php echo (($paged - 1) * $products_per_page + 1); ?>–<?php echo min($paged * $products_per_page, $products_query->found_posts); ?> of <?php echo $products_query->found_posts; ?> results</p>
                </div>

                <!-- Products Grid -->
                <div class="products-grid-updated">
                    <?php while ($products_query->have_posts()) : $products_query->the_post(); ?>
                        <?php
                        global $product, $post;

                        // Ensure we have a valid product object for each iteration
                        if (empty($product) || !is_a($product, 'WC_Product')) {
                            $product = wc_get_product($post->ID);
                        }

                        // Only include the template if we have a valid product
                        if ($product && is_a($product, 'WC_Product') && $product->is_visible()) {
                            // Ensure the global product is properly set
                            $GLOBALS['product'] = $product;

                            // Include the updated product card template
                            include(get_template_directory() . '/woocommerce/content-product-card-updated.php');
                        }
                        ?>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <div class="shop-pagination">
                    <?php
                    echo paginate_links(array(
                        'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
                        'format' => '?paged=%#%',
                        'current' => max(1, get_query_var('paged')),
                        'total' => $products_query->max_num_pages,
                        'prev_text' => '<i data-feather="chevron-left"></i> Previous',
                        'next_text' => 'Next <i data-feather="chevron-right"></i>',
                        'type' => 'list',
                        'end_size' => 3,
                        'mid_size' => 3
                    ));
                    ?>
                </div>

            <?php else : ?>

                <!-- No Products Found -->
                <div class="no-products-found">
                    <div class="text-center">
                        <i data-feather="search" style="width: 64px; height: 64px; color: #ccc;"></i>
                        <h3>No products found</h3>
                        <p>Sorry, no products match your search criteria.</p>
                        <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="btn btn-primary">View All Products</a>
                    </div>
                </div>

            <?php endif; ?>
        </div>
    </div>
</main>

<style>
/* Shop Page Styles */
.shop-header {
    padding: 40px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 40px;
}

.shop-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
}

.shop-description {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 0;
}

.shop-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: flex-end;
}

.shop-search-form {
    flex: 1;
    max-width: 300px;
}

.shop-search-form .input-group {
    display: flex;
}

.shop-search-form .form-control {
    border-radius: 8px 0 0 8px;
    border-right: none;
}

.shop-search-form .btn {
    border-radius: 0 8px 8px 0;
    padding: 0 16px;
}

.form-select {
    min-width: 200px;
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 8px 12px;
}

.shop-results-info {
    margin-bottom: 30px;
    color: #666;
}

.shop-pagination {
    margin-top: 60px;
    display: flex;
    justify-content: center;
}

.shop-pagination .page-numbers {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 8px;
}

.shop-pagination .page-numbers li {
    display: flex;
}

.shop-pagination .page-numbers a,
.shop-pagination .page-numbers span {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.shop-pagination .page-numbers .current {
    background: #ea9c00;
    color: white;
    border-color: #ea9c00;
}

.shop-pagination .page-numbers a:hover {
    background: #f8f9fa;
    border-color: #ea9c00;
}

.no-products-found {
    text-align: center;
    padding: 80px 20px;
}

.no-products-found h3 {
    margin: 20px 0 10px;
    color: #333;
}

.no-products-found p {
    color: #666;
    margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .shop-header {
        padding: 20px 0;
    }

    .shop-title {
        font-size: 2rem;
    }

    .shop-controls {
        flex-direction: column;
        gap: 15px;
        margin-top: 20px;
    }

    .shop-search-form {
        max-width: 100%;
    }

    .form-select {
        min-width: 100%;
    }

    .shop-pagination .page-numbers a,
    .shop-pagination .page-numbers span {
        padding: 8px 12px;
        font-size: 14px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
});
</script>

<?php
wp_reset_postdata();
get_footer();
?>
