/* Updated Product Card Styles */

.shop-main-content {
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
  margin: 0 1.8% 2.992em 0 !important;
}

.woocommerce ul.products li.first,
.woocommerce-page ul.products li.first {
  clear: none !important;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.products-grid.list-view {
  grid-template-columns: 1fr;
}

/* Product Card */
.product-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Product Image */
.product-image {
  position: relative;
  padding-top: 100%;
  /* 1:1 Aspect Ratio */
  overflow: hidden;
}

.product-image a {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

/* Discount Badge */
.discount-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #000;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  padding: 5px 10px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

/* Product Actions */
.product-actions {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 10px;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 2;
}

.product-card:hover .product-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #fff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.action-btn:hover {
  background-color: #f0c14b;
  color: #fff;
  transform: translateY(-3px);
}

/* Product Info */
.product-info {
  padding: 15px;
}

.product-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px;
  line-height: 1.4;
  height: 40px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-title a {
  color: #333;
  text-decoration: none;
  transition: color 0.2s ease;
}

.product-title a:hover {
  color: #f0c14b;
}

/* Product Rating */
.product-rating {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.star-rating {
  color: #f0c14b;
  font-size: 12px;
  margin-right: 5px;
}

.rating-count {
  font-size: 12px;
  color: #777;
}

/* Product Price */
.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.product-price del {
  font-size: 14px;
  font-weight: 400;
  color: #999;
  margin-right: 5px;
}

.product-price ins {
  text-decoration: none;
}

/* Product Meta */
.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
  color: #777;
}

.product-brand {
  font-weight: 500;
}

/* Product Buttons */
.product-buttons {
  display: flex;
  gap: 10px;
}

.btn-add-to-cart {
  flex: 1;
  display: inline-block;
  padding: 8px 15px;
  background-color: #f0c14b;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-add-to-cart:hover {
  background-color: #e0b347;
}

.btn-add-to-favorite {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-decoration: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.btn-add-to-favorite:hover {
  background-color: #f0c14b;
  color: #fff;
  border-color: #f0c14b;
}

/* No Products */
.no-products {
  text-align: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 30px;
}

.no-products p {
  margin: 0 0 10px;
  color: #666;
}

.no-products a {
  color: #f0c14b;
  text-decoration: none;
  font-weight: 500;
}

.no-products a:hover {
  text-decoration: underline;
}

/* Shop Header */
.shop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.shop-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.shop-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.product-count {
  font-size: 14px;
  color: #777;
}

.shop-sorting .form-select {
  padding: 8px 12px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #fff;
  color: #333;
  cursor: pointer;
}

.shop-view-mode {
  display: flex;
  gap: 5px;
}

.view-mode-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-mode-btn.active {
  background-color: #f0c14b;
  color: #fff;
  border-color: #f0c14b;
}

/* Pagination */
.shop-pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.page-numbers {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  margin: 0 5px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #ddd;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
}

.page-numbers.current {
  background-color: #f0c14b;
  color: #fff;
  border-color: #f0c14b;
}

.page-numbers:hover:not(.current) {
  background-color: #f5f5f5;
}

/* List View */
.products-grid.list-view .product-card {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 20px;
}

.products-grid.list-view .product-image {
  padding-top: 0;
  height: 200px;
}

.products-grid.list-view .product-title {
  height: auto;
  -webkit-line-clamp: 1;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .products-grid.list-view .product-card {
    grid-template-columns: 150px 1fr;
  }

  .products-grid.list-view .product-image {
    height: 150px;
  }
}

@media (max-width: 768px) {
  .shop-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .shop-controls {
    width: 100%;
    margin-top: 15px;
    justify-content: space-between;
  }

  .products-grid.list-view .product-card {
    grid-template-columns: 120px 1fr;
  }

  .products-grid.list-view .product-image {
    height: 120px;
  }
}

@media (max-width: 576px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .products-grid.list-view .product-card {
    grid-template-columns: 1fr;
  }

  .products-grid.list-view .product-image {
    height: auto;
    padding-top: 100%;
  }

  .shop-controls {
    flex-wrap: wrap;
    gap: 10px;
  }

  .product-count {
    width: 100%;
    order: 3;
    margin-top: 10px;
  }
}