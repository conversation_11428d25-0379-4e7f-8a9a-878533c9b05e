<?php
/**
 * The Template for displaying product archives with dynamic filters
 *
 * This template overrides the default WooCommerce archive-product.php template
 * to use our dynamic filters implementation.
 *
 * @package Teandeal
 */

defined('ABSPATH') || exit;

get_header('shop');

/**
 * Hook: woocommerce_before_main_content.
 *
 * @hooked woocommerce_output_content_wrapper - 10 (outputs opening divs for the content)
 * @hooked woocommerce_breadcrumb - 20
 * @hooked WC_Structured_Data::generate_website_data() - 30
 */
do_action('woocommerce_before_main_content');

// Include the dynamic filters template
include_once(get_template_directory() . '/shop-dynamic-filters.php');

/**
 * Hook: woocommerce_after_main_content.
 *
 * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
 */
do_action('woocommerce_after_main_content');

get_footer('shop');
