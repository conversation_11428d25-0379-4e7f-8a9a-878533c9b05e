/* Category Page Styles */

/* Breadcrumb styles */
.breadcrumb-container {
  background-color: transparent;
}

.breadcrumb {
  padding: 0;
  margin: 0;
  background-color: transparent;
  display: flex;
  align-items: center;
}

.breadcrumb-item {
  font-size: 14px;
  color: #777;
}

.breadcrumb-item a {
  color: #777;
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: var(--primary-color, #f0c14b);
}

.breadcrumb-item+.breadcrumb-item::before {
  content: ">";
  color: #777;
}

.breadcrumb-item.active {
  color: #333;
}

/* Categories header */
.categories-header {
  margin: 40px 0;
}

.categories-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 0;
}

/* Categories grid */
.categories-grid {
  margin-bottom: 60px;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 30px 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #eee;
  transition: all 0.3s ease;
  height: 100%;
  text-decoration: none;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  border-color: var(--primary-color, #f0c14b);
}

.category-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.category-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 5px;
  line-height: 1.4;
}

.product-count {
  font-size: 16px;
  color: #777;
  display: block;
}

.category-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-width: 60px;
  max-height: 60px;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .categories-title {
    font-size: 28px;
  }
}

@media (max-width: 767px) {
  .categories-title {
    font-size: 24px;
  }

  .category-card {
    padding: 20px 15px;
  }

  .category-icon {
    width: 50px;
    height: 50px;
  }

  .category-name {
    font-size: 14px;
  }
}

@media (max-width: 575px) {
  .categories-title {
    font-size: 22px;
  }
}