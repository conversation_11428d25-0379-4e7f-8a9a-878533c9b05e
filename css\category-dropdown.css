/* Category Dropdown Styles */

/* Category Button */
.category-menu-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #ea9c00;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 180px;
  text-align: left;
}

.category-menu-button:hover {
  background-color: #d08a00;
}

.category-menu-button i {
  margin-right: 6px;
  font-size: 14px;
}

/* Category Dropdown */
.category-dropdown {
  position: absolute;
  /* top: 100%;
  left: 0; */
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1000;
  width: 1200px;
  max-width: 95vw;
  margin-top: 5px;
}

.category-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.category-dropdown-content {
  display: flex;
  padding: 0;
}

/* Left column - Main categories */
.category-column.category-main-list {
  flex: 0 0 250px;
  min-width: 300px;
  background-color: #f8f9fa;
  border-right: 1px solid #eee;
  padding: 0;
  max-height: 500px;
  overflow-y: auto;
}

.category-list {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%
}

.category-item {
  margin: 0;
  border-bottom: 1px solid #eee;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s ease;
}

.category-item a:hover,
.category-item.active a {
  background-color: #fff;
  color: #ea9c00;
}

.category-item i {
  font-size: 12px;
  opacity: 0.7;
}


.category-item .category-icon {
  width: 30px;
  height: 30px;

  display: flex;
  align-items: center;
  justify-content: center;
}


/* Right column - Subcategories */
.category-column.category-subcategory-container {
  flex: 1;
  padding: 20px 25px;
  background-color: #fff;
}

.subcategory-content {
  display: none;
}

.subcategory-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.subcategory-columns {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.subcategory-column {
  flex: 1;
}

.subcategory-column a {
  color: #333;

}

/* Subcategory Links */
.category-link {
  display: block;
  padding: 6px 0;
  color: #333;
  font-size: 14px;
  text-decoration: none;
  transition: color 0.2s ease;
  font-weight: 400;
}

.category-link:hover {
  color: #ea9c00;
}

.category-link.view-all {
  color: #ea9c00;
  font-weight: 600;
  margin-top: 10px;
  display: inline-block;
}

.no-subcategories {
  color: #777;
  font-style: italic;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .category-dropdown {
    width: 800px;
  }

  .category-column.category-main-list {
    flex: 0 0 220px;
    min-width: 220px;
  }

  .subcategory-columns {
    flex-wrap: wrap;
    gap: 20px;
  }

  .subcategory-column {
    flex: 0 0 calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .category-dropdown {
    position: absolute;
    width: 100%;
    max-width: 100%;
    left: 0;
    right: 0;
  }

  .category-dropdown-content {
    flex-direction: column;
  }

  .category-column.category-main-list {
    flex: none;
    width: 100%;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid #eee;
  }

  .category-column.category-subcategory-container {
    padding: 15px;
  }

  .subcategory-title {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .subcategory-columns {
    gap: 15px;
  }

  .category-menu-button {
    padding: 6px 10px;
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .category-menu-button {
    padding: 5px 8px;
    font-size: 11px;
  }

  .category-item a {
    padding: 10px 12px;
    font-size: 16px;
  }

  .subcategory-columns {
    flex-direction: column;
    gap: 0;
  }

  .subcategory-column {
    flex: none;
    width: 100%;
  }

  .category-link {
    font-size: 16px;
    padding: 5px 0;
  }
}