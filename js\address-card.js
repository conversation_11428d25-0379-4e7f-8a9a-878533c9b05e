/**
 * Address Card JavaScript
 *
 * Handles interactions for the address card design
 */

jQuery(document).ready(function($) {

    // Initialize Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }

    // Address card selection (for checkout/forms)
    $('.address-card').on('click', function(e) {
        // Don't trigger if clicking on action buttons
        if ($(e.target).closest('.address-card-actions').length) {
            return;
        }

        // Remove selected class from all cards
        $('.address-card').removeClass('selected');

        // Add selected class to clicked card
        $(this).addClass('selected');

        // Update any hidden input with selected address ID
        const addressId = $(this).data('address-id');
        if (addressId) {
            $('#selected_address_id').val(addressId);
        }

        // Trigger custom event
        $(document).trigger('addressCardSelected', [addressId, this]);
    });

    // Set default address
    $('.address-action-btn.set-default').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $button = $(this);
        const $card = $button.closest('.address-card');
        const addressId = $card.data('address-id');
        const action = $button.data('action');

        if (!addressId || !action) {
            console.error('Missing address ID or action');
            return;
        }

        // Show loading state
        const originalIcon = $button.html();
        $button.html('<i data-feather="loader" class="spin"></i>').prop('disabled', true);
        feather.replace();

        // Make AJAX request
        $.ajax({
            url: tendeal_address_params.ajax_url,
            type: 'POST',
            data: {
                action: action,
                address_id: addressId,
                security: tendeal_address_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Remove default badge from all cards
                    $('.address-default-badge').remove();
                    $('.address-action-btn.set-default').show();

                    // Add default badge to this card
                    $card.prepend('<div class="address-default-badge"><i data-feather="check-circle" class="icon"></i>Default</div>');
                    $button.hide();

                    feather.replace();
                    showAddressMessage('success', response.data.message || 'Default address updated successfully');
                } else {
                    showAddressMessage('error', response.data.message || 'Failed to set default address');
                }
            },
            error: function() {
                showAddressMessage('error', 'Network error. Please try again.');
            },
            complete: function() {
                $button.html(originalIcon).prop('disabled', false);
                feather.replace();
            }
        });
    });

    // Delete address
    $('.address-action-btn.delete').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $button = $(this);
        const $card = $button.closest('.address-card');
        const addressId = $card.data('address-id');
        const action = $button.data('action');

        if (!addressId || !action) {
            console.error('Missing address ID or action');
            return;
        }

        // Confirm deletion
        if (!confirm('Are you sure you want to delete this address? This action cannot be undone.')) {
            return;
        }

        // Show loading state
        const originalIcon = $button.html();
        $button.html('<i data-feather="loader" class="spin"></i>').prop('disabled', true);
        feather.replace();

        // Make AJAX request
        $.ajax({
            url: tendeal_address_params.ajax_url,
            type: 'POST',
            data: {
                action: action,
                address_id: addressId,
                security: tendeal_address_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Animate card removal
                    $card.fadeOut(300, function() {
                        $(this).remove();

                        // Check if no addresses left
                        if ($('.address-card').length === 0) {
                            $('.address-list').html('<p class="no-addresses">No addresses found.</p>');
                        }
                    });

                    showAddressMessage('success', response.data.message || 'Address deleted successfully');
                } else {
                    showAddressMessage('error', response.data.message || 'Failed to delete address');
                }
            },
            error: function() {
                showAddressMessage('error', 'Network error. Please try again.');
            },
            complete: function() {
                $button.html(originalIcon).prop('disabled', false);
                feather.replace();
            }
        });
    });

    // Address form validation
    $('.address-form-container form').on('submit', function(e) {
        let isValid = true;
        const $form = $(this);

        // Clear previous errors
        $form.find('.address-form-input').removeClass('error');

        // Check required fields
        $form.find('.address-form-input[required]').each(function() {
            if ($(this).val().trim() === '') {
                $(this).addClass('error');
                isValid = false;
            }
        });

        if (!isValid) {
            e.preventDefault();
            showAddressMessage('error', 'Please fill in all required fields.');

            // Focus on first error field
            $form.find('.address-form-input.error').first().focus();
        }
    });

    // Remove error class on input
    $('.address-form-input').on('input', function() {
        $(this).removeClass('error');
    });

    // Show address messages
    function showAddressMessage(type, message) {
        // Remove existing messages
        $('.address-message').remove();

        // Create message element
        const messageClass = type === 'success' ? 'success' : 'error';
        const messageHtml = `
            <div class="address-message address-message-${messageClass}" style="
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                font-weight: 500;
                max-width: 300px;
            ">
                ${message}
            </div>
        `;

        $('body').append(messageHtml);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.address-message').fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        .address-card {
            transition: all 0.3s ease;
        }

        .address-card.selected {
            border-color: #ea9c00;
            box-shadow: 0 4px 16px rgba(234, 156, 0, 0.2);
            transform: translateY(-2px);
        }

        .address-form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .spin {
            /* No spinning animation */
        }

        .address-card-actions {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .address-card:hover .address-card-actions {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .address-card-actions {
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);
});

// Global function to programmatically select an address card
function selectAddressCard(addressId) {
    jQuery('.address-card').removeClass('selected');
    jQuery('.address-card[data-address-id="' + addressId + '"]').addClass('selected');
    jQuery('#selected_address_id').val(addressId);
}

// Global function to add a new address card dynamically
function addAddressCard(addressData, options = {}) {
    if (typeof tendeal_generate_address_card === 'function') {
        const cardHtml = tendeal_generate_address_card(addressData, options);
        jQuery('.address-list').append(cardHtml);

        // Initialize feather icons for new card
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    }
}
