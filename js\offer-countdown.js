/**
 * JavaScript for Offer Countdown Timer
 */
jQuery(document).ready(function($) {
    // Initialize countdown timers
    function initCountdowns() {
        $('.offer-countdown').each(function() {
            var $countdown = $(this);

            // Clear any existing interval
            var existingInterval = $countdown.data('interval-id');
            if (existingInterval) {
                clearInterval(existingInterval);
            }

            // If this countdown has data attributes for end time, use them
            if ($countdown.data('end-time')) {
                var endTime = $countdown.data('end-time');
                var now = Math.floor(Date.now() / 1000);
                var timeRemaining = endTime - now;

                if (timeRemaining > 0) {
                    // Start the countdown
                    updateCountdown($countdown, timeRemaining);

                    // Update every second
                    var countdownInterval = setInterval(function() {
                        timeRemaining--;

                        if (timeRemaining <= 0) {
                            clearInterval(countdownInterval);
                            $countdown.closest('.offer-card').fadeOut();
                        } else {
                            updateCountdown($countdown, timeRemaining);
                        }
                    }, 1000);

                    // Store the interval ID
                    $countdown.data('interval-id', countdownInterval);
                } else {
                    // If time has already expired, hide the offer card
                    $countdown.closest('.offer-card').fadeOut();
                }
            } else {
                // For countdowns without data attributes, set a default end time
                // 24 hours from now for demo purposes
                var defaultEndTime = Math.floor(Date.now() / 1000) + (24 * 60 * 60);
                $countdown.attr('data-end-time', defaultEndTime);

                // Initialize with the default end time
                var timeRemaining = defaultEndTime - Math.floor(Date.now() / 1000);

                // Start the countdown
                updateCountdown($countdown, timeRemaining);

                // Update every second
                var countdownInterval = setInterval(function() {
                    timeRemaining--;

                    if (timeRemaining <= 0) {
                        clearInterval(countdownInterval);
                        $countdown.closest('.offer-card').fadeOut();
                    } else {
                        updateCountdown($countdown, timeRemaining);
                    }
                }, 1000);

                // Store the interval ID
                $countdown.data('interval-id', countdownInterval);
            }
        });
    }

    // Update countdown display
    function updateCountdown($countdown, timeRemaining) {
        var days = Math.floor(timeRemaining / (60 * 60 * 24));
        var hours = Math.floor((timeRemaining % (60 * 60 * 24)) / (60 * 60));
        var minutes = Math.floor((timeRemaining % (60 * 60)) / 60);
        var seconds = timeRemaining % 60;

        // Format with leading zeros
        days = (days < 10) ? '0' + days : days;
        hours = (hours < 10) ? '0' + hours : hours;
        minutes = (minutes < 10) ? '0' + minutes : minutes;
        seconds = (seconds < 10) ? '0' + seconds : seconds;

        // Get current values to check if they've changed
        var currentDays = $countdown.find('.countdown-item:eq(0)').text();
        var currentHours = $countdown.find('.countdown-item:eq(1)').text();
        var currentMinutes = $countdown.find('.countdown-item:eq(2)').text();
        var currentSeconds = $countdown.find('.countdown-item:eq(3)').text();

        // Update the display and animate changed values
        updateCountdownItem($countdown.find('.countdown-item:eq(0)'), days, currentDays !== days);
        updateCountdownItem($countdown.find('.countdown-item:eq(1)'), hours, currentHours !== hours);
        updateCountdownItem($countdown.find('.countdown-item:eq(2)'), minutes, currentMinutes !== minutes);
        updateCountdownItem($countdown.find('.countdown-item:eq(3)'), seconds, currentSeconds !== seconds);
    }

    // Update a single countdown item with animation if needed
    function updateCountdownItem($item, newValue, shouldAnimate) {
        // Add animation class if the value has changed
        if (shouldAnimate) {
            // Create a temporary container for the flip animation
            var $container = $('<div class="flip-container"></div>');
            var $current = $('<div class="flip-current"></div>').text($item.text());
            var $new = $('<div class="flip-new"></div>').text(newValue);

            // Position the container over the current item
            $container.css({
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%'
            });

            // Add the flip elements to the container
            $container.append($current).append($new);

            // Add the container to the item
            $item.css('position', 'relative').append($container);

            // Remove animation class first (if exists) to restart animation
            $item.removeClass('animate');

            // Force a reflow to ensure the animation restarts
            void $item[0].offsetWidth;

            // Update the text and add animation class
            $item.text(newValue).addClass('animate');

            // Remove animation class and cleanup after animation completes
            setTimeout(function() {
                $item.removeClass('animate');
                $container.remove();
            }, 500); // Match the animation duration
        } else {
            // Just update the text without animation
            $item.text(newValue);
        }
    }

    // Initialize on page load
    initCountdowns();

    // Reinitialize when new content is loaded via AJAX
    $(document).on('ajaxComplete', function() {
        initCountdowns();
    });
});
