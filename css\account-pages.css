/* Account Pages Styles (Lo<PERSON>, Register, Password Recovery) */

/* Container styles */
.account-page-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

/* Override WooCommerce default styles */
.woocommerce-account .woocommerce {
  /* max-width: 1500px; */
  margin: 0 auto;
}

/* Hide default WooCommerce elements we don't need */
.woocommerce-account .woocommerce h2 {
  display: none;
}

/* Make sure our styles take precedence */
.woocommerce form.login,
.woocommerce form.register,
.woocommerce form.lost_reset_password {
  border: none;
  padding: 0;
  margin: 0;
  border-radius: 0;
}

/* Breadcrumb styles */
.breadcrumb {
  display: flex;
  list-style: none;
  padding: 0;
  margin-bottom: 30px;
}

.breadcrumb-item {
  font-size: 14px;
}

.breadcrumb-item a {
  color: #666;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #ea9c00;
}

.breadcrumb-item+.breadcrumb-item::before {
  content: ">";
  padding: 0 8px;
  color: #ccc;
}

/* Page title */
.account-page-title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 30px;
  color: #1d2939;
}

/* Form styles */
.account-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1d2939;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 16px;
  background-color: #f8fafc;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
  border-color: #ea9c00;
  outline: none;
  box-shadow: 0 0 0 3px rgba(234, 156, 0, 0.1);
}

/* Password field container */
.password-field-container {
  position: relative;
}

/* Password visibility toggle */
.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #98a2b3;
}

.password-toggle:hover {
  color: #1d2939;
}

/* Remember me checkbox */
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.form-check-input {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  border: 1px solid #e2e8f0;
  border-radius: 3px;
}

.form-check-label {
  font-size: 14px;
  color: #1d2939;
}

/* Forgot password link */
.forgot-password {
  display: block;
  text-align: right;
  color: #ea9c00;
  text-decoration: none;
  font-size: 14px;
  margin-bottom: 20px;
}

.forgot-password:hover {
  text-decoration: underline;
}

/* Submit button */
.btn-submit {
  display: block;
  width: 100%;
  padding: 12px 15px;
  background-color: #ea9c00;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-submit:hover {
  background-color: #d08c00;
}

/* Divider */
.divider {
  display: flex;
  align-items: center;
  margin: 30px 0;
  color: #98a2b3;
  font-size: 14px;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #e2e8f0;
}

.divider::before {
  margin-right: 15px;
}

.divider::after {
  margin-left: 15px;
}

/* Social login buttons */
.social-login {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  transition: background-color 0.2s;
}

.social-btn:hover {
  background-color: #f1f5f9;
}

.social-btn img {
  width: 24px;
  height: 24px;
}

/* Account switch message */
.account-switch {
  text-align: center;
  margin-top: 30px;
  font-size: 14px;
  color: #1d2939;
}

.account-switch a {
  color: #ea9c00;
  text-decoration: none;
  font-weight: 600;
}

.account-switch a:hover {
  text-decoration: underline;
}

/* Terms and conditions checkbox */
.terms-check {
  margin-bottom: 20px;
}

.terms-check a {
  color: #ea9c00;
  text-decoration: none;
}

.terms-check a:hover {
  text-decoration: underline;
}

/* Password recovery message */
.recovery-message {
  text-align: center;
  margin-bottom: 30px;
  color: #1d2939;
  font-size: 16px;
  line-height: 1.5;
}

/* Responsive styles */
@media (max-width: 576px) {
  .account-page-container {
    padding: 15px;
  }

  .account-page-title {
    font-size: 28px;
  }

  .social-login {
    gap: 15px;
  }
}