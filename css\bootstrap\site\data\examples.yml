- category: Starters
  external: true
  description: "Functional examples of using Bootstrap in common JS frameworks like Webpack, Parcel, Vite, and more you can edit in StackBlitz."
  examples:
    - name: CDN starter
      description: "Instantly include Bootstrap's compiled CSS and JavaScript via the jsDelivr CDN."
      url: /examples/tree/main/starter
    - name: Sass & JS
      description: "Use npm to import and compile Bootstrap's Sass with Autoprefixer and Stylelint, plus our bundled JavaScript."
      url: /examples/tree/main/sass-js
    - name: Sass & ESM JS
      description: "Import and compile Bootstrap's Sass with Autoprefixer and Stylelint, and compile our source JavaScript with an ESM shim."
      url: /examples/tree/main/sass-js-esm
    - name: Bootstrap color modes
      description: "Import and compile Bootstrap's Sass with Stylelint, and the Bootstrap color modes."
      url: /examples/tree/main/color-modes
    - name: Bootstrap Icons
      description: "Import and compile <PERSON>trap's Sass with <PERSON><PERSON>, PurgeCSS, and the Bootstrap Icons web font."
      url: /examples/tree/main/icons-font
    - name: Parcel
      description: "Import and bundle Bootstrap's source Sass and JavaScript via Parcel."
      url: /examples/tree/main/parcel
      indexPath: src/index.html
      indexPath: src/index.html
    - name: React
      description: "Import and bundle Bootstrap's source Sass and JavaScript with React, Next.js, and React Bootstrap."
      url: /examples/tree/main/react-nextjs
      indexPath: src/pages/index.tsx
    - name: Vite
      description: "Import and bundle Bootstrap's source Sass and JavaScript with Vite."
      url: /examples/tree/main/vite
    - name: Vue
      description: "Import and bundle Bootstrap's source Sass and JavaScript with Vue and Vite."
      url: /examples/tree/main/vue
    - name: Webpack
      description: "Import and bundle Bootstrap's source Sass and JavaScript with Webpack."
      url: /examples/tree/main/webpack
      indexPath: src/index.html

- category: Snippets
  description: "Common patterns for building sites and apps that build on existing components and utilities with custom CSS and more."
  examples:
    - name: Headers
      description: "Display your branding, navigation, search, and more with these header components"
    - name: Heroes
      description: "Set the stage on your homepage with heroes that feature clear calls to action."
    - name: Features
      description: "Explain the features, benefits, or other details in your marketing content."
    - name: Sidebars
      description: "Common navigation patterns ideal for offcanvas or multi-column layouts."
    - name: Footers
      description: "Finish every page strong with an awesome footer, big or small."
    - name: Dropdowns
      description: "Enhance your dropdowns with filters, icons, custom styles, and more."
    - name: List groups
      description: "Extend list groups with utilities and custom styles for any content."
    - name: Modals
      description: "Transform modals to serve any purpose, from feature tours to dialogs."
    - name: Badges
      description: "Make badges work with custom inner HTML and new looks."
    - name: Breadcrumbs
      description: "Integrate custom icons and create stepper components."
    - name: Buttons
      description: "Create custom buttons for just about any use case with utilities."
    - name: Jumbotrons
      description: "Create modernized versions of the classic Bootstrap component."

- category: Custom Components
  description: "Brand-new components and templates to help folks quickly get started with Bootstrap and demonstrate best practices for adding onto the framework."
  examples:
    - name: Album
      description: "Simple one-page template for photo galleries, portfolios, and more."
    - name: Pricing
      description: "Example pricing page built with Cards and featuring a custom header and footer."
    - name: Checkout
      description: "Custom checkout form showing our form components and their validation features."
    - name: Product
      description: "Lean product-focused marketing page with extensive grid and image work."
    - name: Cover
      description: "A one-page template for building simple and beautiful home pages."
    - name: Carousel
      description: "Customize the navbar and carousel, then add some new components."
    - name: Blog
      description: "Magazine like blog template with header, navigation, featured content."
    - name: Dashboard
      description: "Basic admin dashboard shell with fixed sidebar and navbar."
    - name: Sign-in
      description: "Custom form layout and design for a simple sign in form."
    - name: Sticky footer
      description: "Attach a footer to the bottom of the viewport when page content is short."
    - name: Sticky footer navbar
      description: "Attach a footer to the bottom of the viewport with a fixed top navbar."
    - name: Jumbotron
      description: "Use utilities to recreate and enhance Bootstrap 4's jumbotron."

- category: Framework
  description: "Examples that focus on implementing uses of built-in components provided by Bootstrap."
  examples:
    - name: "Starter template"
      description: "Nothing but the basics: compiled CSS and JavaScript."
    - name: Grid
      description: "Multiple examples of grid layouts with all four tiers, nesting, and more."
    - name: Cheatsheet
      description: "Kitchen sink of Bootstrap components."
    - name: Cheatsheet RTL
      description: "Kitchen sink of Bootstrap components, RTL."

- category: Navbars
  description: "Taking the default navbar component and showing how it can be moved, placed, and extended."
  examples:
    - name: Navbars
      description: "Demonstration of all responsive and container options for the navbar."
    - name: Navbars offcanvas
      description: "Same as the Navbars example, but with our offcanvas component."
    - name: Navbar static
      description: "Single navbar example of a static top navbar along with some additional content."
    - name: Navbar fixed
      description: "Single navbar example with a fixed top navbar along with some additional content."
    - name: Navbar bottom
      description: "Single navbar example with a bottom navbar along with some additional content."
    - name: Offcanvas navbar
      description: "Turn your expandable navbar into a sliding offcanvas menu (doesn't use our offcanvas component)."

- category: RTL
  description: "See Bootstrap's RTL version in action with these modified Custom Components examples."
  examples:
    - name: Album RTL
      description: "Simple one-page template for photo galleries, portfolios, and more."
    - name: Checkout RTL
      description: "Custom checkout form showing our form components and their validation features."
    - name: Carousel RTL
      description: "Customize the navbar and carousel, then add some new components."
    - name: Blog RTL
      description: "Magazine like blog template with header, navigation, featured content."
    - name: Dashboard RTL
      description: "Basic admin dashboard shell with fixed sidebar and navbar."

- category: Integrations
  description: "Integrations with external libraries."
  examples:
    - name: "Masonry"
      description: "Combine the powers of the Bootstrap grid and the Masonry layout."
