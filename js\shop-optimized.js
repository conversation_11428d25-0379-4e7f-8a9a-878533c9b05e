/**
 * Optimized Shop JavaScript for Large Product Catalogs
 * Focuses on performance and smooth user experience
 */

(function($) {
    'use strict';

    // Configuration
    const config = {
        debounceDelay: 300,
        cacheExpiry: 300000, // 5 minutes
        maxRetries: 3,
        loadingDelay: 100
    };

    // Cache for storing filter results
    const filterCache = new Map();

    // Request queue to prevent multiple simultaneous requests
    let currentRequest = null;
    let requestQueue = [];

    /**
     * Initialize optimized shop functionality
     */
    function initOptimizedShop() {
        initViewModeToggle();
        initFilterHandling();
        initSortingHandling();
        initLazyLoading();
        initPerformanceMonitoring();
        initAccessibility();
        
        console.log('Optimized shop initialized');
    }

    /**
     * Initialize view mode toggle functionality
     */
    function initViewModeToggle() {
        const savedViewMode = localStorage.getItem('shop_view_mode') || 'grid';
        setViewMode(savedViewMode);

        $('.view-mode-btn').on('click', function() {
            const viewMode = $(this).data('view');
            setViewMode(viewMode);
            localStorage.setItem('shop_view_mode', viewMode);
        });
    }

    /**
     * Set view mode (grid or list)
     */
    function setViewMode(mode) {
        $('.view-mode-btn').removeClass('active');
        $(`.view-mode-btn[data-view="${mode}"]`).addClass('active');
        $('#shop-products').attr('data-view', mode);
        
        // Trigger layout recalculation for better performance
        requestAnimationFrame(() => {
            $(window).trigger('resize');
        });
    }

    /**
     * Initialize optimized filter handling
     */
    function initFilterHandling() {
        const $form = $('#shop-filters-form');
        const $clearButton = $('.clear-filters');

        // Debounced filter change handler
        const debouncedFilterChange = debounce(handleFilterChange, config.debounceDelay);

        // Handle filter changes
        $form.on('change', 'input[type="checkbox"], input[type="radio"]', debouncedFilterChange);
        $form.on('input', 'input[type="number"]', debouncedFilterChange);

        // Handle form submission
        $form.on('submit', function(e) {
            e.preventDefault();
            handleFilterChange();
        });

        // Handle clear filters
        $clearButton.on('click', function(e) {
            e.preventDefault();
            clearAllFilters();
        });

        // Update clear button visibility
        updateClearButtonVisibility();
    }

    /**
     * Handle filter changes with optimization
     */
    function handleFilterChange() {
        const formData = new FormData(document.getElementById('shop-filters-form'));
        const params = new URLSearchParams();

        // Build parameters from form data
        for (let [key, value] of formData.entries()) {
            if (value && value.trim() !== '') {
                params.append(key, value);
            }
        }

        // Check cache first
        const cacheKey = params.toString();
        const cachedResult = getCachedResult(cacheKey);
        
        if (cachedResult) {
            updateShopContent(cachedResult);
            updateURL(params);
            return;
        }

        // Show loading state
        showLoadingState();

        // Cancel any pending request
        if (currentRequest) {
            currentRequest.abort();
        }

        // Build URL for AJAX request
        const baseUrl = shopOptimized.shop_url;
        const ajaxUrl = baseUrl + (baseUrl.includes('?') ? '&' : '?') + params.toString() + '&ajax=1';

        // Make AJAX request
        currentRequest = $.ajax({
            url: ajaxUrl,
            type: 'GET',
            timeout: 30000, // 30 second timeout
            success: function(response) {
                try {
                    const $response = $(response);
                    const $newProducts = $response.find('#shop-products');
                    const $newPagination = $response.find('.woocommerce-pagination');
                    const $newResultsInfo = $response.find('.results-count');

                    if ($newProducts.length) {
                        // Cache the result
                        setCachedResult(cacheKey, {
                            products: $newProducts.html(),
                            pagination: $newPagination.length ? $newPagination[0].outerHTML : '',
                            resultsInfo: $newResultsInfo.length ? $newResultsInfo.html() : ''
                        });

                        // Update content
                        updateShopContent({
                            products: $newProducts.html(),
                            pagination: $newPagination.length ? $newPagination[0].outerHTML : '',
                            resultsInfo: $newResultsInfo.length ? $newResultsInfo.html() : ''
                        });

                        // Update URL
                        updateURL(params);

                        // Reinitialize lazy loading for new images
                        initLazyLoading();
                    }
                } catch (error) {
                    console.error('Error processing AJAX response:', error);
                    handleAjaxError();
                }
            },
            error: function(xhr, status, error) {
                if (status !== 'abort') {
                    console.error('AJAX request failed:', error);
                    handleAjaxError();
                }
            },
            complete: function() {
                hideLoadingState();
                currentRequest = null;
                processRequestQueue();
            }
        });
    }

    /**
     * Update shop content with new data
     */
    function updateShopContent(data) {
        const $productsContainer = $('#shop-products');
        const $pagination = $('.woocommerce-pagination');
        const $resultsInfo = $('.results-count');

        // Update products with fade effect for better UX
        $productsContainer.fadeOut(200, function() {
            $(this).html(data.products).fadeIn(200);
        });

        // Update pagination
        if (data.pagination) {
            $pagination.replaceWith(data.pagination);
        } else {
            $pagination.remove();
        }

        // Update results info
        if (data.resultsInfo) {
            $resultsInfo.html(data.resultsInfo);
        }

        // Scroll to products smoothly
        $('html, body').animate({
            scrollTop: $productsContainer.offset().top - 100
        }, 300);

        // Update clear button visibility
        updateClearButtonVisibility();

        // Trigger custom event
        $(document).trigger('shop:contentUpdated');
    }

    /**
     * Initialize sorting handling
     */
    function initSortingHandling() {
        $('#shop-orderby').on('change', function() {
            const orderby = $(this).val();
            const currentUrl = new URL(window.location);
            
            if (orderby && orderby !== 'menu_order') {
                currentUrl.searchParams.set('orderby', orderby);
            } else {
                currentUrl.searchParams.delete('orderby');
            }

            // Use optimized filter change handler
            handleFilterChange();
        });
    }

    /**
     * Initialize lazy loading for images
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            img.classList.remove('lazy');
                            observer.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe all lazy images
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for older browsers
            $('img[data-src]').each(function() {
                $(this).attr('src', $(this).data('src')).removeAttr('data-src');
            });
        }
    }

    /**
     * Initialize performance monitoring
     */
    function initPerformanceMonitoring() {
        // Monitor page load performance
        if ('performance' in window) {
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    if (perfData) {
                        console.log('Shop page load time:', perfData.loadEventEnd - perfData.fetchStart, 'ms');
                    }
                }, 0);
            });
        }

        // Monitor AJAX request performance
        $(document).ajaxComplete(function(event, xhr, settings) {
            if (settings.url && settings.url.includes('ajax=1')) {
                console.log('AJAX request completed in:', xhr.responseTime || 'unknown', 'ms');
            }
        });
    }

    /**
     * Initialize accessibility features
     */
    function initAccessibility() {
        // Add ARIA labels and roles
        $('.filter-option input').each(function() {
            const $input = $(this);
            const $label = $input.closest('.filter-option').find('.filter-label');
            const labelText = $label.text().trim();
            
            if (labelText) {
                $input.attr('aria-label', labelText);
            }
        });

        // Handle keyboard navigation
        $('.filter-option').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).find('input').click();
            }
        });

        // Announce content changes to screen readers
        $(document).on('shop:contentUpdated', function() {
            const $resultsInfo = $('.results-count');
            if ($resultsInfo.length) {
                $resultsInfo.attr('aria-live', 'polite');
            }
        });
    }

    /**
     * Clear all filters
     */
    function clearAllFilters() {
        const $form = $('#shop-filters-form');
        
        // Clear all inputs
        $form.find('input[type="checkbox"], input[type="radio"]').prop('checked', false);
        $form.find('input[type="number"]').val('');
        $('#shop-orderby').val('menu_order');

        // Clear URL parameters and reload
        const baseUrl = shopOptimized.shop_url;
        window.location.href = baseUrl;
    }

    /**
     * Update clear button visibility
     */
    function updateClearButtonVisibility() {
        const $form = $('#shop-filters-form');
        const hasActiveFilters = $form.find('input:checked, input[type="number"][value!=""]').length > 0 ||
                                $('#shop-orderby').val() !== 'menu_order';
        
        $('.clear-filters').toggle(hasActiveFilters);
    }

    /**
     * Show loading state
     */
    function showLoadingState() {
        $('#shop-loading').fadeIn(config.loadingDelay);
        $('#shop-products').css('opacity', '0.6');
    }

    /**
     * Hide loading state
     */
    function hideLoadingState() {
        $('#shop-loading').fadeOut(config.loadingDelay);
        $('#shop-products').css('opacity', '1');
    }

    /**
     * Handle AJAX errors
     */
    function handleAjaxError() {
        // Show error message
        const errorHtml = `
            <div class="alert alert-warning" role="alert">
                <h4>${shopOptimized.error_title || 'Loading Error'}</h4>
                <p>${shopOptimized.error_message || 'Unable to load products. Please refresh the page.'}</p>
                <button type="button" class="btn btn-primary" onclick="location.reload()">
                    ${shopOptimized.refresh_text || 'Refresh Page'}
                </button>
            </div>
        `;
        
        $('#shop-products').html(errorHtml);
    }

    /**
     * Update URL without page reload
     */
    function updateURL(params) {
        const baseUrl = shopOptimized.shop_url;
        const newUrl = baseUrl + (params.toString() ? '?' + params.toString() : '');
        
        if (window.location.href !== newUrl) {
            history.pushState({ shopFilters: true }, '', newUrl);
        }
    }

    /**
     * Cache management
     */
    function getCachedResult(key) {
        const cached = filterCache.get(key);
        if (cached && (Date.now() - cached.timestamp) < config.cacheExpiry) {
            return cached.data;
        }
        return null;
    }

    function setCachedResult(key, data) {
        filterCache.set(key, {
            data: data,
            timestamp: Date.now()
        });

        // Limit cache size
        if (filterCache.size > 50) {
            const firstKey = filterCache.keys().next().value;
            filterCache.delete(firstKey);
        }
    }

    /**
     * Process request queue
     */
    function processRequestQueue() {
        if (requestQueue.length > 0) {
            const nextRequest = requestQueue.shift();
            nextRequest();
        }
    }

    /**
     * Debounce utility function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Handle browser back/forward buttons
     */
    window.addEventListener('popstate', function(e) {
        if (e.state && e.state.shopFilters) {
            location.reload();
        }
    });

    // Initialize when document is ready
    $(document).ready(function() {
        initOptimizedShop();
    });

    // Expose functions globally for external use
    window.shopOptimized = window.shopOptimized || {};
    window.shopOptimized.functions = {
        setViewMode: setViewMode,
        clearAllFilters: clearAllFilters,
        showLoadingState: showLoadingState,
        hideLoadingState: hideLoadingState
    };

})(jQuery);
