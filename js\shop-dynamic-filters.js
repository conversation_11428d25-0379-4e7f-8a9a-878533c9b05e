// /**
//  * Dynamic Shop Filters JavaScript
//  */

// (function($) {
//   'use strict';

//   // Initialize when document is ready
//   $(document).ready(function() {
//     // Initialize dynamic filter relationships
//     initDynamicFilters();

//     // Initialize price range slider
//     initPriceRangeSlider();

//     // Initialize view mode switching
//     initViewMode();

//     // Initialize sorting
//     initSorting();
//   });

//   /**
//    * Initialize dynamic filters
//    */
//   function initDynamicFilters() {
//     // Handle category link clicks
//     $('.category-link').on('click', function(e) {
//       e.preventDefault();

//       // Get category ID and related data
//       const categoryId = $(this).data('category-id');
//       const relatedBrands = $(this).data('brands').toString().split(',');
//       const relatedAttributes = $(this).data('attributes');

//       // Update URL with the category parameter
//       window.location.href = $(this).attr('href');
//     });

//     // Handle brand link clicks
//     $('.brand-logo-item').on('click', function(e) {
//       e.preventDefault();

//       // Get brand ID and related data
//       const brandId = $(this).data('brand-id');
//       const relatedCategories = $(this).data('categories').toString().split(',');

//       // Update URL with the brand parameter
//       window.location.href = $(this).attr('href');
//     });

//     // Show all brands button
//     $('#show-all-brands-btn').on('click', function(e) {
//       e.preventDefault();

//       // Show all brands
//       $('.brand-logo-item').parent().show();
//       $(this).parent().hide();
//     });

//     // Check if we have a selected category or brand
//     const urlParams = new URLSearchParams(window.location.search);
//     const selectedCategory = urlParams.get('product_cat');
//     const selectedBrand = urlParams.get('brand');

//     if (selectedCategory) {
//       // Find the category link
//       const $categoryLink = $(`.category-link[data-category-id="${selectedCategory}"]`);

//       if ($categoryLink.length) {
//         // Get related brands
//         const relatedBrands = $categoryLink.data('brands').toString().split(',').filter(Boolean);

//         // Filter brands to show only related ones
//         if (relatedBrands.length) {
//           $('.brand-logo-item').each(function() {
//             const brandId = $(this).data('brand-id');
//             if (relatedBrands.includes(brandId.toString())) {
//               $(this).parent().show();
//             } else {
//               $(this).parent().hide();
//             }
//           });
//         }

//         // Show attributes section
//         $('#attributes-section').show();

//         // Parse attributes data
//         const attributesData = $categoryLink.data('attributes');
//         if (attributesData) {
//           const attributePairs = attributesData.split(';');

//           attributePairs.forEach(pair => {
//             const [attrName, termIds] = pair.split(':');
//             const termIdArray = termIds.split(',');

//             // Show this attribute filter
//             $(`.attribute-filter[data-attribute="${attrName}"]`).show();

//             // Show only related terms
//             $(`.attribute-filter[data-attribute="${attrName}"] .attribute-item`).each(function() {
//               const termId = $(this).data('term-id');
//               if (termIdArray.includes(termId.toString())) {
//                 $(this).show();
//               } else {
//                 $(this).hide();
//               }
//             });
//           });
//         }
//       }
//     } else if (selectedBrand) {
//       // Find the brand item
//       const $brandItem = $(`.brand-logo-item[data-brand="${selectedBrand}"]`);

//       if ($brandItem.length) {
//         // Get related categories
//         const relatedCategories = $brandItem.data('categories').toString().split(',').filter(Boolean);

//         // Highlight related categories
//         if (relatedCategories.length) {
//           $('.category-link').each(function() {
//             const categoryId = $(this).data('category-id');
//             if (relatedCategories.includes(categoryId.toString())) {
//               $(this).parent().addClass('related');
//             }
//           });
//         }

//         // Show attributes section based on brand-attribute relationships
//         if (filterRelationships && filterRelationships.brandAttributes) {
//           const brandId = $brandItem.data('brand-id');
//           const brandAttributes = filterRelationships.brandAttributes[brandId];

//           if (brandAttributes) {
//             $('#attributes-section').show();

//             // Show relevant attribute filters
//             for (const attrName in brandAttributes) {
//               const termIds = brandAttributes[attrName];

//               // Show this attribute filter
//               $(`.attribute-filter[data-attribute="${attrName}"]`).show();

//               // Show only related terms
//               $(`.attribute-filter[data-attribute="${attrName}"] .attribute-item`).each(function() {
//                 const termId = $(this).data('term-id');
//                 if (termIds.includes(parseInt(termId))) {
//                   $(this).show();
//                 } else {
//                   $(this).hide();
//                 }
//               });
//             }
//           }
//         }
//       }
//     }
//   }

//   /**
//    * Initialize price range slider
//    */
//   function initPriceRangeSlider() {
//     const $slider = $('#price-range-slider');

//     if ($slider.length && $.fn.slider) {
//       const minPrice = parseInt($slider.data('min'));
//       const maxPrice = parseInt($slider.data('max'));
//       const currentMin = parseInt($slider.data('current-min'));
//       const currentMax = parseInt($slider.data('current-max'));

//       $slider.slider({
//         range: true,
//         min: minPrice,
//         max: maxPrice,
//         values: [currentMin, currentMax],
//         slide: function(event, ui) {
//           $('#min-price').val(ui.values[0]);
//           $('#max-price').val(ui.values[1]);
//         }
//       });

//       // Update slider when inputs change
//       $('#min-price, #max-price').on('change', function() {
//         const minVal = parseInt($('#min-price').val()) || minPrice;
//         const maxVal = parseInt($('#max-price').val()) || maxPrice;

//         $slider.slider('values', [minVal, maxVal]);
//       });

//       // Apply price filter button
//       $('.price-filter-apply-btn').on('click', function(e) {
//         e.preventDefault();

//         // Start with the shop URL
//         let url = woocommerceShopUrl;

//         // Check if we're on a category page and add the category parameter
//         if ($('input[name="product_cat"]').length) {
//           url = addQueryParam(url, 'product_cat', $('input[name="product_cat"]').val());
//         }

//         // Add min and max price parameters
//         url = addQueryParam(url, 'min_price', $('#min-price').val());
//         url = addQueryParam(url, 'max_price', $('#max-price').val());

//         // Redirect to the URL with price filter
//         window.location.href = url;
//       });
//     }
//   }

//   /**
//    * Helper function to add query parameters to URL
//    */
//   function addQueryParam(url, key, value) {
//     const re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
//     const separator = url.indexOf('?') !== -1 ? "&" : "?";

//     if (url.match(re)) {
//       return url.replace(re, '$1' + key + "=" + value + '$2');
//     } else {
//       return url + separator + key + "=" + value;
//     }
//   }

//   /**
//    * Initialize attribute filters
//    */
//   function initAttributeFilters() {
//     // Attribute filters now use direct links, no JavaScript needed
//   }



//   /**
//    * Initialize view mode switching
//    */
//   function initViewMode() {
//     $('.view-mode-btn').on('click', function() {
//       const viewMode = $(this).data('view');

//       // Update active button
//       $('.view-mode-btn').removeClass('active');
//       $(this).addClass('active');

//       // Update products grid
//       if (viewMode === 'grid') {
//         $('#shop-products').removeClass('list-view').addClass('grid-view');
//       } else {
//         $('#shop-products').removeClass('grid-view').addClass('list-view');
//       }

//       // Store preference in localStorage
//       localStorage.setItem('shop_view_mode', viewMode);
//     });

//     // Check for stored preference
//     const storedViewMode = localStorage.getItem('shop_view_mode');
//     if (storedViewMode) {
//       $('.view-mode-btn[data-view="' + storedViewMode + '"]').trigger('click');
//     }
//   }

//   /**
//    * Initialize sorting
//    */
//   function initSorting() {
//     $('#shop-orderby').on('change', function() {
//       const orderby = $(this).val();

//       // Add orderby parameter to form
//       if (!$('input[name="orderby"]').length) {
//         $('<input>').attr({
//           type: 'hidden',
//           name: 'orderby',
//           value: orderby
//         }).appendTo('#shop-filters-form');
//       } else {
//         $('input[name="orderby"]').val(orderby);
//       }

//       // Submit form
//       $('#shop-filters-form').submit();
//     });
//   }

// })(jQuery);
