<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="{{ .Page.Params.description | default .Site.Params.description | markdownify }}">
<meta name="author" content="{{ .Site.Params.authors }}">
<meta name="generator" content="Hugo {{ hugo.Version }}">

<meta name="docsearch:language" content="en">
<meta name="docsearch:version" content="{{ .Site.Params.docs_version }}">

<title>{{ if .IsHome }}{{ .Site.Title | markdownify }} · {{ .Site.Params.subtitle | markdownify }}{{ else }}{{ .Title | markdownify }} · {{ .Site.Title | markdownify }} v{{ .Site.Params.docs_version }}{{ end }}</title>

<link rel="canonical" href="{{ .Permalink }}">

<link rel="preconnect" href="https://AK7KMZKZHQ-dsn.algolia.net" crossorigin>

{{ with .Params.robots -}}
<meta name="robots" content="{{ . }}">
{{- end }}

{{- $colorModeJS := printf "/docs/%s/assets/js/color-modes.js" $.Site.Params.docs_version -}}
<script src="{{ $colorModeJS | relURL }}"></script>

{{ partial "stylesheet" . }}
{{ partial "favicons" . }}
{{ partial "social" . }}
{{ partial "analytics" . }}
