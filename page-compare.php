<?php
/**
 * Template Name: Compare Products
 * 
 * @package tendeal
 */

get_header(); ?>

<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <h1 class="page-title mb-4">Compare Products</h1>
            
            <div id="compare-products-container">
                <?php
                // Get compare items
                $user_id = get_current_user_id();
                $compare_items = array();
                
                if ($user_id) {
                    $compare_items = get_user_meta($user_id, 'tendeal_compare_items', true);
                } else {
                    if (session_id()) {
                        $compare_items = isset($_SESSION['tendeal_compare_items']) ? $_SESSION['tendeal_compare_items'] : array();
                    }
                }
                
                if (!is_array($compare_items)) {
                    $compare_items = array();
                }
                
                if (empty($compare_items)) {
                    echo '<div class="text-center py-5">';
                    echo '<h3>No products to compare</h3>';
                    echo '<p class="text-muted">Add products to your compare list to see them here.</p>';
                    echo '<a href="' . wc_get_page_permalink('shop') . '" class="btn btn-primary">Continue Shopping</a>';
                    echo '</div>';
                } else {
                    // Display compare table
                    echo '<div class="compare-table-wrapper">';
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-bordered compare-table">';
                    
                    // Product images and names
                    echo '<tr class="product-images">';
                    echo '<td class="compare-label"><strong>Product</strong></td>';
                    foreach ($compare_items as $product_id) {
                        $product = wc_get_product($product_id);
                        if ($product) {
                            echo '<td class="text-center">';
                            echo '<div class="compare-product-item">';
                            echo '<a href="' . $product->get_permalink() . '">';
                            echo $product->get_image('medium');
                            echo '</a>';
                            echo '<h5 class="mt-2"><a href="' . $product->get_permalink() . '">' . $product->get_name() . '</a></h5>';
                            echo '<button class="btn btn-sm btn-outline-danger remove-from-compare" data-product-id="' . $product_id . '">Remove</button>';
                            echo '</div>';
                            echo '</td>';
                        }
                    }
                    echo '</tr>';
                    
                    // Price
                    echo '<tr>';
                    echo '<td class="compare-label"><strong>Price</strong></td>';
                    foreach ($compare_items as $product_id) {
                        $product = wc_get_product($product_id);
                        if ($product) {
                            echo '<td class="text-center">';
                            echo '<span class="price">' . $product->get_price_html() . '</span>';
                            echo '</td>';
                        }
                    }
                    echo '</tr>';
                    
                    // Rating
                    echo '<tr>';
                    echo '<td class="compare-label"><strong>Rating</strong></td>';
                    foreach ($compare_items as $product_id) {
                        $product = wc_get_product($product_id);
                        if ($product) {
                            echo '<td class="text-center">';
                            $rating = $product->get_average_rating();
                            if ($rating > 0) {
                                echo wc_get_rating_html($rating);
                            } else {
                                echo '<span class="text-muted">No reviews</span>';
                            }
                            echo '</td>';
                        }
                    }
                    echo '</tr>';
                    
                    // Stock status
                    echo '<tr>';
                    echo '<td class="compare-label"><strong>Availability</strong></td>';
                    foreach ($compare_items as $product_id) {
                        $product = wc_get_product($product_id);
                        if ($product) {
                            echo '<td class="text-center">';
                            if ($product->is_in_stock()) {
                                echo '<span class="badge bg-success">In Stock</span>';
                            } else {
                                echo '<span class="badge bg-danger">Out of Stock</span>';
                            }
                            echo '</td>';
                        }
                    }
                    echo '</tr>';
                    
                    // Description
                    echo '<tr>';
                    echo '<td class="compare-label"><strong>Description</strong></td>';
                    foreach ($compare_items as $product_id) {
                        $product = wc_get_product($product_id);
                        if ($product) {
                            echo '<td>';
                            $description = $product->get_short_description();
                            if (empty($description)) {
                                $description = wp_trim_words($product->get_description(), 20);
                            }
                            echo '<p>' . $description . '</p>';
                            echo '</td>';
                        }
                    }
                    echo '</tr>';
                    
                    // Add to cart buttons
                    echo '<tr>';
                    echo '<td class="compare-label"><strong>Action</strong></td>';
                    foreach ($compare_items as $product_id) {
                        $product = wc_get_product($product_id);
                        if ($product) {
                            echo '<td class="text-center">';
                            if ($product->is_purchasable() && $product->is_in_stock()) {
                                echo '<a href="' . $product->add_to_cart_url() . '" class="btn btn-primary btn-sm">Add to Cart</a>';
                            } else {
                                echo '<a href="' . $product->get_permalink() . '" class="btn btn-outline-primary btn-sm">View Product</a>';
                            }
                            echo '</td>';
                        }
                    }
                    echo '</tr>';
                    
                    echo '</table>';
                    echo '</div>';
                    echo '</div>';
                    
                    // Clear all button
                    echo '<div class="text-center mt-4">';
                    echo '<button class="btn btn-outline-danger" id="clear-all-compare">Clear All</button>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<style>
.compare-table {
    min-width: 800px;
}

.compare-label {
    background-color: #f8f9fa;
    font-weight: 600;
    vertical-align: middle;
    width: 150px;
}

.compare-product-item img {
    max-width: 150px;
    height: auto;
}

.compare-product-item h5 {
    font-size: 1rem;
    margin: 10px 0;
}

.compare-product-item h5 a {
    text-decoration: none;
    color: #333;
}

.compare-product-item h5 a:hover {
    color: #ea9c00;
}

.remove-from-compare {
    margin-top: 10px;
}

.price {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ea9c00;
}

@media (max-width: 768px) {
    .compare-table-wrapper {
        overflow-x: auto;
    }
    
    .compare-product-item img {
        max-width: 100px;
    }
    
    .compare-product-item h5 {
        font-size: 0.9rem;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Remove from compare
    $('.remove-from-compare').on('click', function() {
        var productId = $(this).data('product-id');
        var $button = $(this);
        
        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'tendeal_remove_from_compare',
                product_id: productId,
                nonce: wc_add_to_cart_params.compare_nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                }
            }
        });
    });
    
    // Clear all compare
    $('#clear-all-compare').on('click', function() {
        if (confirm('Are you sure you want to clear all products from compare?')) {
            $.ajax({
                url: wc_add_to_cart_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'tendeal_clear_compare',
                    nonce: wc_add_to_cart_params.compare_nonce
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    }
                }
            });
        }
    });
});
</script>

<?php get_footer(); ?>
