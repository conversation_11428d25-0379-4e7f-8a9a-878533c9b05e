---
layout: examples
title: Buttons
body_class: ""
---

<svg xmlns="http://www.w3.org/2000/svg" class="d-none">
  <symbol id="arrow-right-short" viewBox="0 0 16 16">
    <path fill-rule="evenodd" d="M4 8a.5.5 0 0 1 .5-.5h5.793L8.146 5.354a.5.5 0 1 1 .708-.708l3 3a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708-.708L10.293 8.5H4.5A.5.5 0 0 1 4 8z"/>
  </symbol>
  <symbol id="x-lg" viewBox="0 0 16 16">
    <path fill-rule="evenodd" d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z"/>
    <path fill-rule="evenodd" d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z"/>
  </symbol>
</svg>

<div class="d-flex gap-2 justify-content-center py-5">
  <button class="btn btn-primary rounded-pill px-3" type="button">Primary</button>
  <button class="btn btn-secondary rounded-pill px-3" type="button">Secondary</button>
  <button class="btn btn-success rounded-pill px-3" type="button">Success</button>
  <button class="btn btn-danger rounded-pill px-3" type="button">Danger</button>
  <button class="btn btn-warning rounded-pill px-3" type="button">Warning</button>
  <button class="btn btn-info rounded-pill px-3" type="button">Info</button>
  <button class="btn btn-light rounded-pill px-3" type="button">Light</button>
  <button class="btn btn-dark rounded-pill px-3" type="button">Dark</button>
  <button class="btn btn-link rounded-pill px-3" type="button">Link</button>
</div>

<div class="b-example-divider"></div>

<div class="col-lg-6 col-xxl-4 my-5 mx-auto">
  <div class="d-grid gap-2">
    <button class="btn btn-outline-secondary" type="button">Secondary action</button>
    <button class="btn btn-primary" type="button">Primary action</button>
  </div>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <button class="btn btn-primary d-inline-flex align-items-center" type="button">
    Primary icon
    <svg class="bi ms-1" width="20" height="20"><use xlink:href="#arrow-right-short"/></svg>
  </button>
  <button class="btn btn-outline-secondary d-inline-flex align-items-center" type="button">
    Secondary icon
    <svg class="bi ms-1" width="20" height="20"><use xlink:href="#arrow-right-short"/></svg>
  </button>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <button class="btn btn-primary" type="button" disabled>
    <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
    <span class="visually-hidden" role="status">Loading...</span>
  </button>
  <button class="btn btn-primary" type="button" disabled>
    <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
    <span role="status">Loading...</span>
  </button>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center pt-5 pb-4">
  <button class="btn btn-primary rounded-circle p-2 lh-1" type="button">
    <svg class="bi" width="16" height="16"><use xlink:href="#x-lg"/></svg>
    <span class="visually-hidden">Dismiss</span>
  </button>
  <button class="btn btn-outline-primary rounded-circle p-2 lh-1" type="button">
    <svg class="bi" width="16" height="16"><use xlink:href="#x-lg"/></svg>
    <span class="visually-hidden">Dismiss</span>
  </button>
</div>

<div class="d-flex gap-2 justify-content-center pb-5">
  <button class="btn btn-primary rounded-circle p-3 lh-1" type="button">
    <svg class="bi" width="24" height="24"><use xlink:href="#x-lg"/></svg>
    <span class="visually-hidden">Dismiss</span>
  </button>
  <button class="btn btn-outline-primary rounded-circle p-3 lh-1" type="button">
    <svg class="bi" width="24" height="24"><use xlink:href="#x-lg"/></svg>
    <span class="visually-hidden">Dismiss</span>
  </button>
</div>

<div class="b-example-divider"></div>
