<?php
/**
 * Plugin Name: WooCommerce Dynamic Filters Shop Template
 * Description: Sets the dynamic filters template as the default shop page
 * Version: 1.0
 * Author: Your Name
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Set the dynamic filters template as the default shop page
 */
function set_dynamic_filters_as_shop_template() {
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return;
    }

    // Get the shop page ID
    $shop_page_id = wc_get_page_id('shop');

    // If shop page exists
    if ($shop_page_id > 0) {
        // Update the page template
        update_post_meta($shop_page_id, '_wp_page_template', 'shop-dynamic-filters.php');
    }
}

// Run once when the plugin is activated
register_activation_hook(__FILE__, 'set_dynamic_filters_as_shop_template');

/**
 * Override the WooCommerce template for all product archive pages
 */
function override_woocommerce_shop_template($template) {
    // Apply to shop page, category pages, tag pages, and brand pages
    if (is_shop() || is_product_category() || is_product_tag() || is_tax('product_brand')) {
        $new_template = locate_template('shop-dynamic-filters.php');
        if (!empty($new_template)) {
            return $new_template;
        }
    }
    return $template;
}
add_filter('template_include', 'override_woocommerce_shop_template', 99);

/**
 * Add our template to the page template dropdown
 */
function add_dynamic_filters_template_to_dropdown($templates) {
    $templates['shop-dynamic-filters.php'] = 'Shop with Dynamic Filters';
    return $templates;
}
add_filter('theme_page_templates', 'add_dynamic_filters_template_to_dropdown');