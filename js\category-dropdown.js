/**
 * Category Dropdown Functionality
 */
jQuery(document).ready(function($) {
  // Category dropdown toggle
  const $categoryMenuToggle = $('#category-menu-toggle');
  const $categoryDropdown = $('#category-menu-dropdown');
  
  // Debug elements
  console.log('Category dropdown script loaded');
  console.log('Toggle button:', $categoryMenuToggle.length);
  console.log('Dropdown:', $categoryDropdown.length);

  if ($categoryMenuToggle.length && $categoryDropdown.length) {
    // Toggle dropdown when clicking the button
    // $categoryMenuToggle.on('click', function(e) {
    //   e.preventDefault();
    //   console.log('Category button clicked');
    //   $categoryDropdown.toggleClass('active');
    // });

    // Close dropdown when clicking outside
    // $(document).on('click', function(event) {
    //   if (!$categoryMenuToggle.is(event.target) && 
    //       !$categoryDropdown.is(event.target) && 
    //       $categoryDropdown.has(event.target).length === 0) {
    //     $categoryDropdown.removeClass('active');
    //   }
    // });

    // Handle category hover functionality
    const $categoryItems = $('.category-item');
    const $subcategoryContents = $('.subcategory-content');

    // Handle hover on desktop
    if (window.innerWidth > 768) {
      $categoryItems.on('mouseenter', function() {
        // Remove active class from all items
        $categoryItems.removeClass('active');
        
        // Add active class to current item
        $(this).addClass('active');
        
        // Hide all subcategory contents
        $subcategoryContents.hide();
        
        // Show the corresponding subcategory content
        const categoryId = $(this).data('category-id');
        $('#subcategory-' + categoryId).show();
      });
    } else {
      // Handle click on mobile
      $categoryItems.on('click', function(e) {
        // Only handle the click if it's not on the link
        if (!$(e.target).is('a')) {
          e.preventDefault();
          
          // Toggle active class
          $categoryItems.not(this).removeClass('active');
          $(this).toggleClass('active');
          
          // Hide all subcategory contents
          $subcategoryContents.hide();
          
          // Show the corresponding subcategory content
          const categoryId = $(this).data('category-id');
          $('#subcategory-' + categoryId).show();
        }
      });
    }
  }
});
