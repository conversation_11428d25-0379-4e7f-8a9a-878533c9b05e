<?php
/**
 * Enhanced Checkout Form
 *
 * This template overrides the default WooCommerce checkout form
 * with a multi-step checkout process that supports multiple shipping addresses.
 *
 * @package TenDeal
 */

defined('ABSPATH') || exit;

do_action('woocommerce_before_checkout_form', $checkout);

// If checkout registration is disabled and not logged in, the user cannot checkout.
if (!$checkout->is_registration_enabled() && $checkout->is_registration_required() && !is_user_logged_in()) {
    echo esc_html(apply_filters('woocommerce_checkout_must_be_logged_in_message', __('You must be logged in to checkout.', 'woocommerce')));
    return;
}


?>

<div class="checkout-container">
    <div class="container">
        <div class="checkout-header">
            <h2><?php esc_html_e('Checkout', 'woocommerce'); ?></h2>
            <p><?php esc_html_e('Complete your purchase by providing your shipping and payment details', 'woocommerce'); ?></p>
        </div>

        <div class="checkout-progress">
            <div class="progress-step active" data-step="1">
                <div class="step-icon">
                    <i data-feather="map-pin"></i>
                </div>
                <div class="step-label"><?php esc_html_e('Shipping', 'woocommerce'); ?></div>
            </div>
            <div class="progress-step" data-step="2">
                <div class="step-icon">
                    <i data-feather="credit-card"></i>
                </div>
                <div class="step-label"><?php esc_html_e('Payment', 'woocommerce'); ?></div>
            </div>
            <div class="progress-step" data-step="3">
                <div class="step-icon">
                    <i data-feather="check-circle"></i>
                </div>
                <div class="step-label"><?php esc_html_e('Confirmation', 'woocommerce'); ?></div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <form name="checkout" method="post" class="checkout woocommerce-checkout" action="<?php echo esc_url(wc_get_checkout_url()); ?>" enctype="multipart/form-data">

                    <?php if ($checkout->get_checkout_fields()) : ?>

                        <!-- Step 1: Shipping Details -->
                        <div class="checkout-step" data-step="0">
                            <div class="checkout-form">
                                <h3><?php esc_html_e('Shipping Information', 'woocommerce'); ?></h3>

                                <?php do_action('woocommerce_checkout_before_customer_details'); ?>

                                <div class="row">
                                    <div class="col-md-12">
                                        <?php do_action('woocommerce_checkout_billing'); ?>
                                    </div>
                                </div>

                                <?php if (WC()->cart && method_exists(WC()->cart, 'needs_shipping') && method_exists(WC()->cart, 'show_shipping') && WC()->cart->needs_shipping() && WC()->cart->show_shipping()) : ?>
                                    <div class="row mt-4">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="ship-to-different-address-checkbox" class="form-label">
                                                    <input id="ship-to-different-address-checkbox" class="woocommerce-form__input woocommerce-form__input-checkbox" type="checkbox" name="ship_to_different_address" value="1" />
                                                    <?php esc_html_e('Ship to a different address?', 'woocommerce'); ?>
                                                </label>
                                            </div>

                                            <div class="shipping_address" style="display: none;">
                                                <?php do_action('woocommerce_checkout_shipping'); ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php do_action('woocommerce_checkout_after_customer_details'); ?>
                            </div>

                            <div class="checkout-actions">
                                <button type="button" class="btn btn-primary btn-next"><?php esc_html_e('Continue to Payment', 'woocommerce'); ?></button>
                            </div>
                        </div>

                        <!-- Step 2: Payment Method -->
                        <div class="checkout-step" data-step="1" style="display: none;">
                            <div class="checkout-form">
                                <h3><?php esc_html_e('Payment Method', 'woocommerce'); ?></h3>

                                <?php do_action('woocommerce_checkout_before_order_review'); ?>

                                <div id="payment" class="woocommerce-checkout-payment">
                                    <?php if (WC()->cart && WC()->cart->needs_payment()) : ?>
                                        <div class="payment-methods">
                                            <?php
                                            // Get available payment gateways
                                            $available_gateways = WC()->payment_gateways()->get_available_payment_gateways();

                                            if (!empty($available_gateways)) {
                                                // Set current gateway
                                                if (method_exists(WC()->payment_gateways(), 'set_current_gateway')) {
                                                    WC()->payment_gateways()->set_current_gateway($available_gateways);
                                                }

                                                foreach ($available_gateways as $gateway) {
                                                    wc_get_template('checkout/payment-method.php', array('gateway' => $gateway));
                                                }
                                            } else {
                                                echo '<div class="alert alert-warning">' . esc_html__('Sorry, it seems that there are no available payment methods. Please contact us if you require assistance or wish to make alternate arrangements.', 'woocommerce') . '</div>';
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <?php do_action('woocommerce_checkout_after_order_review'); ?>
                            </div>

                            <div class="checkout-actions">
                                <button type="button" class="btn btn-secondary btn-prev"><?php esc_html_e('Back to Shipping', 'woocommerce'); ?></button>
                                <button type="button" class="btn btn-primary btn-next"><?php esc_html_e('Review Order', 'woocommerce'); ?></button>
                            </div>
                        </div>

                        <!-- Step 3: Order Review -->
                        <div class="checkout-step" data-step="2" style="display: none;">
                            <div class="checkout-form">
                                <h3><?php esc_html_e('Review Your Order', 'woocommerce'); ?></h3>

                                <div id="order_review" class="woocommerce-checkout-review-order">
                                    <?php do_action('woocommerce_checkout_order_review'); ?>
                                </div>

                                <?php if (wc_get_page_id('terms') > 0) : ?>
                                    <div class="form-group mt-4">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="terms" id="terms" required>
                                            <label class="form-check-label" for="terms">
                                                <?php
                                                printf(
                                                    __('I have read and agree to the website %1$s', 'woocommerce'),
                                                    sprintf(
                                                        '<a href="%s" target="_blank">%s</a>',
                                                        esc_url(get_permalink(wc_get_page_id('terms'))),
                                                        __('terms and conditions', 'woocommerce')
                                                    )
                                                );
                                                ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="checkout-actions">
                                <button type="button" class="btn btn-secondary btn-prev"><?php esc_html_e('Back to Payment', 'woocommerce'); ?></button>
                                <button type="submit" class="btn btn-primary" id="place_order" name="woocommerce_checkout_place_order"><?php esc_html_e('Place Order', 'woocommerce'); ?></button>
                            </div>
                        </div>

                    <?php endif; ?>
                </form>
            </div>

            <div class="col-lg-4">
                <div class="order-summary">
                    <h3><?php esc_html_e('Order Summary', 'woocommerce'); ?></h3>

                    <div class="order-summary-content">
                        <?php do_action('woocommerce_checkout_before_order_review'); ?>
                        <div id="order_review_summary" class="woocommerce-checkout-review-order">
                            <?php
                            // Make sure we have a valid cart before calling woocommerce_order_review
                            if (WC()->cart && !WC()->cart->is_empty()) {
                                woocommerce_order_review();
                            } else {
                                echo '<div class="alert alert-warning">' . esc_html__('Your cart is empty. Please add some products before proceeding to checkout.', 'woocommerce') . '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php do_action('woocommerce_after_checkout_form', $checkout); ?>
