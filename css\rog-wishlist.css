/* Modern Wishlist Styles */

.custom-wishlist-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Roboto', sans-serif;
  color: #333;
}

.custom-wishlist-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 25px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.custom-wishlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.custom-wishlist-header-checkbox {
  display: flex;
  align-items: center;
}

.custom-wishlist-header-checkbox input[type="checkbox"] {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #ea9c00;
}

.custom-wishlist-header-checkbox label {
  font-weight: 500;
  cursor: pointer;
  color: #495057;
  font-size: 14px;
}

.custom-delete-selected {
  background-color: transparent;
  color: #dc3545;
  border: 1px solid #dc3545;
  padding: 8px 15px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.custom-delete-selected:hover {
  background-color: #dc3545;
  color: #fff;
}

.custom-delete-selected i {
  margin-right: 8px;
}

/* Wishlist Grid Layout - Vertical List */
.custom-wishlist-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

/* Wishlist Item Row */
.wishlist-item-row {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.wishlist-item-row:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #ddd;
}

/* Item Checkbox */
.item-checkbox {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 2;
}

.item-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #ea9c00;
}

/* Item Content */
.item-content {
  display: flex;
  gap: 20px;
  margin-left: 40px;
  /* Space for checkbox */
  align-items: flex-start;
}

/* Product Image */
.product-image {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e9ecef;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Product Details */
.product-details {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  /* gap: 8px; */
  min-width: 0;
  /* Allow text truncation */
}

/* Product Title */
.product-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
}

.product-title a {
  color: #333;
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-title a:hover {
  color: #ea9c00;
}

/* Store Info */
.store-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.store-info i {
  color: #6c757d;
  font-size: 12px;
}

.store-name {
  color: #0073aa;
  text-decoration: none;
  font-weight: 500;
}

.store-name:hover {
  text-decoration: underline;
}

/* Product Rating */
.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.product-rating .star-rating {
  display: inline-block;
  position: relative;
  font-size: 14px;
  line-height: 1;
}

.product-rating .rating-text {
  font-weight: 600;
  color: #333;
}

.product-rating .review-count {
  color: #6c757d;
}

.no-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stars-empty {
  color: #ddd;
  font-size: 14px;
}

.gray {
  color: #6c757d !important;
}

/* Product Categories */
.product-categories {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-tag {
  background: #f8f9fa;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid #e9ecef;
}

.category-tag:hover {
  background: #e9ecef;
  color: #495057;
}

/* Product Price */
.product-price {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-price .woocommerce-Price-amount {
  font-size: 18px;
  font-weight: 700;
  color: #333;
}

.product-price .woocommerce-Price-currencySymbol {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.tax-info {
  font-size: 12px;
  color: #6c757d;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}

.btn-add-to-cart,
.btn-delete-item {
  /* flex: 1; */
  padding: 8px 12px;
  /* border-radius: 6px; */
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  text-decoration: none;
  border: none;
}

.btn-add-to-cart {
  background: transparent;
  color: #ea9c00;
  /* border: 1px solid #ea9c00; */
}

.btn-add-to-cart:hover {
  background: #ea9c00;
  color: white;
  transform: translateY(-1px);
}

.btn-delete-item {
  background: transparent;
  color: #dc3545;
  /* border: 1px solid #dc3545; */
}

.btn-delete-item:hover {
  background: #dc3545;
  color: white;
  transform: translateY(-1px);
}

.btn-add-to-cart i,
.btn-delete-item i {
  font-size: 12px;
}

/* Product Price Section */
.product-price-section {
  flex-shrink: 0;
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 4px;
  min-width: 120px;
}

.price-amount {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
}

.price-amount .woocommerce-Price-amount {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

.price-amount .woocommerce-Price-currencySymbol {
  font-size: 16px;
  font-weight: 600;
}

.tax-info {
  font-size: 12px;
  color: #6c757d;
  white-space: nowrap;
}

/* Update rating display */
.rating-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stars {
  color: #ffc107;
  font-size: 14px;
  letter-spacing: 1px;
}

/* Empty stars for no reviews */
.stars-empty {
  color: #ddd;
  font-size: 14px;
  letter-spacing: 1px;
}

.no-reviews-text {
  color: #6c757d;
  font-size: 14px;
  font-style: italic;
}

.rating-display.no-reviews {
  display: flex;
  align-items: center;
  gap: 8px;
}


.custom-wishlist-empty {
  background-color: #f9f9f9;
  padding: 30px;
  text-align: center;
  border-radius: 4px;
  margin-bottom: 30px;
}

.custom-wishlist-empty p {
  font-size: 16px;
  color: #555;
  margin-bottom: 20px;
}

.custom-wishlist-empty .button {
  background-color: #f90;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  text-decoration: none;
  display: inline-block;
}

.custom-wishlist-empty .button:hover {
  background-color: #e58300;
}

/* Responsive styles for new row layout */
@media (max-width: 992px) {
  .product-image {
    width: 100px;
    height: 100px;
  }

  .item-content {
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .custom-wishlist-container {
    padding: 15px;
  }

  .custom-wishlist-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .custom-wishlist-header-checkbox {
    margin-bottom: 0;
  }

  .custom-wishlist-grid {
    gap: 12px;
  }

  .wishlist-item-row {
    padding: 15px;
  }

  .item-content {
    flex-direction: column;
    gap: 15px;
    margin-left: 35px;
  }

  .product-image {
    width: 100%;
    height: 150px;
    align-self: center;
    max-width: 200px;
  }

  .product-price-section {
    align-items: flex-start;
    text-align: left;
    min-width: auto;
  }

  .product-actions {
    flex-direction: column;
    gap: 8px;
  }

  .btn-add-to-cart,
  .btn-delete-item {
    flex: none;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .custom-wishlist-container {
    padding: 10px;
  }

  .custom-wishlist-header {
    padding: 12px;
  }

  .wishlist-item-row {
    padding: 12px;
  }

  .item-content {
    margin-left: 30px;
  }

  .product-image {
    height: 120px;
  }
}