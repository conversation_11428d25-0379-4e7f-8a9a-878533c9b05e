jQuery(document).ready(function ($) {
  $('#store-selector').on('change', function () {
      var storeId = $(this).val();
      if (storeId) {
          $.ajax({
              type: 'POST',
              url: ajaxurl,
              data: {
                  action: 'get_store_products',
                  store_id: storeId,
              },
              beforeSend: function () {
                  $('#store-products').html('<p>Loading products...</p>');
              },
              success: function (response) {
                  $('#store-products').html(response);
              },
          });
      } else {
          $('#store-products').html('');
      }
  });
});
