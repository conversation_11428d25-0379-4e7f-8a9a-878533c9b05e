<div class="col-lg-7 mx-auto pb-3 mb-3 mb-md-5 text-md-center">
  <div class="masthead-followup-icon d-inline-block mb-3" style="--bg-rgb: var(--bd-violet-rgb);">
    <svg class="bi fs-1"><use xlink:href="#code"></use></svg>
  </div>
  <h2 class="display-5 mb-3 fw-semibold lh-sm">Get started any way you&nbsp;want</h2>
  <p class="lead fw-normal">
    Jump right into building with Bootstrap—use the CDN, install it via package manager, or download the source code.
  </p>
  <p class="d-flex justify-content-md-start justify-content-md-center lead fw-normal">
    <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/download/" class="icon-link icon-link-hover fw-semibold ps-md-4">
      Read installation docs
      <svg class="bi"><use xlink:href="#arrow-right"></use></svg>
    </a>
  </p>
</div>

<section class="row g-3 g-md-5 mb-5 pb-5 justify-content-center">
  <div class="col-lg-6 py-lg-4 pe-lg-5">
    <svg class="bi mb-2 fs-2 text-body-secondary"><use xlink:href="#box-seam"></use></svg>
    <h3 class="fw-semibold">Install via package manager</h3>
    <p class="pe-lg-5">
      Install Bootstrap’s source Sass and JavaScript files via npm, RubyGems, Composer, or Meteor. Package managed installs don’t include documentation or our full build scripts. You can also <a href="https://github.com/twbs/examples/">use any demo from our Examples repo</a> to quickly jumpstart Bootstrap projects.
    </p>
    {{ highlight (printf ("npm install bootstrap@%s") .Site.Params.current_version) "sh" "" }}
    {{ highlight (printf ("gem install bootstrap -v %s") .Site.Params.current_ruby_version) "sh" "" }}
    <p>
      <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/download/">Read our installation docs</a> for more info and additional package managers.
    </p>
  </div>
  <div class="col-lg-6 py-lg-4 ps-lg-5 border-lg-start">
    <svg class="bi mb-2 fs-2 text-body-secondary"><use xlink:href="#globe2"></use></svg>
    <h3 class="fw-semibold">Include via CDN</h3>
    <p class="pe-lg-5">
      When you only need to include Bootstrap’s compiled CSS or JS, you can use <a href="https://www.jsdelivr.com/package/npm/bootstrap">jsDelivr</a>. See it in action with our simple <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/introduction/#quick-start">quick start</a>, or <a href="/docs/{{ .Site.Params.docs_version }}/examples/">browse the examples</a> to jumpstart your next project. You can also choose to include Popper and our JS <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/introduction/#separate">separately</a>.
    </p>
    {{ highlight (printf (`<link href="%s" rel="stylesheet" integrity=%q crossorigin="anonymous">`) .Site.Params.cdn.css (.Site.Params.cdn.css_hash | safeHTMLAttr)) "html" "" }}
    {{ highlight (printf (`<script src="%s" integrity=%q crossorigin="anonymous"></script>`) .Site.Params.cdn.js_bundle (.Site.Params.cdn.js_bundle_hash | safeHTMLAttr)) "html" "" }}
  </div>

  <div class="col-md-8 mx-auto text-center">
    <h4 class="fw-semibold">Read our getting started guides</h4>
    <p>Get a jump on including Bootstrap's source files in a new project with our official guides.</p>
    <div class="d-flex flex-wrap align-items-center justify-content-center gap-4 mt-4">
      <a class="d-flex flex-column align-items-center text-decoration-none animate-img" href="/docs/{{ .Site.Params.docs_version }}/getting-started/webpack/">
        <img class="d-block mb-2" src="/docs/{{ .Site.Params.docs_version }}/assets/img/webpack.svg" alt="" width="72" height="72" loading="lazy">
        <span class="text-body-secondary">Webpack</span>
      </a>
      <a class="d-flex flex-column align-items-center text-decoration-none animate-img" href="/docs/{{ .Site.Params.docs_version }}/getting-started/parcel/">
        <img class="d-block mb-2" src="/docs/{{ .Site.Params.docs_version }}/assets/img/parcel.png" alt="" width="72" height="72" loading="lazy">
        <span class="text-body-secondary">Parcel</span>
      </a>
      <a class="d-flex flex-column align-items-center text-decoration-none animate-img" href="/docs/{{ .Site.Params.docs_version }}/getting-started/vite/">
        <img class="d-block mb-2" src="/docs/{{ .Site.Params.docs_version }}/assets/img/vite.svg" alt="" width="72" height="72" loading="lazy">
        <span class="text-body-secondary">Vite</span>
      </a>
    </div>
  </div>
</section>
