/**
 * WCFM Store Fix JavaScript
 * This script removes any shop content that appears after the footer on WCFM store pages
 */

jQuery(document).ready(function($) {
    // Check if we're on a WCFM store page
    if ($('body').hasClass('wcfmmp-store-page')) {
        // Remove any content that appears after the footer
        $('.site-footer').nextAll().remove();
        
        // Also check for any WooCommerce shop elements that might be incorrectly placed
        $('.site-footer ~ .woocommerce').remove();
        $('.site-footer ~ .products').remove();
        $('.site-footer ~ .shop-container').remove();
        
        console.log('WCFM Store Fix: Removed shop content after footer');
    }
});
