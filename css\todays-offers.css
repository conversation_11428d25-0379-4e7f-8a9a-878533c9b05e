/* Offer Card Styles */

.offer-card {
  background-color: #fff;
  border-radius: 16px;
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); */
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 20px;
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #eee;
}

.offer-card:hover {
  transform: translateY(-5px);
  /* box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12); */
}

/* Countdown Timer */
.offer-countdown {
  background-color: #222;
  color: #fff;
  border-radius: 30px;
  padding: 8px 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 15px;
  align-self: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s ease;
  animation: countdown-bg-pulse 10s infinite alternate;
}

@keyframes countdown-bg-pulse {
  0% {
    background-color: #222;
  }

  50% {
    background-color: #333;
  }

  100% {
    background-color: #222;
  }
}

.countdown-item {
  display: inline-block;
  min-width: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;

  /* Animation for countdown numbers */
}

/* Animation class defined below */

/* Countdown animations */
@keyframes countdown-pulse {
  0% {
    transform: scale(1.2);
    opacity: 0.8;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes countdown-flip {
  0% {
    transform: rotateX(0deg);
  }

  50% {
    transform: rotateX(90deg);
  }

  100% {
    transform: rotateX(0deg);
  }
}

.countdown-item.animate {
  animation: countdown-flip 0.5s ease-out;
  transform-style: preserve-3d;
  perspective: 300px;
}

/* Flip animation container and elements */
.flip-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  perspective: 300px;
  z-index: 1;
}

.flip-current,
.flip-new {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flip-current {
  transform: rotateX(0deg);
  animation: flip-out 0.25s ease-out forwards;
}

.flip-new {
  transform: rotateX(-90deg);
  animation: flip-in 0.25s ease-out 0.25s forwards;
}

@keyframes flip-out {
  0% {
    transform: rotateX(0deg);
  }

  100% {
    transform: rotateX(90deg);
  }
}

@keyframes flip-in {
  0% {
    transform: rotateX(-90deg);
  }

  100% {
    transform: rotateX(0deg);
  }
}

/* Card Content */
.offer-card-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.offer-image {
  display: block;
  text-align: center;
  margin-bottom: 15px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.offer-image img {
  max-width: 100%;
  max-height: 180px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.offer-card:hover .offer-image img {
  transform: scale(1.05);
}

.offer-details {
  padding-top: 10px;
}

.offer-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  line-height: 1.3;
}

.offer-title a {
  color: #333;
  text-decoration: none;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.offer-title a:hover {
  color: #ea9c00;
}

.offer-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 10px;
  line-height: 1.4;
}

/* Rating */
.offer-rating {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.star-rating {
  display: flex;
  margin-right: 8px;
}

.star {
  color: #ddd;
  margin-right: 2px;
}

.star.filled {
  color: #ea9c00;
}

.star.half {
  color: #ea9c00;
}

.review-count {
  font-size: 16px;
  color: #666;
}

/* Price */
.offer-price {
  display: flex;
  align-items: center;
  margin-top: auto;
}

.current-price {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin-right: 10px;
}

.regular-price {
  font-size: 16px;
  color: #999;
  text-decoration: line-through;
}

/* Responsive */
@media (max-width: 768px) {
  .offer-card {
    padding: 12px;
  }

  .offer-countdown {
    font-size: 12px;
    padding: 6px 10px;
  }

  .offer-title {
    font-size: 14px;
  }

  .offer-description {
    font-size: 12px;
  }

  .current-price {
    font-size: 18px;
  }

  .regular-price {
    font-size: 14px;
  }
}

/* Today's Offers Section Styling */
.todays-offers {
  margin: 40px 0;
}

.todays-offers .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.todays-offers .section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.todays-offers .view-all {
  color: #ea9c00;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
}

.todays-offers .view-all:hover {
  text-decoration: underline;
}

.todays-offers .view-all::after {
  content: "→";
  margin-left: 5px;
}

.todays-offers .offer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

@media (max-width: 768px) {
  .todays-offers .offer-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 576px) {
  .todays-offers .offer-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}