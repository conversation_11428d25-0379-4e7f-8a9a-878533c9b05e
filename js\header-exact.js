/**
 * Header functionality - Exact match to design
 */
document.addEventListener('DOMContentLoaded', function() {
  // Category dropdown toggle
  const categoryMenuToggle = document.getElementById('category-menu-toggle');
  const categoryDropdown = document.getElementById('category-menu-dropdown');

  if (categoryMenuToggle && categoryDropdown) {
    categoryMenuToggle.addEventListener('click', function(e) {
      e.preventDefault();
      categoryDropdown.classList.toggle('active');
    });
  }

  // Close dropdown when clicking outside
  document.addEventListener('click', function(event) {
    if (categoryMenuToggle && categoryDropdown) {
      if (!categoryMenuToggle.contains(event.target) && !categoryDropdown.contains(event.target)) {
        categoryDropdown.classList.remove('active');
      }
    }
  });

  // Mobile menu toggle
  const mobileMenuBtn = document.getElementById('mobile-menu-btn');
  const menuToggle = document.querySelector('.menu-toggle');
  const primaryMenu = document.getElementById('primary-menu');

  if (menuToggle && primaryMenu) {
    menuToggle.addEventListener('click', function() {
      primaryMenu.classList.toggle('toggled');
      menuToggle.setAttribute('aria-expanded', primaryMenu.classList.contains('toggled'));
    });
  }

  // Add active class to current menu item
  const currentUrl = window.location.href;
  const menuItems = document.querySelectorAll('.main-navigation a');

  menuItems.forEach(function(item) {
    if (item.href === currentUrl) {
      item.classList.add('active');
    }
  });

  // Mobile bottom navigation - highlight active item
  const mobileNavItems = document.querySelectorAll('.mobile-nav-item a');
  
  mobileNavItems.forEach(function(item) {
    if (item.href === currentUrl) {
      item.classList.add('active');
    }
  });
});
