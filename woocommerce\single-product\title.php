<?php
/**
 * Single Product title
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/title.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see        https://woocommerce.com/document/template-structure/
 * @package    WooCommerce\Templates
 * @version    1.6.4
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

the_title( '<h3 class="product_title entry-title">', '</h3>' );

?>

<!-- <div class="action-product mb-4 mt-4 ">
  <ul class="action-product__list">
    <li>
      <i class="bi bi-heart"></i>
      <span>Add to favorite</span>

    </li>
    <li>
      <i class="bi bi-arrow-repeat"></i>
      <span>compare</span>
    </li>

    <li><i class="bi bi-share"></i><span>share</span></li>

  </ul>

</div> -->

<div class="product-actions-row">
  <div class="action-product">
    <ul class="action-product__list">
      <li class="action-item">
        <!-- <i data-feather="heart" class="feather-sm"></i>
        <span>Add to favorite</span> -->

        <?php
   if (function_exists('YITH_WCWL')) {
		echo do_shortcode('[yith_wcwl_add_to_wishlist]');
	}
  ?>
      </li>
      <li class="action-item">
        <?php
        // Add compare button with proper functionality
        global $product;
        $product_id = $product ? $product->get_id() : get_the_ID();

        // Always show compare button with our enhanced functionality
        echo '<a href="#" class="yith-compare-btn compare-btn compare-button icon-with-text" data-product-id="' . esc_attr($product_id) . '" title="Compare">';
        echo '<i data-feather="repeat" class="feather-sm"></i>';
        echo '<span>Compare</span>';
        echo '</a>';
        ?>
      </li>
      <li class="action-item">
        <!-- <i data-feather="share-2" class="feather-sm"></i>
        <span>Share</span> -->

        <?php
        // Get current product data
        global $product;
        $product_url = get_permalink();
        $product_title = get_the_title();
        $product_image = wp_get_attachment_image_url(get_post_thumbnail_id(), 'full');

        echo '<div class="share-button icon-with-text" data-product-url="' . esc_url($product_url) . '" data-product-title="' . esc_attr($product_title) . '" data-product-image="' . esc_url($product_image) . '">';
	echo '<i data-feather="share-2" class="feather-sm"></i><span>Share</span>';
	echo '</div>';

	// Add share modal/dropdown (hidden by default)
	echo '<div class="share-modal" id="share-modal" style="display: none;">';
	echo '<div class="share-modal-content">';
	echo '<div class="share-modal-header">';
	echo '<h4>Share this product</h4>';
	echo '<button class="share-modal-close" type="button">&times;</button>';
	echo '</div>';
	echo '<div class="share-options">';

	// Social sharing options
	echo '<a href="#" class="share-option facebook" data-platform="facebook">';
	echo '<i data-feather="facebook" class="feather-sm"></i>';
	echo '<span>Facebook</span>';
	echo '</a>';

	echo '<a href="#" class="share-option twitter" data-platform="twitter">';
	echo '<i data-feather="twitter" class="feather-sm"></i>';
	echo '<span>Twitter</span>';
	echo '</a>';

	echo '<a href="#" class="share-option whatsapp" data-platform="whatsapp">';
	echo '<i data-feather="message-circle" class="feather-sm"></i>';
	echo '<span>WhatsApp</span>';
	echo '</a>';

	echo '<a href="#" class="share-option email" data-platform="email">';
	echo '<i data-feather="mail" class="feather-sm"></i>';
	echo '<span>Email</span>';
	echo '</a>';

	echo '<a href="#" class="share-option copy-link" data-platform="copy">';
	echo '<i data-feather="link" class="feather-sm"></i>';
	echo '<span>Copy Link</span>';
	echo '</a>';

	echo '</div>';
	echo '</div>';
	echo '</div>';

        ?>
      </li>
    </ul>
  </div>
</div>