<?php
/**
 * Cart Page
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/cart.php.
 *
 * @package WooCommerce\Templates
 * @version 7.9.0
 */

defined( 'ABSPATH' ) || exit;

do_action( 'woocommerce_before_cart' ); ?>

<div class="cart-container">
  <form class="woocommerce-cart-form" action="<?php echo esc_url( wc_get_cart_url() ); ?>" method="post">
    <div class="cart-header row d-flex">
      <div class="cart-heder-checkbox">
        <input type="checkbox" id="select-all"> Select All
      </div>
      <div class="cart-header-delbtn"><button type="button" id="delete-selected" class="button">🗑 Delete selected
          items</button></div>

    </div>

    <table class="shop_table shop_table_responsive cart woocommerce-cart-form__contents" cellspacing="0">
      <thead>
        <tr>
          <th></th>
          <th>Items</th>
          <th>Subtotal</th>
        </tr>
      </thead>
      <tbody>
        <?php do_action( 'woocommerce_before_cart_contents' ); ?>

        <?php
                foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
                    $_product   = $cart_item['data'];
                    $product_id = $cart_item['product_id'];
                    $product_permalink = $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '';
                    ?>
        <tr class="cart_item">
          <td class="product-select">
            <input type="checkbox" class="cart-item-checkbox"
              data-cart-item-key="<?php echo esc_attr( $cart_item_key ); ?>">
          </td>
          <td class="product-info">
            <div class="product-thumbnail">
              <a href="<?php echo esc_url( $product_permalink ); ?>">
                <?php echo $_product->get_image(); ?>
              </a>
            </div>
            <div class="product-details">
              <a href="<?php echo esc_url( $product_permalink ); ?>"
                class="product-name"><?php echo $_product->get_name(); ?></a>
              <div class="product-reviews">⭐⭐⭐⭐⭐ 4.8 (397 Reviews)</div>
              <div class="quantity-control">
                <button type="button" class="decrease-qty">-</button>
                <input type="number" name="cart[<?php echo $cart_item_key; ?>][qty]"
                  value="<?php echo $cart_item['quantity']; ?>" min="1" class="qty-input">
                <button type="button" class="increase-qty">+</button>
              </div>
              <a href="<?php echo esc_url( wc_get_cart_remove_url( $cart_item_key ) ); ?>" class="remove">🗑 Delete
                Item</a>
            </div>
          </td>
          <td class="product-subtotal">
            <?php echo WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ); ?>
          </td>
        </tr>
        <?php } ?>
        <?php do_action( 'woocommerce_cart_contents' ); ?>
      </tbody>
    </table>
    <?php do_action( 'woocommerce_after_cart_table' ); ?>
  </form>

  <aside class="cart-sidebar">
    <div class="cart-coupon">
      <input type="text" placeholder="Coupon code">
      <button class="button">Apply coupon</button>
    </div>
    <div class="cart-summary">
      <p>Total: <strong>249.00 QAR</strong></p>
      <p>Items: <strong>249.00 QAR</strong></p>
      <p>Discount: <strong>-120.00 QAR</strong></p>
      <button class="button complete-payment">Complete payment</button>
    </div>
    <div class="payment-methods">
      <p>We support Visa, Mastercard, and bank transfer.</p>
      <img src="path/to/payment-icons.png" alt="Payment Methods">
    </div>
  </aside>
</div>

<script>
document.getElementById('select-all').addEventListener('change', function() {
  let checkboxes = document.querySelectorAll('.cart-item-checkbox');
  checkboxes.forEach(checkbox => checkbox.checked = this.checked);
});

document.querySelectorAll('.decrease-qty').forEach(button => {
  button.addEventListener('click', function() {
    let input = this.nextElementSibling;
    if (input.value > 1) input.value--;
  });
});

document.querySelectorAll('.increase-qty').forEach(button => {
  button.addEventListener('click', function() {
    let input = this.previousElementSibling;
    input.value++;
  });
});
</script>

<style>
.cart-container {
  display: flex;
  gap: 20px;
}

.woocommerce-cart-form {
  width: 70%;
}

.cart-sidebar {
  width: 30%;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 10px;
}

.shop_table {
  width: 100%;
  border-collapse: collapse;
}

.shop_table th,
.shop_table td {
  padding: 10px;
  text-align: left;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-control button {
  width: 30px;
  height: 30px;
  border: none;
  background: #ddd;
  cursor: pointer;
}

.qty-input {
  width: 50px;
  text-align: center;
}
</style>