languageCode:           "en"
title:                  "Bootstrap"
baseURL:                "https://getbootstrap.com"

security:
  enableInlineShortcodes: true
  funcs:
    getenv:
      - ^HUGO_
      - NETLIFY
  gotemplates:
    allowActionJSTmpl: true

markup:
  goldmark:
    renderer:
      unsafe:           true
  highlight:
    noClasses:          false
  tableOfContents:
    startLevel:         2
    endLevel:           6

buildDrafts:            true
buildFuture:            true

enableRobotsTXT:        true
metaDataFormat:         "yaml"
disableKinds:           ["404", "taxonomy", "term", "RSS"]

publishDir:             "_site"

module:
  mounts:
    - source:           dist
      target:           static/docs/5.3/dist
    - source:           site/assets
      target:           assets
    - source:           site/content
      target:           content
    - source:           site/data
      target:           data
    - source:           site/layouts
      target:           layouts
    - source:           site/static
      target:           static
    - source:           site/static/docs/5.3/assets/img/favicons/apple-touch-icon.png
      target:           static/apple-touch-icon.png
    - source:           site/static/docs/5.3/assets/img/favicons/favicon.ico
      target:           static/favicon.ico

params:
  subtitle:             "The most popular HTML, CSS, and JS library in the world."
  description:          "Powerful, extensible, and feature-packed frontend toolkit. Build and customize with Sass, utilize prebuilt grid system and components, and bring projects to life with powerful JavaScript plugins."
  authors:              "Mark Otto, Jacob Thornton, and Bootstrap contributors"

  current_version:      "5.3.3"
  current_ruby_version: "5.3.3"
  docs_version:         "5.3"
  rfs_version:          "v10.0.0"
  github_org:           "https://github.com/twbs"
  repo:                 "https://github.com/twbs/bootstrap"
  twitter:              "getbootstrap"
  opencollective:       "https://opencollective.com/bootstrap"
  blog:                 "https://blog.getbootstrap.com/"
  themes:               "https://themes.getbootstrap.com/"
  icons:                "https://icons.getbootstrap.com/"
  swag:                 "https://cottonbureau.com/people/bootstrap"

  download:
    source:             "https://github.com/twbs/bootstrap/archive/v5.3.3.zip"
    dist:               "https://github.com/twbs/bootstrap/releases/download/v5.3.3/bootstrap-5.3.3-dist.zip"
    dist_examples:      "https://github.com/twbs/bootstrap/releases/download/v5.3.3/bootstrap-5.3.3-examples.zip"

  cdn:
    # See https://www.srihash.org for info on how to generate the hashes
    css:              "https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
    css_hash:         "sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH"
    css_rtl:          "https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css"
    css_rtl_hash:     "sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb"
    js:               "https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
    js_hash:          "sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy"
    js_bundle:        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
    js_bundle_hash:   "sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
    popper:           "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
    popper_hash:      "sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
    popper_esm:       "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/esm/popper.min.js"

  anchors:
    min: 2
    max: 5
