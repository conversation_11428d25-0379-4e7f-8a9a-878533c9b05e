<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Responsive Category Dropdown</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
  <script src="https://unpkg.com/@tailwindcss/browser@latest"></script>
  <style>
  <style>

  /* Custom CSS for the Category Dropdown */
  .category-menu-wrapper {
    position: relative;
    /* Allows for absolute positioning of the dropdown */
  }

  .category-menu-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* Space between text and icon */
    padding: 0.75rem 1rem;
    /* Tailwind's px-4 py-2 */
    background-color: #3b82f6;
    /* <PERSON><PERSON><PERSON>'s bg-blue-500 */
    color: #fff;
    /* <PERSON><PERSON><PERSON>'s text-white */
    font-size: 1rem;
    /* <PERSON><PERSON>wind's text-base */
    font-weight: 600;
    /* <PERSON>lwind's font-semibold */
    border-radius: 0.375rem;
    /* <PERSON>lwind's rounded-md */
    cursor: pointer;
    transition: background-color 0.3s ease;
    /* Smooth transition */
    width: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .category-menu-button:hover {
    background-color: #2563eb;
    /* Tailwind's bg-blue-700 on hover */
  }

  .category-menu-button i {
    margin-left: 0.5rem;
    /* Tailwind's ml-2 */
    font-size: 1.25rem;
  }

  .category-dropdown {
    width: 960px !important;
    position: absolute;
    /* top: 100%; */
    /* left: 0; */
    background-color: #fff;
    border: 1px solid #e5e7eb;
    /* Tailwind's border-gray-200 */
    border-radius: 0.375rem;
    /* Tailwind's rounded-md */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    /* Tailwind's shadow-md */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    z-index: 10;
    width: 100%;
    max-height: 500px;
    overflow-y: auto;
  }

  .category-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }


  .category-dropdown-content {
    width: 960px;
    display: flex;
    flex-wrap: wrap;
    padding: 1rem;
    gap: 1rem;
  }

  .category-column {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .category-column strong {
    margin-bottom: 0.5rem;
    color: #1f2937;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .category-column a {
    margin-bottom: 0.5rem;
    color: #1f2937;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .category-link {
    display: block;
    padding: 0.5rem;
    color: #4b5563;
    /* Tailwind's text-gray-600 */
    font-size: 0.875rem;
    /* Tailwind's text-sm */
    text-decoration: none;
    transition: color 0.2s ease, background-color 0.2s ease;
    border-radius: 0.25rem;
  }

  .category-link:hover {
    color: #fff;
    background-color: #3b82f6;
  }

  @media (max-width: 768px) {
    .category-dropdown-content {
      flex-direction: column;
    }

    .category-column {
      min-width: 100%;
    }
  }
  </style>
  </style>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
</head>

<body class="bg-gray-100 font-sans">
  <div class="container mx-auto py-8">
    <div class="category-menu-wrapper">
      <button class="category-menu-button" id="category-menu-toggle">
        <span>Categories</span>
        <i class="bi bi-list"></i>
      </button>
      <div class="category-dropdown" id="category-menu-dropdown">
        <div class="category-dropdown-content">
          <div class="category-column">
            <strong>Women's Fashion</strong>
            <a href="#" class="category-link">Clothes</a>
            <a href="#" class="category-link">Dresses</a>
            <a href="#" class="category-link">Blouses & Shirts</a>
            <a href="#" class="category-link">Hooded Jackets</a>

          </div>
          <div class="category-column">
            <strong>Men's Fashion</strong>
            <a href="#" class="category-link">Clothes</a>
            <a href="#" class="category-link">Underwear</a>
            <a href="#" class="category-link">Weddings & Events</a>
            <a href="#" class="category-link">Accessories</a>
          </div>
          <div class="category-column">
            <strong>Phones and Communications</strong>
            <a href="#" class="category-link">Dresses</a>
            <a href="#" class="category-link">Underwear</a>
            <a href="#" class="category-link">Weeding dresses</a>
            <a href="#" class="category-link">Hair accessories</a>
          </div>
          <div class="category-column">
            <strong>Computer, Office and Security</strong>
            <a href="#" class="category-link">Bras</a>
            <a href="#" class="category-link">Bras</a>
            <a href="#" class="category-link">Graduation party dresses</a>
            <a href="#" class="category-link">Sunglasses</a>
          </div>

        </div>
      </div>
    </div>
  </div>

  <script>
  const categoryMenuToggle = document.getElementById('category-menu-toggle');
  const categoryDropdown = document.getElementById('category-menu-dropdown');

  categoryMenuToggle.addEventListener('click', () => {
    categoryDropdown.classList.toggle('active');
  });

  document.addEventListener('click', (event) => {
    if (!categoryDropdown.contains(event.target) && !categoryMenuToggle.contains(event.target)) {
      categoryDropdown.classList.remove('active');
    }
  });
  </script>
</body>

</html>


<?php
// Fetch WooCommerce categories dynamically
$categories = get_terms(array(
    'taxonomy'   => 'product_cat',
    'hide_empty' => true,  // Hide categories with no products
));

if (!empty($categories)) : ?>
<div class="category-menu">
  <button class="category-btn">Categories <i class="bi bi-list"></i></button>
  <div class="dropdown-content">
    <ul class="category-list">
      <?php foreach ($categories as $category) : ?>
      <li class="category-item">
        <a href="<?php echo get_term_link($category); ?>">
          <?php echo esc_html($category->name); ?>
          <i class="bi bi-chevron-right"></i>
        </a>

        <!-- Fetch subcategories dynamically -->
        <?php
                        $subcategories = get_terms(array(
                            'taxonomy'   => 'product_cat',
                            'hide_empty' => true,
                            'parent'     => $category->term_id, // Get only child categories
                        ));
                        if (!empty($subcategories)) : ?>
        <ul class="subcategory-list">
          <?php foreach ($subcategories as $subcategory) : ?>
          <li>
            <a href="<?php echo get_term_link($subcategory); ?>">
              <?php echo esc_html($subcategory->name); ?>
            </a>
          </li>
          <?php endforeach; ?>
        </ul>
        <?php endif; ?>
      </li>
      <?php endforeach; ?>
    </ul>
  </div>
</div>
<?php endif; ?>