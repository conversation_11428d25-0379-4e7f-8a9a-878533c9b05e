/**
 * Product Configuration JavaScript
 */
jQuery(document).ready(function($) {
    // Initialize variables
    let selectedAttributes = {};
    let variationsData = [];
  
    // Get variation data if available
    const $variationsData = $('#product-variations-data');
    if ($variationsData.length) {
        try {
            variationsData = JSON.parse($variationsData.attr('data-product_variations'));
            console.log('Variations data loaded:', variationsData);
        } catch (error) {
            console.error('Error parsing variations data:', error);
        }
    }
  
    // Configuration options selection
    $(document).on('click', '.config-option:not(.disabled)', function() {
        const group = $(this).data('group');
        const value = $(this).data('value');
        const isVariation = $(this).data('variation') === 1;
  
        console.log(`Selected ${group}: ${value} (Variation: ${isVariation})`);
  
        // Remove selected class from all options in this group
        $(`.config-option[data-group="${group}"]`).removeClass('selected');
  
        // Add selected class to this option
        $(this).addClass('selected');
  
        // Update selectedAttributes
        selectedAttributes[group] = value;
  
        // Update hidden form field
        updateHiddenField(group, value);
  
        // If this is a variable product, update available options and price
        if (variationsData.length > 0 && isVariation) {
            updateAvailableOptions();
            updateSelectedVariation();
        }
  
        // Trigger a custom event for other scripts
        $(document).trigger('product_attribute_changed', [group, value, isVariation]);
    });
  
    // Helper function to update hidden form field
    function updateHiddenField(attributeName, value) {
        const fieldName = `attribute_${attributeName}`;
        let field = $(`.hidden-form input[name="${fieldName}"]`);
  
        if (field.length) {
            field.val(value);
        } else {
            $('.hidden-form').append(`<input type="hidden" name="${fieldName}" value="${value}">`);
        }
    }
  
    // Helper function to update available options based on current selection
    function updateAvailableOptions() {
        // Only process variation attributes
        $('.product-config.variation-attribute').each(function() {
            const $configOptions = $(this).find('.config-options');
            const attributeName = $configOptions.data('attribute');
  
            // Skip the current attribute being changed
            if (event && event.target && $(event.target).closest('.config-options').data('attribute') === attributeName) {
                return;
            }
  
            // For each option in this group
            $configOptions.find('.config-option').each(function() {
                const optionValue = $(this).data('value');
  
                // Check if this option is available with the current selection
                const tempAttributes = {...selectedAttributes};
                tempAttributes[attributeName] = optionValue;
  
                let isAvailable = false;
  
                // Check if this combination exists in any variation
                for (const variation of variationsData) {
                    let isMatch = true;
  
                    for (const name in tempAttributes) {
                        const key = `attribute_${name}`;
  
                        // Skip attributes with empty value in the variation (any value is allowed)
                        if (!variation.attributes[key] || variation.attributes[key] === '') continue;
  
                        // If this attribute doesn't match, this variation doesn't match
                        if (tempAttributes[name] !== variation.attributes[key]) {
                            isMatch = false;
                            break;
                        }
                    }
  
                    if (isMatch) {
                        isAvailable = true;
                        break;
                    }
                }
  
                // Update option state
                if (isAvailable) {
                    $(this).removeClass('disabled');
                } else {
                    $(this).addClass('disabled');
  
                    // If this option is selected but not available, select the first available option
                    if ($(this).hasClass('selected')) {
                        const firstAvailable = $configOptions.find('.config-option:not(.disabled)').first();
                        if (firstAvailable.length) {
                            firstAvailable.trigger('click');
                        } else {
                            // If no available options, clear the selection
                            $(this).removeClass('selected');
                            delete selectedAttributes[attributeName];
  
                            // Clear the hidden form field
                            const fieldName = `attribute_${attributeName}`;
                            $(`.hidden-form input[name="${fieldName}"]`).val('');
                        }
                    }
                }
            });
  
            // If no option is selected in this group, select the first available one
            if (!$configOptions.find('.config-option.selected').length) {
                const firstAvailable = $configOptions.find('.config-option:not(.disabled)').first();
                if (firstAvailable.length) {
                    firstAvailable.addClass('selected');
                    selectedAttributes[attributeName] = firstAvailable.data('value');
                    updateHiddenField(attributeName, firstAvailable.data('value'));
                }
            }
        });
    }
  
    // Helper function to update price and variation ID based on selected attributes
    function updateSelectedVariation() {
        // Check if we have all required variation attributes selected
        const requiredAttributes = $('.product-config.variation-attribute').length;
        const selectedCount = Object.keys(selectedAttributes).filter(attr => {
            return $(`.product-config.variation-attribute .config-options[data-attribute="${attr}"]`).length > 0;
        }).length;
  
        // If not all required attributes are selected, show a message
        if (selectedCount < requiredAttributes) {
            console.log(`Not all attributes selected: ${selectedCount}/${requiredAttributes}`);
            $('.variation_id').val('');
            $('.stock-info').html('Please select all options');
            $('.btn-add-cart, .btn-buy-now').prop('disabled', true);
            return;
        }
  
        // Convert selectedAttributes to the format used in variations
        const attributes = {};
        for (const name in selectedAttributes) {
            attributes[`attribute_${name}`] = selectedAttributes[name];
        }
  
        // Find matching variation
        let matchedVariation = null;
  
        for (const variation of variationsData) {
            let isMatch = true;
  
            // Check if all variation attributes match
            for (const key in variation.attributes) {
                // Skip attributes with empty value (any value is allowed)
                if (variation.attributes[key] === '') continue;
  
                // If this attribute doesn't match, this variation doesn't match
                if (!attributes[key] || attributes[key] !== variation.attributes[key]) {
                    isMatch = false;
                    break;
                }
            }
  
            // Check if all selected attributes are present in the variation
            for (const key in attributes) {
                if (variation.attributes[key] !== undefined &&
                    variation.attributes[key] !== '' &&
                    variation.attributes[key] !== attributes[key]) {
                    isMatch = false;
                    break;
                }
            }
  
            if (isMatch) {
                matchedVariation = variation;
                break;
            }
        }
  
        if (matchedVariation) {
            console.log('Matching variation found:', matchedVariation);
  
            // Update variation ID
            $('.variation_id').val(matchedVariation.variation_id);
  
            // Update price
            if (matchedVariation.display_price !== undefined) {
                updatePrice(matchedVariation.display_price, matchedVariation.display_regular_price);
            }
  
            // Update stock status
            updateStockStatus(matchedVariation);
  
            // Update image if available
            if (matchedVariation.image && matchedVariation.image.src) {
                updateProductImage(matchedVariation.image);
            }
  
            // Update SKU if available
            if (matchedVariation.sku) {
                updateSku(matchedVariation.sku);
            }
  
            // Update weight if available
            if (matchedVariation.weight_html) {
                updateWeight(matchedVariation.weight_html);
            }
  
            // Update dimensions if available
            if (matchedVariation.dimensions_html) {
                updateDimensions(matchedVariation.dimensions_html);
            }
  
            // Enable add to cart and buy now buttons
            $('.btn-add-cart, .btn-buy-now').prop('disabled', false);
  
            // Trigger event for other scripts
            $(document).trigger('variation_found', [matchedVariation]);
        } else {
            console.log('No matching variation found');
  
            // Clear variation ID
            $('.variation_id').val('');
  
            // Update stock status to indicate invalid selection
            $('.stock-info').html('This combination is not available');
  
            // Disable add to cart and buy now buttons
            $('.btn-add-cart, .btn-buy-now').prop('disabled', true);
  
            // Trigger event for other scripts
            $(document).trigger('no_variation_found');
        }
    }
  
    // Helper function to update price display
    function updatePrice(price, regularPrice) {
        const priceContainer = $('.price');
        priceContainer.empty();
  
        // Format price with currency
        const formattedPrice = formatPrice(price);
        priceContainer.append(`<span class="woocommerce-Price-amount amount">${formattedPrice}</span>`);
  
        // Add regular price and discount if available
        if (regularPrice && regularPrice > price) {
            const formattedRegularPrice = formatPrice(regularPrice);
            const discount = Math.round(100 - ((price / regularPrice) * 100));
  
            priceContainer.append(` <del>${formattedRegularPrice}</del> <span class="discount-badge">${discount}%</span>`);
        }
    }
  
    // Helper function to update stock status
    function updateStockStatus(variation) {
        if (variation.is_in_stock) {
            const stockQty = variation.max_qty || '';
            $('.stock-info').html(stockQty ? `${stockQty} pieces available` : 'In stock');
        } else {
            $('.stock-info').html('Out of stock');
            $('.btn-add-cart, .btn-buy-now').prop('disabled', true);
        }
    }
  
    // Helper function to update product image
    function updateProductImage(image) {
        const mainImage = $('.woocommerce-product-gallery__image:first-child img');
        if (mainImage.length) {
            mainImage.attr('src', image.src);
            if (image.srcset) {
                mainImage.attr('srcset', image.srcset);
            }
        }
    }
  
    // Helper function to format price with currency
    function formatPrice(price) {
        // Use WooCommerce settings if available
        if (typeof woocommerce_params !== 'undefined') {
            const symbol = woocommerce_params.currency_symbol || '$';
            const format = woocommerce_params.currency_format || '%s%v';
  
            // Format the price with the appropriate number of decimals
            const decimals = woocommerce_params.currency_decimals || 2;
            const formattedValue = parseFloat(price).toFixed(decimals);
  
            // Replace placeholders in the format string
            return format.replace('%s', symbol).replace('%v', formattedValue);
        }
  
        // Fallback to simple formatting
        return '$' + parseFloat(price).toFixed(2);
    }
  
    // Helper function to update SKU
    function updateSku(sku) {
        const $skuContainer = $('.product-sku');
        if ($skuContainer.length) {
            $skuContainer.html(sku);
        } else {
            // If SKU container doesn't exist, create it
            const $skuElement = $('<div class="product-meta product-sku"></div>');
            $skuElement.html(`<span class="meta-label">SKU:</span> <span class="meta-value">${sku}</span>`);
  
            // Add it after the product summary
            $('.product-summary').after($skuElement);
        }
    }
  
    // Helper function to update weight
    function updateWeight(weightHtml) {
        const $weightContainer = $('.product-weight');
        if ($weightContainer.length) {
            $weightContainer.html(weightHtml);
        } else {
            // If weight container doesn't exist, create it
            const $weightElement = $('<div class="product-meta product-weight"></div>');
            $weightElement.html(`<span class="meta-label">Weight:</span> <span class="meta-value">${weightHtml}</span>`);
  
            // Add it after the SKU or product summary
            if ($('.product-sku').length) {
                $('.product-sku').after($weightElement);
            } else {
                $('.product-summary').after($weightElement);
            }
        }
    }
  
    // Helper function to update dimensions
    function updateDimensions(dimensionsHtml) {
        const $dimensionsContainer = $('.product-dimensions');
        if ($dimensionsContainer.length) {
            $dimensionsContainer.html(dimensionsHtml);
        } else {
            // If dimensions container doesn't exist, create it
            const $dimensionsElement = $('<div class="product-meta product-dimensions"></div>');
            $dimensionsElement.html(`<span class="meta-label">Dimensions:</span> <span class="meta-value">${dimensionsHtml}</span>`);
  
            // Add it after the weight, SKU, or product summary
            if ($('.product-weight').length) {
                $('.product-weight').after($dimensionsElement);
            } else if ($('.product-sku').length) {
                $('.product-sku').after($dimensionsElement);
            } else {
                $('.product-summary').after($dimensionsElement);
            }
        }
    }
  
    // Initialize the page
    function init() {
        // Set initial selected attributes based on the first selected option in each group
        $('.config-option.selected').each(function() {
            const group = $(this).data('group');
            const value = $(this).data('value');
            selectedAttributes[group] = value;
            updateHiddenField(group, value);
        });
  
        // For variable products, update available options on page load
        if (variationsData.length > 0) {
            updateAvailableOptions();
            updateSelectedVariation();
        }
    }
  
    // Run initialization
    init();
  });
  