<?php
/**
 * Add WooCommerce support
 *
 * @package Understrap
 */

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

add_action( 'after_setup_theme', 'tendeal_woocommerce_support' );

if ( ! function_exists( 'tendeal_woocommerce_support' ) ) {
	/**
	 * Declares WooCommerce theme support.
	 */
	function tendeal_woocommerce_support() {
		add_theme_support( 'woocommerce' );

		// Add Product Gallery support.
		// add_theme_support( 'wc-product-gallery-lightbox' );
		// add_theme_support( 'wc-product-gallery-zoom' );
		// add_theme_support( 'wc-product-gallery-slider' );

		// Add Bootstrap classes to form fields.
		// add_filter( 'woocommerce_form_field_args', 'understrap_wc_form_field_args', 10, 3 );
		// add_filter( 'woocommerce_form_field_radio', 'understrap_wc_form_field_radio', 10, 4 );
		// add_filter( 'woocommerce_quantity_input_classes', 'understrap_quantity_input_classes' );
		// add_filter( 'woocommerce_loop_add_to_cart_args', 'understrap_loop_add_to_cart_args' );

		// Wrap the add-to-cart link in `div.add-to-cart-container`.
		add_filter( 'woocommerce_loop_add_to_cart_link', 'tendeal_loop_add_to_cart_link' );

		// Add Bootstrap classes to account navigation.
		// add_filter( 'woocommerce_account_menu_item_classes', 'understrap_account_menu_item_classes' );
	}
}

if ( ! function_exists( 'tendeal_loop_add_to_cart_link' ) ) {
	/**
	 * Wrap add to cart link in container.
	 *
	 * @param string $html Add to cart link HTML.
	 * @return string Add to cart link HTML.
	 */
	function tendeal_loop_add_to_cart_link( $html ) {
		return '<div class="row d-flex mb-3 ">  <div class="add-to-cart-container  col">' . $html . ' </div>';
	}
}


// <?php  do_shortcode('[yith_wcwl_add_to_wishlist]') ?>