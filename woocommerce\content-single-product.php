<?php
/**
 * The template for displaying product content in the single-product.php template
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 */

defined( 'ABSPATH' ) || exit;

global $product;

/**
 * Hook: woocommerce_before_single_product.
 *
 * @hooked woocommerce_output_all_notices - 10
 */
do_action( 'woocommerce_before_single_product' );

if ( post_password_required() ) {
	echo get_the_password_form(); // WPCS: XSS ok.
	return;
}
?>
<div id="product-<?php the_ID(); ?>" <?php wc_product_class( '', $product ); ?>>

  <?php
	/**
	 * Hook: woocommerce_before_single_product_summary.
	 *
	 * @hooked woocommerce_show_product_sale_flash - 10
	 * @hooked woocommerce_show_product_images - 20
	 */
	do_action( 'woocommerce_before_single_product_summary' );
	?>

  <div class="summary entry-summary">
    <?php
		/**
		 * Hook: woocommerce_single_product_summary.
		 *
		 * @hooked woocommerce_template_single_title - 5
		 * @hooked woocommerce_template_single_rating - 10
		 * @hooked woocommerce_template_single_price - 10
		 * @hooked woocommerce_template_single_excerpt - 20
		 * @hooked woocommerce_template_single_add_to_cart - 30
		 * @hooked woocommerce_template_single_meta - 40
		 * @hooked woocommerce_template_single_sharing - 50
		 * @hooked WC_Structured_Data::generate_product_data() - 60
		 */
		do_action( 'woocommerce_single_product_summary' );
		?>
  </div>


  <?php if ( is_product() ) : ?>

  <div id="sidebar" class="col-md-3 col-12 sidbar-single-product">
    <div class="shipping-section">
      <div class="shipping-section__list">
        <span>Shipping to:</span>
        <span><i data-feather="map-pin" class="feather-sm"></i> Qatar</span>
      </div>

      <div class="shipping-section__list">
        <span>Shipping cost:</span>
        <span>20.00 QAR</span>
      </div>

      <div class="shipping-section__list">
        <span>Estimated date:</span>
        <span>11-30 days</span>
      </div>
    </div>

    <div class="quantity-section">
      <div class="quantity-label">
        <span>Quantity:</span>
      </div>

      <div class="quantity-controls">
        <button type="button" class="quantity-btn minus">-</button>
        <input type="number" id="product-quantity" class="quantity-input" value="1" min="1" max="999"
          inputmode="numeric" pattern="[0-9]*">
        <button type="button" class="quantity-btn plus">+</button>
      </div>

      <div class="product-availability">
        <span><?php echo $product->get_stock_quantity() ? $product->get_stock_quantity() : 'NA'; ?> pieces
          available</span>
      </div>


    </div>

    <div class="button-buy-section">
      <form class="cart"
        action="<?php echo esc_url(apply_filters('woocommerce_add_to_cart_form_action', $product->get_permalink())); ?>"
        method="post" enctype="multipart/form-data">
        <input type="hidden" id="product-id" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>">
        <input type="hidden" name="quantity" value="1" class="product-quantity-input">
        <?php wp_nonce_field('woocommerce-add-to-cart', 'woocommerce-add-to-cart-nonce'); ?>

        <button type="button" class="btn btn-warning buy-now-btn">Buy now</button>
        <button type="button" class="btn btn-outline-dark add-to-cart-btn">Add to cart</button>
        <button type="submit" class="btn btn-outline-dark add-to-cart-fallback" style="display: none;">Add to cart
          (fallback)</button>
        <button type="button" class="btn btn-outline-dark add-to-rfq-btn">Add to RFQ</button>
      </form>

      <!-- Direct add to cart form for fallback -->
      <form id="direct-add-to-cart-form" action="<?php echo esc_url(wc_get_cart_url()); ?>" method="post"
        style="display: none;">
        <input type="hidden" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>">
        <input type="hidden" name="quantity" value="1" class="direct-quantity-input">
        <?php wp_nonce_field('woocommerce-add-to-cart', 'direct-woocommerce-add-to-cart-nonce'); ?>
      </form>
    </div>

    <div class="buyer-protection">
      <div class="protection-icon">
        <i data-feather="shield" class="feather-md"></i>
      </div>
      <div class="protection-text">
        <span class="protection-title">30 Day Money Back Guarantee</span>
        <a href="#" class="protection-link">See the rules</a>
      </div>
    </div>

    <div class="store-info">
      <?php echo do_shortcode('[wcfm_store_sold_by ]') ?>
    </div>
  </div>
  <?php endif; ?>

  <?php
	/**
	 * Hook: woocommerce_after_single_product_summary.
	 *
	 * @hooked woocommerce_output_product_data_tabs - 10
	 * @hooked woocommerce_upsell_display - 15
	 * @hooked woocommerce_output_related_products - 20
	 */
	do_action( 'woocommerce_after_single_product_summary' );
	?>
</div>

<?php do_action( 'woocommerce_after_single_product' ); ?>