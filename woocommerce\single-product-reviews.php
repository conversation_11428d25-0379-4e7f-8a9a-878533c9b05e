<?php
/**
 * Display single product reviews (comments)
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product-reviews.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.7.0
 */

defined( 'ABSPATH' ) || exit;

global $product;

// Get product rating details
$average_rating = $product->get_average_rating();
$review_count = $product->get_review_count();
$total_sales = get_post_meta($product->get_id(), 'total_sales', true);
$rating_counts = $product->get_rating_counts(); // Get count of each star rating
$total_reviews = array_sum($rating_counts); // Total number of reviews

?>

<div class="reviews-section">
  <!-- Left Side: Summary & Rating -->
  <div class="review-summary">
    <h2>Clients reviews</h2>
    <div class="rating-score">
      <span class="average-score"><?php echo esc_html($average_rating); ?></span>
      <div class="star-rating"><?php echo wc_get_rating_html($average_rating); ?></div>
    </div>
    <p><?php echo esc_html($review_count); ?> Reviews</p>

    <!-- Star Percentage Breakdown -->
    <div class="rating-breakdown">
      <?php for ($i = 5; $i >= 1; $i--) :
                $percentage = ($total_reviews > 0) ? ($rating_counts[$i] ?? 0) / $total_reviews * 100 : 0;
            ?>
      <div class="rating-row">
        <span><?php echo $i; ?> ★</span>
        <div class="rating-bar">
          <div class="fill" style="width:<?php echo $percentage; ?>%"></div>
        </div>
        <span><?php echo round($percentage, 0); ?>%</span>
      </div>
      <?php endfor; ?>
    </div>

    <!-- Add Review Button -->
    <!-- <button class="add-review-btn">Add your review</button> -->
    <button id="write-review-btn" class="write-review-btn">Add Your Review</button>
  </div>

  <!-- Right Side: Reviews -->
  <div class="review-list">
    <h2>Reviews </h2>
    <?php if (have_comments()) : ?>
    <ul class="review-items">
      <?php
        wp_list_comments([
					'callback' => function ($comment, $args, $depth) {
							$GLOBALS['comment'] = $comment;
							$rating = get_comment_meta($comment->comment_ID, 'rating', true);
							$images = get_comment_meta($comment->comment_ID, 'review_images', true);
							$avatar = get_avatar($comment->comment_author_email, 50); // Get avatar
							?>
      <li class="review-item">
        <div class="review-header">
          <div class="review-avatar">
            <?php echo $avatar; ?>
          </div>
          <div class="review-user-info">
            <span class="review-author"><?php echo get_comment_author(); ?></span>
            <span class="review-date"><?php echo get_comment_date(); ?></span>
          </div>
        </div>
        <div class="review-rating"><?php echo wc_get_rating_html($rating); ?></div>
        <div class="review-text"><?php echo get_comment_text(); ?></div>
        <!-- Display Images -->
        <?php if (!empty($images)) : ?>
        <div class="review-images">
          <?php foreach ($images as $image_url) : ?>
          <img src="<?php echo esc_url($image_url); ?>" alt="Review Image">
          <?php endforeach; ?>
        </div>
        <?php endif; ?>
        <!-- Like & Report -->
        <!-- <div class="review-actions">
          <span class="like-btn">👍 20</span>
          <span class="report-btn">🚨 Report</span>
        </div> -->
      </li>
      <?php }
      ]);
		   ?>
    </ul>
    <?php else : ?>
    <p>No reviews yet.</p>
    <?php endif; ?>


    <!-- add review form  -->
    <?php if ( get_option( 'woocommerce_review_rating_verification_required' ) === 'no' || wc_customer_bought_product( '', get_current_user_id(), $product->get_id() ) ) : ?>
    <div id="review_form_wrapper" class="review_form_wrapper mt-4 ">
      <div id="review_form">
        <?php
				$commenter    = wp_get_current_commenter();
				$comment_form = array(
					/* translators: %s is product title */
					'title_reply'         => have_comments() ? esc_html__( 'Add a review', 'woocommerce' ) : sprintf( esc_html__( 'Be the first to review &ldquo;%s&rdquo;', 'woocommerce' ), get_the_title() ),
					/* translators: %s is product title */
					'title_reply_to'      => esc_html__( 'Leave a Reply to %s', 'woocommerce' ),
					'title_reply_before'  => '<span id="reply-title" class="comment-reply-title" role="heading" aria-level="3">',
					'title_reply_after'   => '</span>',
					'comment_notes_after' => '',
					'label_submit'        => esc_html__( 'Submit', 'woocommerce' ),
					'logged_in_as'        => '',
					'comment_field'       => '',
				);

				$name_email_required = (bool) get_option( 'require_name_email', 1 );
				$fields              = array(
					'author' => array(
						'label'        => __( 'Name', 'woocommerce' ),
						'type'         => 'text',
						'value'        => $commenter['comment_author'],
						'required'     => $name_email_required,
						'autocomplete' => 'name',
					),
					'email'  => array(
						'label'        => __( 'Email', 'woocommerce' ),
						'type'         => 'email',
						'value'        => $commenter['comment_author_email'],
						'required'     => $name_email_required,
						'autocomplete' => 'email',
					),
				);

				$comment_form['fields'] = array();

				foreach ( $fields as $key => $field ) {
					$field_html  = '<p class="comment-form-' . esc_attr( $key ) . '">';
					$field_html .= '<label for="' . esc_attr( $key ) . '">' . esc_html( $field['label'] );

					if ( $field['required'] ) {
						$field_html .= '&nbsp;<span class="required">*</span>';
					}

					$field_html .= '</label><input id="' . esc_attr( $key ) . '" name="' . esc_attr( $key ) . '" type="' . esc_attr( $field['type'] ) . '" autocomplete="' . esc_attr( $field['autocomplete'] ) . '" value="' . esc_attr( $field['value'] ) . '" size="30" ' . ( $field['required'] ? 'required' : '' ) . ' /></p>';

					$comment_form['fields'][ $key ] = $field_html;
				}

				$account_page_url = wc_get_page_permalink( 'myaccount' );
				if ( $account_page_url ) {
					/* translators: %s opening and closing link tags respectively */
					$comment_form['must_log_in'] = '<p class="must-log-in">' . sprintf( esc_html__( 'You must be %1$slogged in%2$s to post a review.', 'woocommerce' ), '<a href="' . esc_url( $account_page_url ) . '">', '</a>' ) . '</p>';
				}

				if ( wc_review_ratings_enabled() ) {
					$comment_form['comment_field'] = '<div class="comment-form-rating"><label for="rating" id="comment-form-rating-label">' . esc_html__( 'Your rating', 'woocommerce' ) . ( wc_review_ratings_required() ? '&nbsp;<span class="required">*</span>' : '' ) . '</label><select name="rating" id="rating" required>
						<option value="">' . esc_html__( 'Rate&hellip;', 'woocommerce' ) . '</option>
						<option value="5">' . esc_html__( 'Perfect', 'woocommerce' ) . '</option>
						<option value="4">' . esc_html__( 'Good', 'woocommerce' ) . '</option>
						<option value="3">' . esc_html__( 'Average', 'woocommerce' ) . '</option>
						<option value="2">' . esc_html__( 'Not that bad', 'woocommerce' ) . '</option>
						<option value="1">' . esc_html__( 'Very poor', 'woocommerce' ) . '</option>
					</select></div>';
				}

				$comment_form['comment_field'] .= '<p class="comment-form-comment"><label for="comment">' . esc_html__( 'Your review', 'woocommerce' ) . '&nbsp;<span class="required">*</span></label><textarea id="comment" name="comment" cols="45" rows="8" required></textarea></p>';

				comment_form( apply_filters( 'woocommerce_product_review_comment_form_args', $comment_form ) );
				?>
      </div>
    </div>
    <?php else : ?>
    <p class="woocommerce-verification-required">
      <?php esc_html_e( 'Only logged in customers who have purchased this product may leave a review.', 'woocommerce' ); ?>
    </p>
    <?php endif; ?>

  </div>

</div>