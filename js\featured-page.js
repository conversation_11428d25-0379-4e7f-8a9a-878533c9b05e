/**
 * JavaScript for Featured Page Dynamic Functionality
 */
jQuery(document).ready(function($) {

    // Tab switching functionality
    $('.featured-tab').on('click', function(e) {
        e.preventDefault();

        const $clickedTab = $(this);
        const tabType = $clickedTab.data('tab');

        // Don't reload if already selected
        if ($clickedTab.hasClass('selected-ops')) {
            return;
        }

        // Update tab appearance
        $('.featured-tab').removeClass('selected-ops');
        $clickedTab.addClass('selected-ops');

        // Reset category selection to "All"
        $('.category-item').removeClass('active');
        $('.category-item[data-category="all"]').addClass('active');

        // Load products for selected tab and update categories
        loadProductsByType(tabType);
        loadCategoriesForTab(tabType);
    });

    // Category switching functionality
    $(document).on('click', '.category-item', function(e) {
        e.preventDefault();

        const $clickedCategory = $(this);
        const categorySlug = $clickedCategory.data('category');
        const currentTab = $('.featured-tab.selected-ops').data('tab');

        // Don't reload if already selected
        if ($clickedCategory.hasClass('active')) {
            return;
        }

        // Update category appearance
        $('.category-item').removeClass('active');
        $clickedCategory.addClass('active');

        // Load products for selected tab and category
        loadProductsByType(currentTab, categorySlug);
    });

    /**
     * Load products based on type and category
     */
    function loadProductsByType(type, category = 'all') {
        // Hide current content
        $('#featured-products-container').hide();
        // Show loading state
        $('#featured-products-loading').show();

        // Make AJAX request
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'load_featured_products',
                product_type: type,
                category: category,
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update section title
                    $('#products-section-title').text(response.data.title);

                    // Update products grid
                    $('#products-grid').html(response.data.products);

                    // Hide loading and show content
                    $('#featured-products-loading').hide();
                    $('#featured-products-container').show();

                    // Trigger animations for new products
                    triggerProductAnimations();

                    // Reinitialize any product-specific JavaScript
                    initializeProductInteractions();
                } else {
                    console.error('Error loading products:', response.data);
                    showErrorMessage('Failed to load products. Please try again.');
                }
            },
            error: function() {
                console.error('AJAX Error: Failed to load products');
                showErrorMessage('Failed to load products. Please check your connection.');
            }
        });
    }

    /**
     * Load categories for specific tab
     */
    function loadCategoriesForTab(type) {
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'load_featured_categories',
                product_type: type,
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#category-list').html(response.data.categories);
                }
            },
            error: function() {
                console.error('Failed to load categories');
            }
        });
    }

    /**
     * Trigger animations for newly loaded products
     */
    function triggerProductAnimations() {
        // No animations - just ensure products are visible
        $('#products-grid .col-lg-3, #products-grid .col-md-4, #products-grid .col-sm-6').each(function() {
            $(this).css('opacity', '1').addClass('animate-in');
        });
    }

    /**
     * Show error message
     */
    function showErrorMessage(message) {
        $('#featured-products-loading').hide();
        $('#products-grid').html('<div class="col-12"><div class="alert alert-danger text-center">' + message + '</div></div>');
        $('#featured-products-container').show();
    }

    /**
     * Initialize product interactions (hover effects, add to cart, etc.)
     */
    function initializeProductInteractions() {
        // Product hover effects
        $('.woocommerce ul.products li.product').off('mouseenter mouseleave').hover(
            function() {
                $(this).addClass('product-hover');
            },
            function() {
                $(this).removeClass('product-hover');
            }
        );

        // Add to cart button effects
        $('.add_to_cart_button').off('click.featured').on('click.featured', function() {
            const $button = $(this);
            // No loading class added - no spinning animation

            // Optional: Add a simple visual feedback without animation
            $button.css('opacity', '0.7');
            setTimeout(function() {
                $button.css('opacity', '1');
            }, 1000);
        });

        // Wishlist button effects (if available)
        $('.yith-wcwl-add-to-wishlist a, .add_to_wishlist').off('click.featured').on('click.featured', function() {
            $(this).addClass('adding-to-wishlist');
        });
    }

    /**
     * Initialize on page load
     */
    function init() {
        // Initialize product interactions for initially loaded products
        initializeProductInteractions();

        // Add scrolling for shop now buttons (no animation)
        $('.btn-warning').on('click', function(e) {
            if ($(this).text().toLowerCase().includes('shop')) {
                e.preventDefault();
                $('html, body').scrollTop($('#featured-products-container').offset().top - 100);
            }
        });
    }

    // Initialize everything
    init();
});
