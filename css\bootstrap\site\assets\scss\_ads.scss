// stylelint-disable declaration-no-important, selector-max-id

//
// Carbon ads
//

#carbonads {
  position: static;
  display: block;
  max-width: 400px;
  padding: 15px 15px 15px 160px;
  margin: 2rem 0;
  overflow: hidden;
  @include font-size(.8125rem);
  line-height: 1.4;
  text-align: left;
  background-color: var(--bs-tertiary-bg);

  a {
    color: var(--bs-body-color);
    text-decoration: none;
  }

  @include media-breakpoint-up(sm) {
    @include border-radius(.5rem);
  }
}

.carbon-img {
  float: left;
  margin-left: -145px;
}

.carbon-poweredby {
  display: block;
  margin-top: .75rem;
  color: var(--bs-body-color) !important;
}
