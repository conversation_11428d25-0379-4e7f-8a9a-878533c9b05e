/**
 * Dynamic Shop Filters JavaScript
 */

jQuery(document).ready(function ($) {
  'use strict';

  // Hide all attribute filters by default
  $('.attribute-filter').hide();

  // Hide all subcategory lists by default, except those with active items
  $('.subcategory-list').each(function () {
    if ($(this).find('.subcategory-item.active').length === 0) {
      $(this).hide();
    } else {
      // If this subcategory list has an active item, change the toggle button text
      const parentId = $(this).attr('id').replace('subcategory-list-', '');
      $(`.toggle-subcategories[data-parent-id="${parentId}"]`).text('-');
    }
  });

  // Function to show all brands
  function showAllBrands() {
    // Remove any filtering classes from brands
    $('.brand-logo-item').removeClass('filtered-out hidden-brand was-hidden');

    console.log('Showing all brands');
  }

  // Function to filter brands by category
  function filterBrandsByCategory(categoryId) {
    console.log('filterBrandsByCategory called with categoryId:', categoryId);

    // If no category is selected, show all brands
    if (!categoryId) {
      console.log('No categoryId, showing all brands');
      showAllBrands();
      return;
    }

    // Get the category element
    const $category = $(`.category-link[data-category-id="${categoryId}"]`);
    console.log('Category element found:', $category.length > 0);

    if (!$category.length) {
      console.log('Category element not found, showing all brands');
      showAllBrands();
      return;
    }

    // Check if this is a category or subcategory
    const isSubcategory = $category.closest('.subcategory-item').length > 0;
    console.log('Is subcategory:', isSubcategory);

    if (isSubcategory) {
      // If it's a subcategory, just use its brands
      const relatedBrands = $category.data('brands');
      console.log('Subcategory related brands data:', relatedBrands);

      if (!relatedBrands) {
        // For subcategories with no brands, try to get brands from parent category
        const parentCategoryId = $category.closest('.subcategory-list').attr('id').replace('subcategory-list-', '');
        console.log('Parent category ID:', parentCategoryId);

        if (parentCategoryId) {
          const $parentCategory = $(`.category-link[data-category-id="${parentCategoryId}"]`);
          const parentRelatedBrands = $parentCategory.data('brands');

          if (parentRelatedBrands) {
            console.log('Using parent category brands:', parentRelatedBrands);
            filterBrandsByBrandIds(parentRelatedBrands);
            return;
          }
        }

        console.log('No related brands data for subcategory, showing all brands');
        showAllBrands();
        return;
      }

      // Filter brands by the subcategory's related brand IDs
      filterBrandsByBrandIds(relatedBrands);
    } else {
      // If it's a main category, collect brands from it and all its subcategories
      let allBrandIds = [];

      // Get brands from the main category
      const mainCategoryBrands = $category.data('brands');
      if (mainCategoryBrands) {
        const mainBrandIds = typeof mainCategoryBrands === 'string'
          ? mainCategoryBrands.split(',').filter(Boolean)
          : (Array.isArray(mainCategoryBrands) ? mainCategoryBrands : []);

        allBrandIds = [...allBrandIds, ...mainBrandIds];
        console.log('Main category brands:', mainBrandIds);
      }

      // Get the subcategory list for this category
      const subcategoryListId = `subcategory-list-${categoryId}`;
      const $subcategoryList = $(`#${subcategoryListId}`);

      if ($subcategoryList.length) {
        // Find all subcategories and collect their brands
        $subcategoryList.find('.category-link').each(function () {
          const subcategoryBrands = $(this).data('brands');
          if (subcategoryBrands) {
            const subBrandIds = typeof subcategoryBrands === 'string'
              ? subcategoryBrands.split(',').filter(Boolean)
              : (Array.isArray(subcategoryBrands) ? subcategoryBrands : []);

            allBrandIds = [...allBrandIds, ...subBrandIds];
            console.log('Adding subcategory brands:', subBrandIds);
          }
        });
      }

      // Remove duplicates
      allBrandIds = [...new Set(allBrandIds)];
      console.log('All collected brand IDs:', allBrandIds);

      if (allBrandIds.length === 0) {
        console.log('No brands found for category or subcategories, showing all brands');
        showAllBrands();
        return;
      }

      // Filter brands by all collected brand IDs
      filterBrandsByBrandIds(allBrandIds.join(','));
    }
  }

  // Helper function to filter brands by brand IDs
  function filterBrandsByBrandIds(brandsData) {
    // Convert to array if it's a string
    const brandIds = typeof brandsData === 'string'
      ? brandsData.split(',').filter(Boolean)
      : (Array.isArray(brandsData) ? brandsData : []);

    console.log('Brand IDs array:', brandIds);

    // If no related brands, show all
    if (!brandIds.length) {
      console.log('No brand IDs in array, showing all brands');
      showAllBrands();
      return;
    }

    // First, add the 'filtered' class to all brands
    $('.brand-logo-item').addClass('filtered-out');
    console.log('Marked all brands as filtered out');

    // Remove the 'filtered-out' class from related brands
    brandIds.forEach(brandId => {
      const $brand = $(`.brand-logo-item[data-brand-id="${brandId}"]`);
      console.log(`Looking for brand with ID ${brandId}:`, $brand.length > 0);
      $brand.removeClass('filtered-out');

      // If this brand is beyond the first 12, make sure it's visible when expanded
      if ($brand.hasClass('hidden-brand')) {
        $brand.removeClass('hidden-brand').addClass('was-hidden');
      }
    });

    // Note: Show All Brands button removed - using scrollable container
  }

  // Function to show attributes related to a category
  function showAttributesByCategory(categoryId) {
    console.log('showAttributesByCategory called with categoryId:', categoryId);

    // If no category is selected, hide all attributes
    if (!categoryId) {
      console.log('No categoryId, hiding all attributes');
      $('.attribute-filter').hide();
      return;
    }

    // Get the category element
    const $category = $(`.category-link[data-category-id="${categoryId}"]`);
    console.log('Category element found:', $category.length > 0);

    if (!$category.length) {
      console.log('Category element not found, hiding all attributes');
      $('.attribute-filter').hide();
      return;
    }

    // Check if this is a category or subcategory
    const isSubcategory = $category.closest('.subcategory-item').length > 0;
    console.log('Is subcategory:', isSubcategory);

    // Initialize an object to store all attribute data
    const allAttributes = {};

    if (isSubcategory) {
      // If it's a subcategory, get its attributes and parent's attributes
      let subcategoryAttributesData = $category.data('attributes');
      console.log('Subcategory attributes data:', subcategoryAttributesData);

      // Process subcategory attributes
      if (subcategoryAttributesData) {
        processAttributesData(subcategoryAttributesData, allAttributes);
      }

      // Get parent category attributes
      const parentCategoryId = $category.closest('.subcategory-list').attr('id').replace('subcategory-list-', '');
      console.log('Parent category ID:', parentCategoryId);

      if (parentCategoryId) {
        const $parentCategory = $(`.category-link[data-category-id="${parentCategoryId}"]`);
        const parentAttributesData = $parentCategory.data('attributes');

        if (parentAttributesData) {
          console.log('Using parent category attributes:', parentAttributesData);
          processAttributesData(parentAttributesData, allAttributes);
        }
      }
    } else {
      // If it's a main category, collect attributes from it and all its subcategories

      // Get attributes from the main category
      const mainCategoryAttributes = $category.data('attributes');
      if (mainCategoryAttributes) {
        console.log('Main category attributes:', mainCategoryAttributes);
        processAttributesData(mainCategoryAttributes, allAttributes);
      }

      // Get the subcategory list for this category
      const subcategoryListId = `subcategory-list-${categoryId}`;
      const $subcategoryList = $(`#${subcategoryListId}`);

      if ($subcategoryList.length) {
        // Find all subcategories and collect their attributes
        $subcategoryList.find('.category-link').each(function () {
          const subcategoryAttributes = $(this).data('attributes');
          if (subcategoryAttributes) {
            console.log('Adding subcategory attributes:', subcategoryAttributes);
            processAttributesData(subcategoryAttributes, allAttributes);
          }
        });
      }
    }

    // Check if we have any attributes to show
    const attributeCount = Object.keys(allAttributes).length;
    console.log('Total attributes collected:', attributeCount);

    if (attributeCount === 0) {
      console.log('No attributes data, hiding all attributes');
      $('.attribute-filter').hide();
      return;
    }

    // Show the attributes section
    $('#attributes-section').show();
    console.log('Showing attributes section');

    // Make sure the attributes section is visible
    $('#attributes-section').css('display', 'block');

    // Hide all attribute filters first
    $('.attribute-filter').hide();
    console.log('Hidden all attribute filters');

    // Show collected attributes
    for (const attrName in allAttributes) {
      const termIds = allAttributes[attrName];
      console.log(`Processing attribute: ${attrName} with terms:`, termIds);

      // Show this attribute filter
      const $attrFilter = $(`.attribute-filter[data-attribute="${attrName}"]`);
      console.log(`Attribute filter element found:`, $attrFilter.length > 0);
      $attrFilter.show();

      // Hide all terms first
      $(`.attribute-filter[data-attribute="${attrName}"] .attribute-item`).hide();
      console.log(`Hidden all terms for attribute ${attrName}`);

      // Show only related terms
      termIds.forEach(termId => {
        const $term = $(`.attribute-filter[data-attribute="${attrName}"] .attribute-item[data-term-id="${termId}"]`);
        console.log(`Looking for term with ID ${termId}:`, $term.length > 0);
        $term.show();
      });
    }
  }

  // Helper function to process attributes data and add to the allAttributes object
  function processAttributesData(attributesData, allAttributes) {
    if (!attributesData) return;

    const attributePairs = attributesData.split(';').filter(Boolean);

    attributePairs.forEach(pair => {
      const [attrName, termIdsStr] = pair.split(':');

      if (!attrName || !termIdsStr) {
        console.log('Invalid attribute pair, skipping');
        return;
      }

      // Get term IDs as array
      const termIdArray = termIdsStr.split(',').filter(Boolean);

      // Initialize attribute in allAttributes if not exists
      if (!allAttributes[attrName]) {
        allAttributes[attrName] = [];
      }

      // Add term IDs to the attribute, avoiding duplicates
      termIdArray.forEach(termId => {
        if (!allAttributes[attrName].includes(termId)) {
          allAttributes[attrName].push(termId);
        }
      });
    });
  }

  // Function to show attributes when both category and brand are selected
  function showAttributesByBoth(categoryId, brandId) {
    console.log('showAttributesByBoth called with categoryId:', categoryId, 'brandId:', brandId);

    if (!categoryId || !brandId) {
      console.log('Missing categoryId or brandId, hiding all attributes');
      $('.attribute-filter').hide();
      return;
    }

    // Get the category element
    const $category = $(`.category-link[data-category-id="${categoryId}"]`);
    if (!$category.length) {
      console.log('Category element not found, hiding all attributes');
      $('.attribute-filter').hide();
      return;
    }

    // Get the brand element
    const $brand = $(`.brand-logo-item[data-brand-id="${brandId}"]`);
    if (!$brand.length) {
      console.log('Brand element not found, hiding all attributes');
      $('.attribute-filter').hide();
      return;
    }

    // Initialize an object to store all attribute data
    const allAttributes = {};

    // Get attributes from the category
    const categoryAttributes = $category.data('attributes');
    if (categoryAttributes) {
      console.log('Category attributes:', categoryAttributes);
      processAttributesData(categoryAttributes, allAttributes);
    }

    // Get categories related to this brand
    const relatedCategories = $brand.data('categories');
    if (relatedCategories) {
      const categoryIds = typeof relatedCategories === 'string'
        ? relatedCategories.split(',').filter(Boolean)
        : (Array.isArray(relatedCategories) ? relatedCategories : []);

      // Find categories that have attribute data
      for (const catId of categoryIds) {
        // Skip if it's the same as the selected category
        if (catId === categoryId) continue;

        const $relatedCategory = $(`.category-link[data-category-id="${catId}"]`);
        if ($relatedCategory.length) {
          const relatedCategoryAttributes = $relatedCategory.data('attributes');
          if (relatedCategoryAttributes) {
            console.log('Related category attributes from brand:', relatedCategoryAttributes);
            processAttributesData(relatedCategoryAttributes, allAttributes);
          }
        }
      }
    }

    // Check if we have any attributes to show
    const attributeCount = Object.keys(allAttributes).length;
    console.log('Total attributes collected:', attributeCount);

    if (attributeCount === 0) {
      console.log('No attributes data, hiding all attributes');
      $('.attribute-filter').hide();
      return;
    }

    // Show the attributes section
    $('#attributes-section').show();
    console.log('Showing attributes section');

    // Make sure the attributes section is visible
    $('#attributes-section').css('display', 'block');

    // Hide all attribute filters first
    $('.attribute-filter').hide();
    console.log('Hidden all attribute filters');

    // Show collected attributes
    for (const attrName in allAttributes) {
      const termIds = allAttributes[attrName];
      console.log(`Processing attribute: ${attrName} with terms:`, termIds);

      // Show this attribute filter
      const $attrFilter = $(`.attribute-filter[data-attribute="${attrName}"]`);
      console.log(`Attribute filter element found:`, $attrFilter.length > 0);
      $attrFilter.show();

      // Hide all terms first
      $(`.attribute-filter[data-attribute="${attrName}"] .attribute-item`).hide();
      console.log(`Hidden all terms for attribute ${attrName}`);

      // Show only related terms
      termIds.forEach(termId => {
        const $term = $(`.attribute-filter[data-attribute="${attrName}"] .attribute-item[data-term-id="${termId}"]`);
        console.log(`Looking for term with ID ${termId}:`, $term.length > 0);
        $term.show();
      });
    }
  }

  // Get all active filters from the URL
  const urlParams = new URLSearchParams(window.location.search);

  // Create an object to store all active filters
  const activeFilters = {
    category: null,
    brand: null,
    attributes: {},
    minPrice: null,
    maxPrice: null,
    rating: null
  };

  // Check if we're on a category page
  const isCategoryPage = window.location.pathname.includes('/product-category/');
  const isBrandPage = window.location.pathname.includes('/product-brand/');

  // Try different possible category parameter names
  let selectedCategorySlug = urlParams.get('product_cat');

  // If not found, check if we're on a category page by looking at the URL path
  if (!selectedCategorySlug && isCategoryPage) {
    const pathParts = window.location.pathname.split('/');
    const productCategoryIndex = pathParts.indexOf('product-category');

    if (productCategoryIndex !== -1 && pathParts.length > productCategoryIndex + 1) {
      selectedCategorySlug = pathParts[productCategoryIndex + 1];
      console.log('Found category slug in URL path:', selectedCategorySlug);
    }
  }

  // Store category in activeFilters
  activeFilters.category = selectedCategorySlug;

  // Get brand filter - check URL parameter or brand page path
  let selectedBrand = urlParams.get('brand');

  // If not found and we're on a brand page, get from URL path
  if (!selectedBrand && isBrandPage) {
    const pathParts = window.location.pathname.split('/');
    const productBrandIndex = pathParts.indexOf('product-brand');

    if (productBrandIndex !== -1 && pathParts.length > productBrandIndex + 1) {
      selectedBrand = pathParts[productBrandIndex + 1];
      console.log('Found brand slug in URL path:', selectedBrand);
    }
  }

  activeFilters.brand = selectedBrand;

  // Get price filters
  const minPrice = urlParams.get('min_price');
  const maxPrice = urlParams.get('max_price');
  if (minPrice) activeFilters.minPrice = parseFloat(minPrice);
  if (maxPrice) activeFilters.maxPrice = parseFloat(maxPrice);

  // Get rating filter
  const rating = urlParams.get('rating');
  if (rating) activeFilters.rating = parseInt(rating);

  // Get attribute filters
  urlParams.forEach((value, key) => {
    if (key.startsWith('filter_')) {
      const attributeName = 'pa_' + key.replace('filter_', '');
      activeFilters.attributes[attributeName] = value;
    }
  });

  console.log('Active Filters:', activeFilters);
  console.log('Selected Category Slug:', selectedCategorySlug);
  console.log('Selected Brand:', selectedBrand);

  // Find the category ID from the slug
  let selectedCategoryId = null;
  if (selectedCategorySlug) {
    // First try to find an exact match using data-category-slug
    $('.category-link').each(function () {
      const categorySlug = $(this).data('category-slug');
      if (categorySlug === selectedCategorySlug) {
        selectedCategoryId = $(this).data('category-id');
        console.log('Found exact category ID match from data-category-slug:', selectedCategoryId);
        return false; // Break the loop
      }
    });

    // If not found, try to find by URL
    if (!selectedCategoryId) {
      $('.category-link').each(function () {
        const href = $(this).attr('href');
        // Check for exact match in URL
        if (href && (
          href.includes(`/product-category/${selectedCategorySlug}/`) ||
          href.includes(`/product-category/${selectedCategorySlug}`) ||
          href.endsWith(`=${selectedCategorySlug}`)
        )) {
          selectedCategoryId = $(this).data('category-id');
          console.log('Found exact category ID match from URL:', selectedCategoryId);
          return false; // Break the loop
        }
      });
    }

    // If no exact match, try a partial match
    if (!selectedCategoryId) {
      $('.category-link').each(function () {
        const href = $(this).attr('href');
        if (href && href.includes(selectedCategorySlug)) {
          selectedCategoryId = $(this).data('category-id');
          console.log('Found partial category ID match:', selectedCategoryId);
          return false; // Break the loop
        }
      });
    }

    // If still no match, check data attributes directly
    if (!selectedCategoryId) {
      // Try to find a category with matching slug in data attributes
      $('.category-item, .subcategory-item').each(function () {
        const $link = $(this).find('.category-link');
        const categoryId = $link.data('category-id');
        const href = $link.attr('href');

        if (href && href.includes(selectedCategorySlug)) {
          selectedCategoryId = categoryId;
          console.log('Found category ID from item:', selectedCategoryId);
          return false; // Break the loop
        }
      });
    }

    // Last resort: check if there's an active category
    if (!selectedCategoryId) {
      const $activeCategory = $('.category-item.active, .subcategory-item.active').first();
      if ($activeCategory.length) {
        const $link = $activeCategory.find('.category-link');
        selectedCategoryId = $link.data('category-id');
        console.log('Found category ID from active class:', selectedCategoryId);
      }
    }
  }

  // Handle mixed filtering (category and brand together)
  if (selectedCategoryId && selectedBrand) {
    console.log('Mixed filtering - Category ID:', selectedCategoryId, 'Brand:', selectedBrand);

    // Add mixed-filtering class to the body
    $('body').addClass('mixed-filtering');

    // If it's a main category, show its subcategories
    const $category = $(`.category-link[data-category-id="${selectedCategoryId}"]`);
    const isSubcategory = $category.length > 0 && $category.closest('.subcategory-item').length > 0;

    if (!isSubcategory) {
      // It's a main category, show its subcategories
      const subcategoryListId = `subcategory-list-${selectedCategoryId}`;
      const $subcategoryList = $(`#${subcategoryListId}`);

      if ($subcategoryList.length) {
        $subcategoryList.show();
        $(`.toggle-subcategories[data-parent-id="${selectedCategoryId}"]`).text('-');
      }
    }

    // Highlight the selected brand
    $('.brand-logo-item').removeClass('active');
    $(`.brand-logo-item[data-brand="${selectedBrand}"]`).addClass('active');

    // Get the brand element
    const $brandItem = $(`.brand-logo-item[data-brand="${selectedBrand}"]`);
    const brandId = $brandItem.data('brand-id');

    // Check if the selected brand is related to the selected category
    const categoryBrands = $category.data('brands');
    const categoryBrandIds = categoryBrands ? categoryBrands.toString().split(',') : [];

    if (categoryBrandIds.includes(brandId.toString())) {
      // Brand is related to category - show only this brand
      $('.brand-logo-item').hide();
      $brandItem.show();

      // Note: Show All Brands button removed - using scrollable container

      // Show attributes based on both category and brand
      showAttributesByBoth(selectedCategoryId, brandId);
    } else {
      // Brand is not related to category - show a message or fallback to category only
      console.log('Selected brand is not related to selected category');

      // Fallback to category filtering only
      filterBrandsByCategory(selectedCategoryId);
      showAttributesByCategory(selectedCategoryId);
    }
  }
  // If only category is selected
  else if (selectedCategoryId) {
    console.log('Filtering by category ID:', selectedCategoryId);

    // Remove mixed-filtering class from the body
    $('body').removeClass('mixed-filtering');

    // If we're on a category page, don't show category filtering UI
    if (isCategoryPage) {
      console.log('On category page - hiding category section and showing filtered data');
      $('#categories-section').hide();

      // Force show attributes section on category pages
      $('#attributes-section').show();
      $('.attribute-filter').show();

      // Show only brands and attributes related to this category
      filterBrandsByCategory(selectedCategoryId);
      showAttributesByCategory(selectedCategoryId);
    } else {
      // If it's a main category, show its subcategories
      const $category = $(`.category-link[data-category-id="${selectedCategoryId}"]`);
      const isSubcategory = $category.length > 0 && $category.closest('.subcategory-item').length > 0;

      if (!isSubcategory) {
        // It's a main category, show its subcategories
        const subcategoryListId = `subcategory-list-${selectedCategoryId}`;
        const $subcategoryList = $(`#${subcategoryListId}`);

        if ($subcategoryList.length) {
          $subcategoryList.show();
          $(`.toggle-subcategories[data-parent-id="${selectedCategoryId}"]`).text('-');
        }
      }

      filterBrandsByCategory(selectedCategoryId);
      showAttributesByCategory(selectedCategoryId);
    }

    // Note: Show All Brands button removed - using scrollable container
  }
  // If only brand is selected
  else if (selectedBrand) {
    console.log('Filtering by brand:', selectedBrand);

    // Remove mixed-filtering class from the body
    $('body').removeClass('mixed-filtering');

    // Highlight the selected brand
    $('.brand-logo-item').removeClass('active');
    $(`.brand-logo-item[data-brand="${selectedBrand}"]`).addClass('active');

    // Show all brands but highlight the selected one
    showAllBrands();

    // Show attributes related to the selected brand
    const $brandItem = $(`.brand-logo-item[data-brand="${selectedBrand}"]`);
    if ($brandItem.length) {
      console.log('Brand element found');
      const brandId = $brandItem.data('brand-id');
      console.log('Brand ID:', brandId);

      // If we're on a brand page, show filtered categories and attributes
      if (isBrandPage) {
        console.log('On brand page - showing categories and attributes related to this brand');

        // Show categories section (it should contain only related categories from PHP filtering)
        $('#categories-section').show();

        // Force show attributes section on brand pages
        $('#attributes-section').show();
        $('.attribute-filter').show();

        // Get categories related to this brand
        const relatedCategories = $brandItem.data('categories');
        console.log('Related Categories:', relatedCategories);

        if (relatedCategories) {
          const categoryIds = typeof relatedCategories === 'string'
            ? relatedCategories.split(',').filter(Boolean)
            : (Array.isArray(relatedCategories) ? relatedCategories : []);

          console.log('Category IDs:', categoryIds);

          // Find the first category that has attribute data
          for (const catId of categoryIds) {
            const $category = $(`.category-link[data-category-id="${catId}"]`);
            if ($category.length && $category.data('attributes')) {
              console.log('Found category with attributes:', catId);
              showAttributesByCategory(catId);
              break;
            }
          }
        }
      } else {
        // Regular brand filtering on shop page
        // Show the attributes section
        $('#attributes-section').show();

        // Get categories related to this brand
        const relatedCategories = $brandItem.data('categories');
        console.log('Related Categories:', relatedCategories);

        if (relatedCategories) {
          const categoryIds = typeof relatedCategories === 'string'
            ? relatedCategories.split(',').filter(Boolean)
            : (Array.isArray(relatedCategories) ? relatedCategories : []);

          console.log('Category IDs:', categoryIds);

          // Find the first category that has attribute data
          for (const catId of categoryIds) {
            const $category = $(`.category-link[data-category-id="${catId}"]`);
            if ($category.length && $category.data('attributes')) {
              console.log('Found category with attributes:', catId);
              showAttributesByCategory(catId);
              break;
            }
          }
        }
      }
    } else {
      console.log('Brand element not found');
      $('#attributes-section').hide();
    }
  } else {
    // If no category or brand is selected, show all brands and hide attributes
    console.log('No category or brand selected');

    // Remove mixed-filtering class from the body
    $('body').removeClass('mixed-filtering');

    showAllBrands();
    $('#attributes-section').hide();
  }

  // Handle category link clicks
  $('.category-link').on('click', function (e) {
    // Check if this is a main category
    const isMainCategory = $(this).closest('.subcategory-item').length === 0;
    const categoryId = $(this).data('category-id');
    const categorySlug = $(this).data('category-slug');

    // If we have other active filters, we need to preserve them
    if (Object.keys(activeFilters.attributes).length > 0 ||
      activeFilters.brand ||
      activeFilters.minPrice ||
      activeFilters.maxPrice ||
      activeFilters.rating) {

      e.preventDefault(); // Prevent the default link behavior

      // Create a new URL with all current parameters
      const currentUrl = new URL(window.location.href);

      // Update or add the category parameter
      if (currentUrl.pathname.includes('/product-category/')) {
        // We're on a category page, need to change the URL structure
        let newPath = currentUrl.pathname.split('/product-category/')[0];
        newPath += '/shop/'; // Redirect to shop page with parameters
        currentUrl.pathname = newPath;
      }

      // Set the category parameter
      currentUrl.searchParams.set('product_cat', categorySlug);

      // Navigate to the new URL
      window.location.href = currentUrl.toString();
      return;
    }

    if (isMainCategory && categoryId) {
      // Show subcategories for this category
      const subcategoryListId = `subcategory-list-${categoryId}`;
      const $subcategoryList = $(`#${subcategoryListId}`);

      if ($subcategoryList.length) {
        // Don't prevent default - let the link work normally
        // Just make sure subcategories will be visible when the page loads
        // This is handled by the URL parameter detection code
      }
    }

    // Let the link work normally if no other filters are active
  });

  // Handle subcategory toggle clicks
  $('.toggle-subcategories').on('click', function () {
    const parentId = $(this).data('parent-id');
    const $subcategoryList = $(`#subcategory-list-${parentId}`);

    if ($subcategoryList.is(':visible')) {
      $subcategoryList.hide();
      $(this).text('+');
    } else {
      $subcategoryList.show();
      $(this).text('-');
    }
  });

  // Handle brand logo clicks for mixed filtering
  $('.brand-logo-item').on('click', function (e) {
    e.preventDefault();

    const brandSlug = $(this).data('brand');

    // Construct the URL with all current parameters
    const currentUrl = new URL(window.location.href);

    // Add or update the brand parameter
    currentUrl.searchParams.set('brand', brandSlug);

    // Navigate to the new URL
    window.location.href = currentUrl.toString();
  });

  // Handle attribute link clicks for mixed filtering
  $('.attribute-link').on('click', function (e) {
    e.preventDefault();

    const attributeUrl = $(this).attr('href');
    const attributeParams = new URLSearchParams(new URL(attributeUrl).search);

    // Find the attribute parameter (starts with filter_)
    let attributeKey = null;
    let attributeValue = null;

    attributeParams.forEach((value, key) => {
      if (key.startsWith('filter_')) {
        attributeKey = key;
        attributeValue = value;
      }
    });

    if (!attributeKey || !attributeValue) {
      // If we couldn't find the attribute parameter, just navigate to the original URL
      window.location.href = attributeUrl;
      return;
    }

    // Construct the URL with all current parameters
    const currentUrl = new URL(window.location.href);

    // Add or update the attribute parameter
    currentUrl.searchParams.set(attributeKey, attributeValue);

    // Navigate to the new URL
    window.location.href = currentUrl.toString();
  });

  // Note: Show All Brands button functionality removed - now using scrollable container

  // Handle rating filter clicks
  $('.rating-link').on('click', function (e) {
    e.preventDefault();

    const ratingUrl = $(this).attr('href');
    const ratingParams = new URLSearchParams(new URL(ratingUrl).search);
    const ratingValue = ratingParams.get('rating');

    if (!ratingValue) {
      // If we couldn't find the rating parameter, just navigate to the original URL
      window.location.href = ratingUrl;
      return;
    }

    // Construct the URL with all current parameters
    const currentUrl = new URL(window.location.href);

    // Add or update the rating parameter
    currentUrl.searchParams.set('rating', ratingValue);

    // Reset to page 1 when applying filters
    currentUrl.searchParams.delete('paged');

    // Navigate to the new URL
    window.location.href = currentUrl.toString();
  });

  // Handle price filter apply button
  $('.price-filter-apply-btn').on('click', function (e) {
    e.preventDefault();

    const minPrice = $('#min-price').val();
    const maxPrice = $('#max-price').val();

    if (!minPrice && !maxPrice) {
      return; // No price values entered
    }

    // Construct the URL with all current parameters
    const currentUrl = new URL(window.location.href);

    // Add or update the price parameters
    if (minPrice) {
      currentUrl.searchParams.set('min_price', minPrice);
    } else {
      currentUrl.searchParams.delete('min_price');
    }

    if (maxPrice) {
      currentUrl.searchParams.set('max_price', maxPrice);
    } else {
      currentUrl.searchParams.delete('max_price');
    }

    // Reset to page 1 when applying filters
    currentUrl.searchParams.delete('paged');

    // Navigate to the new URL
    window.location.href = currentUrl.toString();
  });

  // Handle pagination clicks with AJAX for smoother experience - DISABLED
  function initPaginationAjax() {
    // AJAX pagination disabled to fix navigation issues
    // Pagination now works with normal page loads for better reliability
    console.log('AJAX pagination disabled - using normal page navigation');
  }

  // Initialize pagination AJAX - DISABLED
  // initPaginationAjax();

  // Handle browser back/forward navigation
  window.addEventListener('popstate', function (event) {
    if (event.state && event.state.shopFilters) {
      // Reload the page content when user navigates back/forward
      location.reload();
    }
  });

  // Handle reset filters button
  $('.reset-filters-btn').on('click', function (e) {
    e.preventDefault();

    // Navigate to the shop page without any parameters
    window.location.href = $(this).attr('href');
  });

  // Add a class to the body to indicate mixed filtering is active
  if (Object.keys(activeFilters.attributes).length > 0 ||
    activeFilters.brand ||
    activeFilters.category ||
    activeFilters.minPrice ||
    activeFilters.maxPrice ||
    activeFilters.rating) {
    $('body').addClass('mixed-filtering');

    // Count active filters
    let filterCount = 0;
    if (activeFilters.category) filterCount++;
    if (activeFilters.brand) filterCount++;
    if (activeFilters.minPrice || activeFilters.maxPrice) filterCount++;
    if (activeFilters.rating) filterCount++;
    filterCount += Object.keys(activeFilters.attributes).length;

    // Add a class with the number of active filters
    $('body').addClass(`filter-count-${filterCount}`);
  }

  // Debug information
  // Initialize filtered states for category and brand pages
  function initializeFilteredStates() {
    console.log('Initializing filtered states...');
    console.log('Is Category Page:', isCategoryPage);
    console.log('Is Brand Page:', isBrandPage);

    if (isCategoryPage) {
      console.log('Category page detected - forcing attributes to show');
      $('#attributes-section').show();
      $('.attribute-filter').show();
      $('#categories-section').hide();
    }

    if (isBrandPage) {
      console.log('Brand page detected - forcing attributes and categories to show');
      $('#attributes-section').show();
      $('.attribute-filter').show();
      $('#categories-section').show();
    }
  }

  // Call initialization
  initializeFilteredStates();

  console.log('Selected Category ID:', selectedCategoryId);
  console.log('Selected Brand:', selectedBrand);
  console.log('Available Attributes:', $('.attribute-filter').length);

  if (selectedCategoryId) {
    const $category = $(`.category-link[data-category-id="${selectedCategoryId}"]`);
    console.log('Category Element:', $category.length ? 'Found' : 'Not Found');
    console.log('Category Brands:', $category.data('brands'));
    console.log('Category Attributes:', $category.data('attributes'));
  }

  // Output all available categories for debugging
  console.log('Available Categories:');
  $('.category-link').each(function () {
    console.log(`ID: ${$(this).data('category-id')}, Slug: ${$(this).data('category-slug')}, Brands: ${$(this).data('brands')}`);
  });
});
