{{- /*
  Usage: `placeholder args`

  `args` are all optional and can be one of the following:
    * title: Used in the SVG `title` tag - default: "Placeholder"
    * text: The text to show in the image - default: "width x height"
    * class: Class to add to the `svg` or `img` - default: "bd-placeholder-img"
    * color: The text color (foreground) - default: "#dee2e6"
    * background: The background color - default: "#868e96"
    * width: default: "100%"
    * height: default: "180px"
    * markup: If it should render `svg` or `img` tags - default: "svg"
*/ -}}

{{- $grays := $.Site.Data.grays -}}
{{- $default_color := (index $grays 2).hex -}}
{{- $default_background := (index $grays 5).hex -}}

{{- $title := .Get "title" | default "Placeholder" -}}
{{- $class := .Get "class" -}}
{{- $color := .Get "color" | default $default_color -}}
{{- $background := .Get "background" | default $default_background -}}
{{- $width := .Get "width" | default "100%" -}}
{{- $height := .Get "height" | default "180" -}}
{{- $text := .Get "text" | default (printf "%sx%s" $width $height) -}}

{{- $show_title := not (eq $title "false") -}}
{{- $show_text := not (eq $text "false") -}}

{{- $markup := .Get "markup" | default "svg" -}}

{{- if eq $markup "img" -}}
  <img class="bd-placeholder-img{{ with $class }} {{ . }}{{ end }}" alt="{{ $title }} : {{ $text }}" width="{{ $width }}" height="{{ $height }}" src="data:image/svg+xml,%3Csvg%20style='font-size:%201.125rem;%20font-family:system-ui,-apple-system,%22Segoe%20UI%22,Roboto,%22Helvetica%20Neue%22,%22Noto%20Sans%22,%22Liberation%20Sans%22,Arial,sans-serif,%22Apple%20Color%20Emoji%22,%22Segoe%20UI%20Emoji%22,%22Segoe%20UI%20Symbol%22,%22Noto%20Color%20Emoji%22;%20-webkit-user-select:%20none;%20-moz-user-select:%20none;%20user-select:%20none;%20text-anchor:%20middle;'%20width='200'%20height='200'%20xmlns='http://www.w3.org/2000/svg'%3E
      {{- if $show_title }}%3Ctitle%3E{{ $title }}%3C/title%3E{{ end -}}
      %3Crect%20width='100%25'%20height='100%25'%20fill='{{ replace $background "#" "%23" }}'%3E%3C/rect%3E
      {{- if $show_text }}%3Ctext%20x='50%25'%20y='50%25'%20fill='{{ replace $color "#" "%23" }}'%20dy='.3em'%3E{{ $text }}%3C/text%3E{{ end -}}
    %3C/svg%3E">
{{- else -}}
  <svg class="bd-placeholder-img{{ with $class }} {{ . }}{{ end }}" width="{{ $width }}" height="{{ $height }}" xmlns="http://www.w3.org/2000/svg"{{ if (or $show_title $show_text) }} role="img" aria-label="{{ if $show_title }}{{ $title }}{{ if $show_text }}: {{ end }}{{ end }}{{ if ($show_text) }}{{ $text }}{{ end }}"{{ else }} aria-hidden="true"{{ end }} preserveAspectRatio="xMidYMid slice" focusable="false">
    {{- if $show_title }}<title>{{ $title }}</title>{{ end -}}
    <rect width="100%" height="100%" fill="{{ $background }}"/>
    {{- if $show_text }}<text x="50%" y="50%" fill="{{ $color }}" dy=".3em">{{ $text }}</text>{{ end -}}
  </svg>
{{- end -}}
