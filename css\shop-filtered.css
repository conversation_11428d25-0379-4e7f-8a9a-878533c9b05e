/* Shop Page with Filters Styles */

:root {
  --primary-color: #ea9c00;
  --secondary-color: #282f39;
  --success-color: #12b76a;
  --warning-color: #f79009;
  --danger-color: #f04438;
  --light-color: #98a2b3;
  --dark-color: #1d2939;
  --gray-color: #98a2b3;
  --body-bg: #f4f7fd;
  --border-color: rgba(152, 162, 179, 0.25);
}

/* Shop Container */
.shop-container {
  padding: 30px 0;
  background-color: var(--body-bg);
}

/* Sidebar Styles */
.shop-sidebar {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

.filter-section {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-section:last-child {
  margin-bottom: 20px;
}

/* Current Category Info */
.current-category-info {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.category-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 10px;
}

.category-description {
  font-size: 14px;
  color: var(--secondary-color);
  line-height: 1.5;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.filter-title::after {
  content: '\F282';
  font-family: 'Bootstrap Icons';
  font-size: 14px;
  transition: transform 0.3s ease;
}

.filter-title.collapsed::after {
  transform: rotate(-90deg);
}

/* Category Filter */
.category-list,
.subcategory-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item,
.subcategory-item {
  margin-bottom: 10px;
}

.subcategory-list {
  margin-left: 20px;
  margin-top: 10px;
}

.category-item a,
.subcategory-item a {
  color: var(--secondary-color);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.category-item a:hover,
.subcategory-item a:hover {
  color: var(--primary-color);
}

.category-item.current>a,
.subcategory-item.current>a {
  color: var(--primary-color);
  font-weight: 600;
}

.back-to-parent {
  margin-top: 15px;
  font-size: 14px;
}

.back-to-parent a {
  color: var(--secondary-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 5px;
}

.back-to-parent a:hover {
  color: var(--primary-color);
}

.category-item label,
.subcategory-item label,
.brand-item label,
.color-item label,
.rating-item label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: var(--secondary-color);
}

.filter-checkbox {
  margin-right: 10px;
}

/* Brand Filter */
.brand-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.brand-item {
  margin-bottom: 10px;
}

/* Color Filter */
.color-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.color-item {
  margin-bottom: 10px;
  width: calc(50% - 5px);
}

.color-swatch {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  border: 1px solid var(--border-color);
}

/* Rating Filter */
.rating-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.rating-item {
  margin-bottom: 10px;
}

.rating-item .bi-star-fill {
  color: var(--primary-color);
  font-size: 14px;
}

.rating-item .bi-star {
  color: var(--light-color);
  font-size: 14px;
}

/* Price Filter */
.price-filter .price_slider_wrapper {
  padding: 10px 0;
}

.price-filter .price_slider {
  height: 4px;
  background-color: var(--light-color) !important;
  margin: 15px 0;
  border-radius: 2px;
}

.price-filter .ui-slider-range {
  background-color: var(--primary-color) !important;
}

.price-filter .ui-slider-handle {
  background-color: #fff !important;
  border: 2px solid var(--primary-color) !important;
  border-radius: 50% !important;
  top: -0.5em !important;
  width: 1em !important;
  height: 1em !important;
  cursor: pointer !important;
}

.price-filter .price_slider_amount {
  display: flex;
  flex-direction: column-reverse;
}

.price-filter .price_label {
  margin-bottom: 10px;
  font-size: 14px;
  color: var(--secondary-color);
}

.price-filter button {
  background-color: var(--primary-color) !important;
  color: #fff !important;
  border: none !important;
  padding: 8px 15px !important;
  border-radius: 5px !important;
  cursor: pointer !important;
  font-size: 14px !important;
}

.price-filter button:hover {
  background-color: #d08a00 !important;
}

/* Filter Actions */
.filter-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.filter-actions .btn {
  flex: 1;
  padding: 10px;
  font-size: 14px;
}

/* Main Content Area */
.shop-main-content {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Shop Header */
.shop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.shop-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--dark-color);
  margin: 0;
}

.shop-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.product-count {
  font-size: 14px;
  color: var(--secondary-color);
}

.shop-sorting .form-select {
  padding: 8px 12px;
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid var(--border-color);
  background-color: #fff;
  color: var(--secondary-color);
  cursor: pointer;
}

.shop-view-mode {
  display: flex;
  gap: 5px;
}

.view-mode-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-mode-btn.active {
  background-color: var(--primary-color);
  color: #fff;
  border-color: var(--primary-color);
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.products-grid.list-view {
  grid-template-columns: 1fr;
}

/* Ensure product cards are always 3 in a row */
.products-grid li.product-card {
  width: 100%;
  margin-right: 0 !important;
  margin-left: 0 !important;
  clear: none !important;
}

/* Override WooCommerce default column settings */
.woocommerce ul.products,
.woocommerce-page ul.products {
  display: flex !important;
  flex-wrap: wrap !important;
  margin: 0 -10px !important;
  width: calc(100% + 20px) !important;
}

.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
  float: none !important;
  margin: 0 10px 20px !important;
  padding: 0 !important;
  width: calc(33.333% - 20px) !important;
  clear: none !important;
  box-sizing: border-box !important;
}

.woocommerce ul.products::before,
.woocommerce ul.products::after,
.woocommerce-page ul.products::before,
.woocommerce-page ul.products::after {
  display: none !important;
}

/* Fix for WooCommerce columns */
.woocommerce.columns-1 ul.products li.product {
  width: calc(100% - 20px) !important;
}

.woocommerce.columns-2 ul.products li.product {
  width: calc(50% - 20px) !important;
}

.woocommerce.columns-3 ul.products li.product {
  width: calc(33.333% - 20px) !important;
}

.woocommerce.columns-4 ul.products li.product {
  width: calc(25% - 20px) !important;
}

.woocommerce.columns-5 ul.products li.product {
  width: calc(20% - 20px) !important;
}

.woocommerce.columns-6 ul.products li.product {
  width: calc(16.666% - 20px) !important;
}

/* Fix for WooCommerce pagination */
.woocommerce nav.woocommerce-pagination {
  text-align: center;
  margin-top: 30px;
}

.woocommerce nav.woocommerce-pagination ul {
  border: none;
  display: inline-flex;
}

.woocommerce nav.woocommerce-pagination ul li {
  border: 1px solid var(--border-color);
  margin: 0 5px;
  border-radius: 5px;
  overflow: hidden;
}

.woocommerce nav.woocommerce-pagination ul li a,
.woocommerce nav.woocommerce-pagination ul li span {
  padding: 10px 15px;
  color: var(--secondary-color);
}

.woocommerce nav.woocommerce-pagination ul li a:hover,
.woocommerce nav.woocommerce-pagination ul li span.current {
  background-color: var(--primary-color);
  color: #fff;
}

/* Basic Theme Product Card Styling */
.woocommerce ul.products li.product {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding-bottom: 15px !important;
}

.woocommerce ul.products li.product:hover {
  /* No transform animation */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.woocommerce ul.products li.product a img {
  margin: 0 0 10px !important;
  border-radius: 8px 8px 0 0;
}

.woocommerce ul.products li.product .woocommerce-loop-product__title {
  padding: 0 15px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
  color: #333;
  height: 40px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.woocommerce ul.products li.product .star-rating {
  margin: 0 15px 8px !important;
  color: #FFB800;
}

.woocommerce ul.products li.product .price {
  padding: 0 15px !important;
  color: #333 !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  margin-bottom: 10px !important;
}

.woocommerce ul.products li.product .price del {
  color: #999 !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  opacity: 0.8 !important;
}

.woocommerce ul.products li.product .price ins {
  font-weight: 600 !important;
  text-decoration: none !important;
}

.woocommerce ul.products li.product .button {
  margin: 0 15px !important;
  display: inline-block !important;
  background-color: var(--primary-color, #f0c14b) !important;
  color: #fff !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  text-transform: none !important;
  transition: all 0.3s ease !important;
  padding: 8px 15px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.woocommerce ul.products li.product .button:hover {
  background-color: var(--primary-hover-color, #d8a935) !important;
  color: #fff !important;
}

.woocommerce span.onsale {
  background-color: #000 !important;
  color: #fff !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  min-height: 3em !important;
  min-width: 3em !important;
  line-height: 3em !important;
  border-radius: 50% !important;
  padding: 0 !important;
}

/* Pagination */
.shop-pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.shop-pagination .page-numbers {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  margin: 0 5px;
  border-radius: 5px;
  background-color: #fff;
  border: 1px solid var(--border-color);
  color: var(--secondary-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

.shop-pagination .page-numbers.current {
  background-color: var(--primary-color);
  color: #fff;
  border-color: var(--primary-color);
}

.shop-pagination .page-numbers:hover {
  background-color: var(--primary-color);
  color: #fff;
  border-color: var(--primary-color);
}

/* No Products */
.no-products {
  text-align: center;
  padding: 30px;
  color: var(--secondary-color);
  font-size: 16px;
}

/* Responsive Styles */
@media (max-width: 1200px) {

  /* Maintain 3 products per row on medium-large screens */
  .woocommerce ul.products li.product,
  .woocommerce-page ul.products li.product {
    width: calc(33.333% - 20px) !important;
    margin: 0 10px 20px !important;
  }

  .product-title {
    font-size: 16px;
  }
}

@media (max-width: 992px) {
  .shop-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .shop-controls {
    width: 100%;
    margin-top: 15px;
    justify-content: space-between;
  }

  /* Still maintain 3 products per row on medium screens */
  .woocommerce ul.products li.product,
  .woocommerce-page ul.products li.product {
    width: calc(33.333% - 16px) !important;
    margin: 0 8px 16px !important;
  }

  /* Adjust product card for smaller space */
  .product-image {
    height: 180px;
  }

  .product-meta {
    flex-direction: column;
    align-items: flex-start;
  }

  .list-view .product-card {
    flex-direction: column;
  }

  .list-view .product-image {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .shop-controls {
    flex-wrap: wrap;
    gap: 10px;
  }

  .product-count {
    width: 100%;
    order: 3;
    margin-top: 10px;
  }

  /* Try to maintain 3 products per row on tablets */
  .woocommerce ul.products li.product,
  .woocommerce-page ul.products li.product {
    width: calc(33.333% - 12px) !important;
    margin: 0 6px 12px !important;
  }

  /* Simplify product cards */
  .product-image {
    height: 150px;
    padding: 5px;
  }

  .product-info {
    padding: 10px;
  }

  .product-title {
    font-size: 12px;
    margin-bottom: 5px;
  }

  .product-rating,
  .product-meta {
    margin-bottom: 5px;
  }

  .product-description {
    display: none;
  }

  .list-view .product-description {
    display: none;
  }

  .list-view-actions {
    display: flex;
  }
}

/* Small screens (phones) */
@media (max-width: 576px) {

  /* Switch to 2 products per row on phones */
  .woocommerce ul.products li.product,
  .woocommerce-page ul.products li.product {
    width: calc(50% - 10px) !important;
    margin: 0 5px 10px !important;
  }

  .product-meta {
    font-size: 10px;
  }

  .product-price {
    font-size: 16px;
  }

  .woocommerce ul.products li.product .woocommerce-loop-product__title {
    font-size: 14px;
  }

  .woocommerce ul.products li.product .button {
    font-size: 12px;
    padding: 8px 12px;
  }
}

/* Extra small screens */
@media (max-width: 375px) {

  /* Keep 2 products per row even on very small phones */
  .woocommerce ul.products li.product,
  .woocommerce-page ul.products li.product {
    width: calc(50% - 8px) !important;
    margin: 0 4px 8px !important;
  }

  .woocommerce ul.products li.product .woocommerce-loop-product__title {
    font-size: 12px;
  }

  .woocommerce ul.products li.product .price {
    font-size: 12px;
  }

  .woocommerce ul.products li.product .button {
    font-size: 11px;
    padding: 6px 10px;
  }
}

/* Hidden elements in grid view */
.products-grid:not(.list-view) .product-description,
.products-grid:not(.list-view) .list-view-actions {
  display: none;
}

/* Notification Styles */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
}

.notification {
  padding: 15px 20px;
  margin-bottom: 10px;
  border-radius: 5px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  opacity: 1;
}

.notification.show {
  opacity: 1;
}

.notification-success {
  background-color: var(--success-color);
  color: #fff;
}

.notification-error {
  background-color: var(--danger-color);
  color: #fff;
}

.notification-info {
  background-color: var(--primary-color);
  color: #fff;
}