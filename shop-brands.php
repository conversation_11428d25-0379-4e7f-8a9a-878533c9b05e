<?php
/**
 * Template Name: Shop Page with Brands
 *
 * A shop page template with brand focus, product categories, and featured products.
 *
 * @package Teandeal
 */

get_header();
?>

<div class="container shop-container">
  <div class="row">
    <!-- Sidebar with filters -->
    <div class="col-lg-3 shop-sidebar">
      <div class="filter-section">
        <h4 class="filter-title">Brands</h4>
        <div class="brand-filter">
          <?php
          $brands = get_terms(array(
            'taxonomy' => 'product_brand',
            'hide_empty' => true,
            'number' => 10
          ));

          if (!empty($brands) && !is_wp_error($brands)) {
            echo '<ul class="brand-list">';
            foreach ($brands as $brand) {
              echo '<li class="brand-item">';
              echo '<input type="checkbox" id="brand-' . esc_attr($brand->slug) . '" name="brand[]" value="' . esc_attr($brand->slug) . '">';
              echo '<label for="brand-' . esc_attr($brand->slug) . '">' . esc_html($brand->name) . '</label>';
              echo '</li>';
            }
            echo '</ul>';
          }
          ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Price</h4>
        <div class="price-filter">
          <?php echo do_shortcode('[woocommerce_price_filter]'); ?>
        </div>
      </div>

      <div class="filter-section">
        <h4 class="filter-title">Ratings</h4>
        <div class="rating-filter">
          <ul class="rating-list">
            <?php for ($i = 5; $i >= 1; $i--) : ?>
            <li class="rating-item">
              <input type="checkbox" id="rating-<?php echo $i; ?>" name="rating[]" value="<?php echo $i; ?>">
              <label for="rating-<?php echo $i; ?>">
                <?php
                  for ($j = 1; $j <= 5; $j++) {
                    if ($j <= $i) {
                      echo '<i class="bi bi-star-fill"></i>';
                    } else {
                      echo '<i class="bi bi-star"></i>';
                    }
                  }
                  ?>
              </label>
            </li>
            <?php endfor; ?>
          </ul>
        </div>
      </div>
    </div>

    <!-- Main content area -->
    <div class="col-lg-9 shop-main-content">
      <!-- Featured banners -->
      <div class="featured-banners row">
        <div class="col-md-6 banner-item">
          <div class="banner-content">
            <h3>Powerfull Apple watches for you</h3>
            <a href="#" class="btn btn-primary btn-sm">Shop Now</a>
          </div>
          <img src="<?php echo get_template_directory_uri(); ?>/assets/images/apple-watch.jpg" alt="Apple Watch"
            class="banner-image">
        </div>
        <div class="col-md-6 banner-item">
          <div class="banner-content">
            <h3>Discover virtual reality with Xiaomi</h3>
            <p>Discover the latest offers and special deals</p>
            <a href="#" class="btn btn-primary btn-sm">Shop Now</a>
          </div>
          <img src="<?php echo get_template_directory_uri(); ?>/assets/images/vr-headset.jpg" alt="VR Headset"
            class="banner-image">
        </div>
      </div>

      <!-- Brand showcase section -->
      <div class="best-brands-section">
        <div class="section-header">
          <h2 class="section-title">Best brands</h2>
        </div>
        <div class="brands-grid">
          <?php
          // Get top brands
          $top_brands = get_terms(array(
            'taxonomy' => 'product_brand',
            'hide_empty' => true,
            'number' => 4,
            'orderby' => 'count',
            'order' => 'DESC'
          ));

          if (!empty($top_brands) && !is_wp_error($top_brands)) {
            foreach ($top_brands as $brand) {
              $thumbnail_id = get_term_meta($brand->term_id, 'thumbnail_id', true);
              $thumbnail = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : get_template_directory_uri() . '/assets/images/brands/default-brand.png';
              ?>
          <a href="<?php echo get_term_link($brand); ?>" class="brand-item">
            <img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr($brand->name); ?>">
          </a>
          <?php
            }
          } else {
            // Fallback to static brand images if no brands are found
            $default_brands = array(
              'apple' => 'Apple',
              'oppo' => 'Oppo',
              'dell' => 'Dell',
              'asus' => 'Asus'
            );

            foreach ($default_brands as $slug => $name) {
              ?>
          <a href="#" class="brand-item">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/brands/<?php echo $slug; ?>.png"
              alt="<?php echo $name; ?>">
          </a>
          <?php
            }
          }
          ?>
        </div>
      </div>

      <!-- Products section -->
      <div class="category-section">
        <div class="section-header">
          <h2 class="section-title">Products</h2>
        </div>

        <!-- Products grid -->
        <div class="products-grid">
          <?php
          // First try to get products from the Computer & Office category if it exists
          $category_exists = term_exists('computer-office', 'product_cat');

          if ($category_exists) {
            $args = array(
              'post_type' => 'product',
              'posts_per_page' => 6,
              'tax_query' => array(
                array(
                  'taxonomy' => 'product_cat',
                  'field'    => 'slug',
                  'terms'    => 'computer-office',
                  'operator' => 'IN',
                ),
              ),
            );
          } else {
            // If the category doesn't exist, just get any products
            $args = array(
              'post_type' => 'product',
              'posts_per_page' => 6,
            );
          }

          $products = new WP_Query($args);

          if ($products->have_posts()) {
            while ($products->have_posts()) {
              $products->the_post();
              global $product;

              // Make sure we have a valid product object
              if (!is_a($product, 'WC_Product')) {
                continue;
              }
              ?>
          <div class="product-card">
            <div class="product-image">
              <?php if ($product->is_on_sale()) : ?>
              <span class="sale-badge">Sale</span>
              <?php endif; ?>
              <a href="<?php the_permalink(); ?>">
                <?php
                    if (has_post_thumbnail()) {
                      echo woocommerce_get_product_thumbnail();
                    } else {
                      echo '<img src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                    }
                    ?>
              </a>
              <div class="product-actions">
                <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-cart-plus"></i>
                </button>
                <button class="action-btn add-to-wishlist"
                  data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-heart"></i>
                </button>
                <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-eye"></i>
                </button>
              </div>
            </div>
            <div class="product-info">
              <h3 class="product-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
              </h3>
              <div class="product-rating">
                <?php echo wc_get_rating_html($product->get_average_rating()); ?>
                <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
              </div>
              <div class="product-price">
                <?php echo $product->get_price_html(); ?>
              </div>
              <div class="product-meta">
                <?php if ($product->get_stock_status() === 'instock') : ?>
                <span class="in-stock">In Stock</span>
                <?php else : ?>
                <span class="out-of-stock">Out of Stock</span>
                <?php endif; ?>
                <span class="free-shipping">Free Shipping</span>
              </div>
            </div>
          </div>
          <?php
            }
            wp_reset_postdata();
          } else {
            echo '<p>No products found. Please add some products to your WooCommerce store.</p>';
          }
          ?>
        </div>

        <div class="view-more-container">
          <a href="<?php echo get_permalink(wc_get_page_id('shop')); ?>" class="btn btn-outline-primary">View more</a>
        </div>
      </div>

      <!-- Special offers section -->
      <div class="gaming-offers-section">
        <div class="section-header">
          <h2 class="section-title">Special offers for you</h2>
        </div>
        <div class="gaming-offers-grid">
          <?php
          // Get products on sale
          $args = array(
            'post_type' => 'product',
            'posts_per_page' => 3,
            'meta_query' => array(
              array(
                'key' => '_sale_price',
                'value' => 0,
                'compare' => '>',
                'type' => 'NUMERIC'
              )
            )
          );

          // Try to add gaming category if it exists
          $gaming_cat_exists = term_exists('gaming', 'product_cat');
          if ($gaming_cat_exists) {
            $args['tax_query'] = array(
              array(
                'taxonomy' => 'product_cat',
                'field'    => 'slug',
                'terms'    => 'gaming',
                'operator' => 'IN',
              ),
            );
          }

          $sale_products = new WP_Query($args);

          // If no products found with the gaming category, try again without the category filter
          if (!$sale_products->have_posts() && $gaming_cat_exists) {
            $args = array(
              'post_type' => 'product',
              'posts_per_page' => 3,
              'meta_query' => array(
                array(
                  'key' => '_sale_price',
                  'value' => 0,
                  'compare' => '>',
                  'type' => 'NUMERIC'
                )
              )
            );
            $sale_products = new WP_Query($args);
          }

          if ($sale_products->have_posts()) {
            while ($sale_products->have_posts()) {
              $sale_products->the_post();
              global $product;

              // Make sure we have a valid product object
              if (!is_a($product, 'WC_Product')) {
                continue;
              }
              ?>
          <div class="product-card">
            <div class="product-image">
              <?php if ($product->is_on_sale()) : ?>
              <span class="sale-badge">Sale</span>
              <?php endif; ?>
              <a href="<?php the_permalink(); ?>">
                <?php
                if (has_post_thumbnail()) {
                  echo woocommerce_get_product_thumbnail();
                } else {
                  echo '<img src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                }
                ?>
              </a>
              <div class="product-actions">
                <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-cart-plus"></i>
                </button>
                <button class="action-btn add-to-wishlist"
                  data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-heart"></i>
                </button>
                <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-eye"></i>
                </button>
              </div>
            </div>
            <div class="product-info">
              <h3 class="product-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
              </h3>
              <div class="product-rating">
                <?php echo wc_get_rating_html($product->get_average_rating()); ?>
                <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
              </div>
              <div class="product-price">
                <?php echo $product->get_price_html(); ?>
              </div>
              <div class="product-meta">
                <?php if ($product->get_stock_status() === 'instock') : ?>
                <span class="in-stock">In Stock</span>
                <?php else : ?>
                <span class="out-of-stock">Out of Stock</span>
                <?php endif; ?>
                <span class="free-shipping">Free Shipping</span>
              </div>
            </div>
          </div>
          <?php
            }
            wp_reset_postdata();
          } else {
            // If no products on sale, just show regular products
            $args = array(
              'post_type' => 'product',
              'posts_per_page' => 3,
            );
            $regular_products = new WP_Query($args);

            if ($regular_products->have_posts()) {
              while ($regular_products->have_posts()) {
                $regular_products->the_post();
                global $product;

                // Make sure we have a valid product object
                if (!is_a($product, 'WC_Product')) {
                  continue;
                }
                ?>
          <div class="product-card">
            <div class="product-image">
              <a href="<?php the_permalink(); ?>">
                <?php
                      if (has_post_thumbnail()) {
                        echo woocommerce_get_product_thumbnail();
                      } else {
                        echo '<img src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                      }
                      ?>
              </a>
              <div class="product-actions">
                <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-cart-plus"></i>
                </button>
                <button class="action-btn add-to-wishlist"
                  data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-heart"></i>
                </button>
                <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-eye"></i>
                </button>
              </div>
            </div>
            <div class="product-info">
              <h3 class="product-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
              </h3>
              <div class="product-rating">
                <?php echo wc_get_rating_html($product->get_average_rating()); ?>
                <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
              </div>
              <div class="product-price">
                <?php echo $product->get_price_html(); ?>
              </div>
              <div class="product-meta">
                <?php if ($product->get_stock_status() === 'instock') : ?>
                <span class="in-stock">In Stock</span>
                <?php else : ?>
                <span class="out-of-stock">Out of Stock</span>
                <?php endif; ?>
                <span class="free-shipping">Free Shipping</span>
              </div>
            </div>
          </div>
          <?php
              }
              wp_reset_postdata();
            } else {
              echo '<p>No products found to display.</p>';
            }
          }
          ?>
        </div>
      </div>

      <!-- Featured products section -->
      <div class="featured-products-section">
        <div class="section-header">
          <h2 class="section-title">Featured products</h2>
        </div>

        <div class="featured-products-grid">
          <?php
          // Try to get featured products first
          $args = array(
            'post_type' => 'product',
            'posts_per_page' => 3,
            'meta_key' => '_featured',
            'meta_value' => 'yes',
          );
          $featured_products = new WP_Query($args);

          // If no featured products found, just get recent products
          if (!$featured_products->have_posts()) {
            $args = array(
              'post_type' => 'product',
              'posts_per_page' => 3,
              'orderby' => 'date',
              'order' => 'DESC',
            );
            $featured_products = new WP_Query($args);
          }

          if ($featured_products->have_posts()) {
            while ($featured_products->have_posts()) {
              $featured_products->the_post();
              global $product;

              // Make sure we have a valid product object
              if (!is_a($product, 'WC_Product')) {
                continue;
              }
              ?>
          <div class="product-card">
            <div class="product-image">
              <?php if ($product->is_on_sale()) : ?>
              <span class="sale-badge">Sale</span>
              <?php endif; ?>
              <a href="<?php the_permalink(); ?>">
                <?php
                if (has_post_thumbnail()) {
                  echo woocommerce_get_product_thumbnail();
                } else {
                  echo '<img src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                }
                ?>
              </a>
              <div class="product-actions">
                <button class="action-btn add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-cart-plus"></i>
                </button>
                <button class="action-btn add-to-wishlist"
                  data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-heart"></i>
                </button>
                <button class="action-btn quick-view" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                  <i class="bi bi-eye"></i>
                </button>
              </div>
            </div>
            <div class="product-info">
              <h3 class="product-title">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
              </h3>
              <div class="product-rating">
                <?php echo wc_get_rating_html($product->get_average_rating()); ?>
                <span class="rating-count">(<?php echo $product->get_review_count(); ?>)</span>
              </div>
              <div class="product-price">
                <?php echo $product->get_price_html(); ?>
              </div>
              <div class="product-meta">
                <?php if ($product->get_stock_status() === 'instock') : ?>
                <span class="in-stock">In Stock</span>
                <?php else : ?>
                <span class="out-of-stock">Out of Stock</span>
                <?php endif; ?>
                <span class="free-shipping">Free Shipping</span>
              </div>
            </div>
          </div>
          <?php
            }
            wp_reset_postdata();
          } else {
            echo '<p>No products found to display.</p>';
          }
          ?>
        </div>
      </div>
    </div>
  </div>
</div>

<?php
get_footer();
?>