# Copyright (C) 2020 Automattic
# This file is distributed under the GNU General Public License v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: _s 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/theme/_s\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2020-04-17T21:03:15+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.4.0\n"
"X-Domain: _s\n"

#. Theme Name of the theme
msgid "_s"
msgstr ""

#. Theme URI of the theme
msgid "https://underscores.me/"
msgstr ""

#. Description of the theme
msgid "Hi. I'm a starter theme called <code>_s</code>, or <em>underscores</em>, if you like. I'm a theme meant for hacking so don't use me as a <em>Parent Theme</em>. Instead try turning me into the next, most awesome, WordPress theme out there. That's what I'm here for."
msgstr ""

#. Author of the theme
msgid "Automattic"
msgstr ""

#. Author URI of the theme
msgid "https://automattic.com/"
msgstr ""

#: 404.php:18
msgid "Oops! That page can&rsquo;t be found."
msgstr ""

#: 404.php:22
msgid "It looks like nothing was found at this location. Maybe try one of the links below or a search?"
msgstr ""

#: 404.php:31
msgid "Most Used Categories"
msgstr ""

#. translators: %1$s: smiley
#: 404.php:49
msgid "Try looking in the monthly archives. %1$s"
msgstr ""

#. translators: 1: title.
#: comments.php:35
msgid "One thought on &ldquo;%1$s&rdquo;"
msgstr ""

#. translators: 1: comment count number, 2: title.
#: comments.php:41
msgctxt "comments title"
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] ""
msgstr[1] ""

#: comments.php:68
msgid "Comments are closed."
msgstr ""

#: footer.php:18
msgid "https://wordpress.org/"
msgstr ""

#. translators: %s: CMS name, i.e. WordPress.
#: footer.php:21
msgid "Proudly powered by %s"
msgstr ""

#. translators: 1: Theme name, 2: Theme author.
#: footer.php:27
msgid "Theme: %1$s by %2$s."
msgstr ""

#: functions.php:53
msgid "Primary"
msgstr ""

#: functions.php:130
msgid "Sidebar"
msgstr ""

#: functions.php:132
msgid "Add widgets here."
msgstr ""

#: header.php:26
msgid "Skip to content"
msgstr ""

#: header.php:49
msgid "Primary Menu"
msgstr ""

#. translators: %s: post date.
#: inc/template-tags.php:30
msgctxt "post date"
msgid "Posted on %s"
msgstr ""

#. translators: %s: post author.
#: inc/template-tags.php:46
msgctxt "post author"
msgid "by %s"
msgstr ""

#. translators: used between list items, there is a space after the comma
#: inc/template-tags.php:63
msgid ", "
msgstr ""

#. translators: 1: list of categories.
#: inc/template-tags.php:66
msgid "Posted in %1$s"
msgstr ""

#. translators: used between list items, there is a space after the comma
#: inc/template-tags.php:70
msgctxt "list item separator"
msgid ", "
msgstr ""

#. translators: 1: list of tags.
#: inc/template-tags.php:73
msgid "Tagged %1$s"
msgstr ""

#. translators: %s: post title
#: inc/template-tags.php:83
msgid "Leave a Comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr ""

#. translators: %s: Name of current post. Only visible to screen readers
#: inc/template-tags.php:100
#: template-parts/content-page.php:39
msgid "Edit <span class=\"screen-reader-text\">%s</span>"
msgstr ""

#: inc/woocommerce.php:186
msgid "View your shopping cart"
msgstr ""

#. translators: number of items in the mini cart.
#: inc/woocommerce.php:190
msgid "%d item"
msgid_plural "%d items"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: search query.
#: search.php:22
msgid "Search Results for: %s"
msgstr ""

#: single.php:23
msgid "Previous:"
msgstr ""

#: single.php:24
msgid "Next:"
msgstr ""

#: template-parts/content-none.php:14
msgid "Nothing Found"
msgstr ""

#. translators: 1: link to WP admin new post page.
#: template-parts/content-none.php:24
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr ""

#: template-parts/content-none.php:37
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr ""

#: template-parts/content-none.php:44
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr ""

#: template-parts/content-page.php:25
#: template-parts/content.php:53
msgid "Pages:"
msgstr ""

#. translators: %s: Name of current post. Only visible to screen readers
#: template-parts/content.php:40
msgid "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr ""
